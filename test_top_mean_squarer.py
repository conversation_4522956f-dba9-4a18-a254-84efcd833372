#!/usr/bin/env python3
"""
测试linear_regression_optimizer.py中的top_mean_squarer功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from factor.linear_regression_optimizer import train_factor_regression

def test_top_mean_squarer():
    """测试top_mean_squarer优化目标"""
    print("=== 测试top_mean_squarer优化目标 ===\n")
    
    # 测试参数
    train_start = '20240101'
    train_end = '20240630'
    
    # 测试1: 标准线性回归
    print("1. 标准线性回归训练:")
    print("-" * 50)
    model_standard = train_factor_regression(
        train_start=train_start,
        train_end=train_end,
        regularization='ridge',
        alpha=1.0,
        model_name='test_standard',
        factor_config_name='best_five',
        target_factor='next_return',
        return_days=1,
        save_model=False,
        use_top_mean_squarer=False
    )
    
    print("\n" + "="*80 + "\n")
    
    # 测试2: 使用top_mean_squarer优化
    print("2. top_mean_squarer优化训练:")
    print("-" * 50)
    model_top_mse = train_factor_regression(
        train_start=train_start,
        train_end=train_end,
        regularization='ridge',
        alpha=1.0,
        model_name='test_top_mse',
        factor_config_name='best_five',
        target_factor='next_return',
        return_days=1,
        save_model=False,
        use_top_mean_squarer=True
    )
    
    print("\n" + "="*80 + "\n")
    
    # 比较结果
    print("3. 结果比较:")
    print("-" * 50)
    
    if model_standard and model_standard.training_results:
        std_results = model_standard.training_results
        print(f"标准线性回归:")
        print(f"  R2: {std_results.get('r2', 'N/A'):.4f}")
        print(f"  MSE: {std_results.get('mse', 'N/A'):.6f}")
        print(f"  交叉验证R2: {std_results.get('cv_mean', 'N/A'):.4f}")
        
        # 计算标准模型的top_mse
        if 'top_mse' in std_results:
            print(f"  Top MSE (前10%): {std_results['top_mse']:.6f}")
    
    print()
    
    if model_top_mse and model_top_mse.training_results:
        top_results = model_top_mse.training_results
        print(f"top_mean_squarer优化:")
        print(f"  R2: {top_results.get('r2', 'N/A'):.4f}")
        print(f"  MSE: {top_results.get('mse', 'N/A'):.6f}")
        print(f"  Top MSE (前10%): {top_results.get('top_mse', 'N/A'):.6f}")
        print(f"  交叉验证R2: {top_results.get('cv_mean', 'N/A'):.4f}")
        print(f"  优化成功: {top_results.get('optimization_success', 'N/A')}")
    
    print("\n" + "="*80 + "\n")
    
    # 分析差异
    if (model_standard and model_standard.training_results and 
        model_top_mse and model_top_mse.training_results):
        
        std_r2 = std_results.get('r2', 0)
        top_r2 = top_results.get('r2', 0)
        std_mse = std_results.get('mse', 0)
        top_mse_val = top_results.get('mse', 0)
        std_top_mse = std_results.get('top_mse', 0)
        top_top_mse = top_results.get('top_mse', 0)
        
        print("4. 性能差异分析:")
        print("-" * 50)
        print(f"R2变化: {top_r2 - std_r2:+.4f}")
        print(f"MSE变化: {top_mse_val - std_mse:+.6f}")
        if std_top_mse > 0 and top_top_mse > 0:
            print(f"Top MSE变化: {top_top_mse - std_top_mse:+.6f}")
            print(f"Top MSE改善: {((std_top_mse - top_top_mse) / std_top_mse * 100):+.2f}%")
        
        print("\n说明:")
        print("- top_mean_squarer优化专注于提升预测值前10%样本的准确性")
        print("- 这种方法适合于选股策略，因为我们主要关心高预测值的准确性")
        print("- Top MSE的改善表明模型在识别高收益样本方面的能力")

if __name__ == "__main__":
    test_top_mean_squarer()
