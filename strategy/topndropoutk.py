"""
策略模块，实现topndropoutk策略和特殊事件处理
"""
import numpy as np
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time

# 导入配置管理器
from config.config import config_manager

class TopNDropoutKStrategy:
    """TopNDropoutK策略类，实现TopNDropoutK策略的核心逻辑"""
    
    def __init__(self, n=10, k=2, d=40):
        """
        初始化TopNDropoutK策略 - 优化版
        
        Args:
            n: 持有的可转债数量，默认为10（最优参数）
            k: 每日最多换出的可转债数量，默认为2（最优参数）
            d: 保护排名阈值，排名在d之前的转债即便掉出前n也不会被换出，默认为40
        
        注意：N=10, K=2 为经过优化测试的最佳参数组合
        """
        # 设置策略参数
        self.n = n
        self.k = k
        self.d = d
        
        # 设置数据库路径
        self.db_path = config_manager.get_db_path()
        
        # 导入数据缓存器
        from data.fetcher import DataCache
        self.data_cache = DataCache()
        
        # 初始化持仓
        self.holdings = []
    
    def select_bonds(self, trade_date, factor_name='combined_factor', ascending=False):
        """
        根据因子选择可转债
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            选择的可转债列表，格式为[{'ts_code': code, 'weight': weight}, ...]
        """
        # 转换日期格式
        date_str = trade_date.replace('-', '')
        
        # 获取因子数据
        df_factor = self.data_cache.get_cb_factor(trade_date=date_str)
        
        # 如果数据为空，返回空列表
        if df_factor.empty:
            print(f"未找到{trade_date}的因子数据")
            return []
        
        # 检查因子是否存在
        if factor_name not in df_factor.columns:
            print(f"因子{factor_name}不存在")
            return []
        
        # 排序
        df_sorted = df_factor.sort_values(factor_name, ascending=ascending)
        
        # 选择前N个
        selected = df_sorted.head(self.n)
        
        # 构建结果
        result = []
        for i, row in selected.iterrows():
            result.append({
                'ts_code': row['ts_code'],
                'weight': 1.0 / self.n  # 等权重
            })
        
        return result
    
    def update_holdings(self, trade_date, factor_name='combined_factor', ascending=False):
        """
        更新持仓
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            更新后的持仓列表，格式为[{'ts_code': code, 'weight': weight}, ...]
        """
        # 如果持仓为空，直接选择N个
        if not self.holdings:
            self.holdings = self.select_bonds(trade_date, factor_name, ascending)
            return self.holdings
        
        # 获取当前最优的N个可转债
        optimal = self.select_bonds(trade_date, factor_name, ascending)
        
        # 如果最优列表为空，保持当前持仓不变
        if not optimal:
            return self.holdings
        
        # 获取当前持仓的代码列表
        current_codes = [h['ts_code'] for h in self.holdings]
        
        # 获取最优的代码列表
        optimal_codes = [o['ts_code'] for o in optimal]
        
        # 获取因子数据用于排名判断
        df_factor = self.data_cache.get_cb_factor(trade_date=trade_date.replace('-', ''))
        
        if df_factor.empty or factor_name not in df_factor.columns:
            return self.holdings
        
        # 按因子值排序，得到完整排名
        df_sorted = df_factor.sort_values(factor_name, ascending=ascending).reset_index(drop=True)
        
        # 创建排名映射（使用正确的索引）
        rank_map = {}
        for idx, row in df_sorted.iterrows():
            rank_map[row['ts_code']] = idx + 1
        
        # 找出需要卖出的可转债（在当前持仓中但不在最优列表中）
        to_sell_candidates = [code for code in current_codes if code not in optimal_codes]
        
        # 根据d参数过滤卖出候选：只有排名低于d的才会被卖出
        to_sell = []
        for code in to_sell_candidates:
            if code in rank_map and rank_map[code] > self.d:
                to_sell.append(code)
        
        # 找出需要买入的可转债（在最优列表中但不在当前持仓中）
        to_buy = [code for code in optimal_codes if code not in current_codes]
        
        # 限制每日最多换出K个
        if len(to_sell) > self.k:
            # 按因子值排序，选择表现最差的K个债券进行卖出
            df_to_sell = df_factor[df_factor['ts_code'].isin(to_sell)]
            # 无论升序还是降序，都选择因子值最小的（表现最差的）
            df_to_sell = df_to_sell.sort_values(factor_name, ascending=True)
            to_sell = df_to_sell.head(self.k)['ts_code'].tolist()
        
        # 更新持仓
        # 1. 移除需要卖出的可转债
        self.holdings = [h for h in self.holdings if h['ts_code'] not in to_sell]
        
        # 2. 添加需要买入的可转债（最多K个）
        to_buy = to_buy[:self.k]
        for code in to_buy:
            self.holdings.append({
                'ts_code': code,
                'weight': 1.0 / self.n  # 等权重
            })
        
        # 3. 重新计算权重
        for h in self.holdings:
            h['weight'] = 1.0 / len(self.holdings)
        
        return self.holdings
    
    def handle_special_events(self, trade_date):
        """
        处理特殊事件（强赎、下修等）
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
        
        Returns:
            处理后的持仓列表，格式为[{'ts_code': code, 'weight': weight}, ...]
        """
        # 如果持仓为空，直接返回
        if not self.holdings:
            return self.holdings
        
        # 获取当前持仓的代码列表
        current_codes = [h['ts_code'] for h in self.holdings]
        
        # 检查是否有强赎事件
        event_handler = EventHandler()
        call_events = event_handler.check_call_events(current_codes, trade_date)
        
        # 如果有强赎事件，移除对应的可转债
        if call_events:
            for event in call_events:
                self.holdings = [h for h in self.holdings if h['ts_code'] != event['ts_code']]
            
            # 重新计算权重
            if self.holdings:
                for h in self.holdings:
                    h['weight'] = 1.0 / len(self.holdings)
        
        return self.holdings
    
    def generate_signals(self, trade_date, factor_name='combined_factor', ascending=False):
        """
        生成交易信号
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            交易信号列表，格式为[{'ts_code': code, 'signal': signal, 'weight': weight}, ...]
            其中signal为1表示买入，-1表示卖出，0表示持有
        """
        # 获取当前持仓的代码列表
        current_codes = [h['ts_code'] for h in self.holdings]
        
        # 处理特殊事件
        self.handle_special_events(trade_date)
        
        # 更新后的持仓代码列表
        updated_codes = [h['ts_code'] for h in self.holdings]
        
        # 更新持仓
        new_holdings = self.update_holdings(trade_date, factor_name, ascending)
        
        # 新的持仓代码列表
        new_codes = [h['ts_code'] for h in new_holdings]
        
        # 生成信号
        signals = []
        
        # 1. 处理卖出信号
        for code in current_codes:
            if code not in new_codes:
                signals.append({
                    'ts_code': code,
                    'signal': -1,  # 卖出
                    'weight': 0
                })
        
        # 2. 处理买入信号
        for h in new_holdings:
            code = h['ts_code']
            if code not in current_codes:
                signals.append({
                    'ts_code': code,
                    'signal': 1,  # 买入
                    'weight': h['weight']
                })
            else:
                signals.append({
                    'ts_code': code,
                    'signal': 0,  # 持有
                    'weight': h['weight']
                })
        
        return signals


class EventHandler:
    """事件处理类，负责处理特殊事件（强赎、下修等）"""
    
    def __init__(self):
        """初始化事件处理器"""
        # 设置数据库路径
        self.db_path = config_manager.get_db_path()
    
    def check_call_events(self, ts_codes, trade_date):
        """
        检查是否有强赎事件
        
        Args:
            ts_codes: 可转债代码列表
            trade_date: 交易日期，格式YYYY-MM-DD
        
        Returns:
            强赎事件列表，格式为[{'ts_code': code, 'call_date': date, 'call_price': price}, ...]
        """
        # 如果代码列表为空，直接返回空列表
        if not ts_codes:
            return []
        
        # 转换日期格式
        date_str = trade_date.replace('-', '')
        
        # 构建SQL查询
        placeholders = ', '.join(['?' for _ in ts_codes])
        sql = f"""
        SELECT ts_code, call_date, call_price, call_type, call_reason
        FROM cb_call
        WHERE ts_code IN ({placeholders})
        AND call_date <= '{date_str}'
        """
        
        # 执行查询
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(sql, ts_codes)
        rows = cursor.fetchall()
        conn.close()
        
        # 构建结果
        result = []
        for row in rows:
            result.append({
                'ts_code': row[0],
                'call_date': row[1],
                'call_price': row[2],
                'call_type': row[3],
                'call_reason': row[4]
            })
        
        return result
    
    def check_put_events(self, ts_codes, trade_date):
        """
        检查是否有回售事件

        注意：回售功能已被移除，因为缺乏可靠的数据源

        Args:
            ts_codes: 可转债代码列表
            trade_date: 交易日期，格式YYYY-MM-DD

        Returns:
            空列表（回售功能已移除）
        """
        # 回售功能已移除，直接返回空列表
        return []
    
    def check_price_adjustment_events(self, ts_codes, trade_date):
        """
        检查是否有转股价调整事件
        
        Args:
            ts_codes: 可转债代码列表
            trade_date: 交易日期，格式YYYY-MM-DD
        
        Returns:
            转股价调整事件列表，格式为[{'ts_code': code, 'change_date': date, 'convert_price_before': price_before, 'convert_price_after': price_after}, ...]
        """
        # 如果代码列表为空，直接返回空列表
        if not ts_codes:
            return []
        
        # 转换日期格式
        date_str = trade_date.replace('-', '')
        
        # 构建SQL查询
        placeholders = ', '.join(['?' for _ in ts_codes])
        sql = f"""
        SELECT ts_code, change_date, convertprice_bef, convertprice_aft, publish_date
        FROM cb_price_chg
        WHERE ts_code IN ({placeholders})
        AND change_date <= '{date_str}'
        """

        # 执行查询
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(sql, ts_codes)
        rows = cursor.fetchall()
        conn.close()

        # 构建结果
        result = []
        for row in rows:
            result.append({
                'ts_code': row[0],
                'change_date': row[1],
                'convert_price_before': row[2],
                'convert_price_after': row[3],
                'publish_date': row[4]
            })
        
        return result
    
    def check_downward_adjustment_condition(self, ts_code, trade_date):
        """
        检查是否满足下修条件（连续30个交易日收盘价格低于当期转股价格的80%）
        
        Args:
            ts_code: 可转债代码
            trade_date: 交易日期，格式YYYY-MM-DD
        
        Returns:
            是否满足下修条件
        """
        # 转换日期格式
        date_str = trade_date.replace('-', '')
        
        # 获取可转债基本信息
        conn = sqlite3.connect(self.db_path)
        query = f"""
        SELECT conv_price
        FROM cb_basic
        WHERE ts_code = '{ts_code}'
        """
        df_basic = pd.read_sql(query, conn)
        
        # 如果数据为空，返回False
        if df_basic.empty:
            conn.close()
            return False
        
        # 获取转股价
        conv_price = df_basic.iloc[0]['conv_price']
        
        # 获取正股代码
        query = f"""
        SELECT stk_code
        FROM cb_basic
        WHERE ts_code = '{ts_code}'
        """
        df_stk = pd.read_sql(query, conn)
        
        # 如果数据为空，返回False
        if df_stk.empty:
            conn.close()
            return False
        
        # 获取正股代码
        stk_code = df_stk.iloc[0]['stk_code']
        stk_ts_code = f"{stk_code}.SH" if stk_code.startswith('6') else f"{stk_code}.SZ"
        
        # 获取过去30个交易日的正股价格
        query = f"""
        SELECT trade_date, close
        FROM stock_daily
        WHERE ts_code = '{stk_ts_code}'
        AND trade_date <= '{date_str}'
        ORDER BY trade_date DESC
        LIMIT 30
        """
        df_stock = pd.read_sql(query, conn)
        conn.close()
        
        # 如果数据不足30天，返回False
        if len(df_stock) < 30:
            return False
        
        # 计算下修阈值
        threshold = conv_price * 0.8
        
        # 检查是否满足下修条件
        days_below_threshold = (df_stock['close'] < threshold).sum()
        
        return days_below_threshold == 30
    
    def check_call_condition(self, ts_code, trade_date):
        """
        检查是否满足强赎条件（连续30个交易日中至少15个交易日收盘价格不低于当期转股价格的130%）
        
        Args:
            ts_code: 可转债代码
            trade_date: 交易日期，格式YYYY-MM-DD
        
        Returns:
            是否满足强赎条件
        """
        # 转换日期格式
        date_str = trade_date.replace('-', '')
        
        # 获取可转债基本信息
        conn = sqlite3.connect(self.db_path)
        query = f"""
        SELECT conv_price
        FROM cb_basic
        WHERE ts_code = '{ts_code}'
        """
        df_basic = pd.read_sql(query, conn)
        
        # 如果数据为空，返回False
        if df_basic.empty:
            conn.close()
            return False
        
        # 获取转股价
        conv_price = df_basic.iloc[0]['conv_price']
        
        # 获取正股代码
        query = f"""
        SELECT stk_code
        FROM cb_basic
        WHERE ts_code = '{ts_code}'
        """
        df_stk = pd.read_sql(query, conn)
        
        # 如果数据为空，返回False
        if df_stk.empty:
            conn.close()
            return False
        
        # 获取正股代码
        stk_code = df_stk.iloc[0]['stk_code']
        stk_ts_code = f"{stk_code}.SH" if stk_code.startswith('6') else f"{stk_code}.SZ"
        
        # 获取过去30个交易日的正股价格
        query = f"""
        SELECT trade_date, close
        FROM stock_daily
        WHERE ts_code = '{stk_ts_code}'
        AND trade_date <= '{date_str}'
        ORDER BY trade_date DESC
        LIMIT 30
        """
        df_stock = pd.read_sql(query, conn)
        conn.close()
        
        # 如果数据不足30天，返回False
        if len(df_stock) < 30:
            return False
        
        # 计算强赎阈值
        threshold = conv_price * 1.3
        
        # 检查是否满足强赎条件
        days_above_threshold = (df_stock['close'] >= threshold).sum()
        
        return days_above_threshold >= 15


class SignalGenerator:
    """信号生成类，负责生成交易信号"""
    
    def __init__(self, strategy=None):
        """
        初始化信号生成器
        
        Args:
            strategy: 策略对象，默认为None，表示使用TopNDropoutK策略
        """
        # 设置策略
        if strategy is None:
            self.strategy = TopNDropoutKStrategy(
                n=config_manager.get_default_n(),
                k=config_manager.get_default_k(),
                d=40  # 默认d值
            )
        else:
            self.strategy = strategy
    
    def generate_signals(self, trade_date, factor_name='combined_factor', ascending=False):
        """
        生成交易信号
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            交易信号DataFrame
        """
        # 生成信号
        signals = self.strategy.generate_signals(trade_date, factor_name, ascending)
        
        # 转换为DataFrame
        df_signals = pd.DataFrame(signals)
        
        return df_signals
    
    def generate_signals_from_data(self, factor_data, factor_name='combined_factor', ascending=False):
        """
        直接从因子数据生成交易信号（用于多进程）
        
        Args:
            factor_data: 因子数据DataFrame
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            交易信号DataFrame
        """
        if factor_data.empty or factor_name not in factor_data.columns:
            return pd.DataFrame()
        
        try:
            # 排序选择前N个
            df_sorted = factor_data.sort_values(factor_name, ascending=ascending)
            selected = df_sorted.head(self.strategy.n)
            
            # 构建信号
            signals = []
            for _, row in selected.iterrows():
                signals.append({
                    'ts_code': row['ts_code'],
                    'signal': 1,  # 买入信号
                    'weight': 1.0 / self.strategy.n  # 等权重
                })
            
            return pd.DataFrame(signals)
        except Exception as e:
            print(f"生成信号失败: {e}")
            return pd.DataFrame()


class ParameterOptimizer:
    """参数优化类，负责优化策略参数"""
    
    def __init__(self, backtest_func):
        """
        初始化参数优化器
        
        Args:
            backtest_func: 回测函数，接受策略参数，返回回测结果
        """
        self.backtest_func = backtest_func
    
    def grid_search(self, param_grid):
        """
        网格搜索优化参数
        
        Args:
            param_grid: 参数网格，格式为{'param_name': [value1, value2, ...], ...}
        
        Returns:
            最优参数和对应的回测结果
        """
        # 生成参数组合
        param_combinations = self._generate_param_combinations(param_grid)
        
        # 存储结果
        results = []
        
        # 遍历参数组合
        for params in param_combinations:
            # 回测
            result = self.backtest_func(params)
            
            # 存储结果
            results.append({
                'params': params,
                'result': result
            })
        
        # 按回测结果排序
        results.sort(key=lambda x: x['result']['performance'], reverse=True)
        
        # 返回最优参数和对应的回测结果
        return results[0]['params'], results[0]['result']
    
    def _generate_param_combinations(self, param_grid):
        """
        生成参数组合
        
        Args:
            param_grid: 参数网格，格式为{'param_name': [value1, value2, ...], ...}
        
        Returns:
            参数组合列表
        """
        # 参数名列表
        param_names = list(param_grid.keys())
        
        # 参数值列表
        param_values = list(param_grid.values())
        
        # 生成参数组合
        combinations = []
        
        # 递归生成
        def generate(index, current):
            if index == len(param_names):
                combinations.append(current.copy())
                return
            
            for value in param_values[index]:
                current[param_names[index]] = value
                generate(index + 1, current)
        
        generate(0, {})
        
        return combinations
