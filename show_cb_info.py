#!/usr/bin/env python3
"""
可转债信息交互式展示工具
显示可转债价格、正股价格，并支持键盘交互查看每日的转股溢价率和纯债溢价率
增加ZL模型套利空间和动态套利空间显示
"""

import argparse
import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
# from datetime import datetime  # 暂时不需要
import warnings
import tkinter as tk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

# 导入配置管理器
from config.config import config_manager

# ZL模型数据将从数据库读取，无需导入计算器

# 设置中文字体
from utils.font_utils import setup_chinese_font
setup_chinese_font()

# 抑制警告
warnings.filterwarnings('ignore')


class CBInfoViewer:
    """可转债信息交互式查看器"""
    
    def __init__(self, cb_code, start_date, end_date):
        """
        初始化查看器

        Args:
            cb_code: 可转债代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        self.cb_code = cb_code
        self.start_date = start_date.replace('-', '')
        self.end_date = end_date.replace('-', '')
        self.db_path = config_manager.get_db_path()

        # 数据存储
        self.cb_data = None
        self.stock_data = None
        self.merged_data = None

        # 图表相关
        self.root = None
        self.fig = None
        self.canvas = None
        self.ax1 = None
        self.ax2 = None
        self.ax3 = None
        self.ax4 = None
        self.ax5 = None  # 新增：套利空间图表
        self.cursor_line = None
        self.current_index = 0
        self.info_text = None

        # ZL模型数据可用性标志（从数据库读取，不需要初始化计算器）
        self.zl_model_available = True
        print("✅ ZL模型数据读取模式已启用")

        # 加载数据
        self.load_data()
        
    def load_data(self):
        """加载可转债和正股数据"""
        print(f"正在加载 {self.cb_code} 的数据...")
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取可转债基本信息
            basic_query = """
            SELECT ts_code, bond_short_name, stk_code, conv_price, par
            FROM cb_basic 
            WHERE ts_code = ?
            """
            basic_info = pd.read_sql(basic_query, conn, params=[self.cb_code])
            
            if basic_info.empty:
                raise ValueError(f"未找到可转债 {self.cb_code} 的基本信息")
            
            self.bond_name = basic_info.iloc[0]['bond_short_name']
            self.stock_code = basic_info.iloc[0]['stk_code']
            self.conv_price = basic_info.iloc[0]['conv_price']
            self.par_value = basic_info.iloc[0]['par'] or 100  # 面值，默认100
            
            print(f"债券名称: {self.bond_name}")
            print(f"正股代码: {self.stock_code}")
            print(f"转股价: {self.conv_price}")
            
            # 获取可转债日行情数据（包含全价和应计利息）
            cb_query = """
            SELECT trade_date, close, vol, amount, accrued_interest, full_price
            FROM cb_daily
            WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """
            self.cb_data = pd.read_sql(cb_query, conn, params=[self.cb_code, self.start_date, self.end_date])

            # 处理全价数据：使用前向填充处理空值和无效值
            # 先将无效值（<=0）设为NaN
            self.cb_data.loc[self.cb_data['full_price'] <= 0, 'full_price'] = np.nan
            # 使用线性插值和前向填充处理NaN值
            self.cb_data['full_price'] = self.cb_data['full_price'].interpolate(method='linear', limit_direction='both')
            self.cb_data['full_price'] = self.cb_data['full_price'].fillna(method='ffill')
            # 如果前向填充后仍有NaN（通常是开头的数据），使用后向填充
            self.cb_data['full_price'] = self.cb_data['full_price'].fillna(method='bfill')
            
            # 处理应计利息：如果为空，设为0
            self.cb_data['accrued_interest'] = self.cb_data['accrued_interest'].fillna(0.0)
            
            print("✅ 已加载收盘价和全价数据")
            
            if self.cb_data.empty:
                raise ValueError(f"未找到可转债 {self.cb_code} 在指定时间段的行情数据")
            
            # 获取正股代码（检查是否已包含交易所后缀）
            if '.' in self.stock_code:
                stock_ts_code = self.stock_code  # 已包含交易所后缀
            else:
                stock_ts_code = f"{self.stock_code}.SH" if self.stock_code.startswith('6') else f"{self.stock_code}.SZ"
            
            # 获取正股日行情数据
            stock_query = """
            SELECT trade_date, close, vol, amount
            FROM stock_daily 
            WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """
            self.stock_data = pd.read_sql(stock_query, conn, params=[stock_ts_code, self.start_date, self.end_date])
            
            if self.stock_data.empty:
                raise ValueError(f"未找到正股 {stock_ts_code} 在指定时间段的行情数据")
            
            # 合并数据
            self.merge_data()
            
            print(f"成功加载数据，共 {len(self.merged_data)} 个交易日")
            
        finally:
            conn.close()
    
    def merge_data(self):
        """合并可转债和正股数据，计算溢价率和套利空间"""
        # 合并数据
        self.merged_data = pd.merge(
            self.cb_data,
            self.stock_data,
            on='trade_date',
            how='inner',
            suffixes=('_cb', '_stock')
        )

        # 确保全价字段正确命名（如果没有重命名则手动重命名）
        if 'full_price' in self.merged_data.columns and 'full_price_cb' not in self.merged_data.columns:
            self.merged_data['full_price_cb'] = self.merged_data['full_price']
        
        # 确保应计利息字段正确命名
        if 'accrued_interest' in self.merged_data.columns and 'accrued_interest_cb' not in self.merged_data.columns:
            self.merged_data['accrued_interest_cb'] = self.merged_data['accrued_interest']

        # 转换日期格式
        self.merged_data['date'] = pd.to_datetime(self.merged_data['trade_date'], format='%Y%m%d')

        # 计算转股价值 = 正股价格 * 100 / 转股价
        self.merged_data['conversion_value'] = self.merged_data['close_stock'] * 100 / self.conv_price

        # 计算转股溢价率（基于收盘价）
        self.merged_data['premium_ratio'] = (self.merged_data['close_cb'] / self.merged_data['conversion_value']) - 1

        # 计算纯债溢价率（简化计算，假设无风险利率3%，剩余期限2年）
        # 纯债价值 = 面值 * exp(-r * T)，这里简化为固定值
        risk_free_rate = 0.03
        time_to_maturity = 2.0  # 假设剩余期限2年
        bond_floor = self.par_value * np.exp(-risk_free_rate * time_to_maturity)

        # 纯债溢价率（基于收盘价）
        self.merged_data['bond_premium_ratio'] = (self.merged_data['close_cb'] / bond_floor) - 1

        # 处理异常值
        self.merged_data['premium_ratio'] = self.merged_data['premium_ratio'].clip(-0.5, 2.0)
        self.merged_data['bond_premium_ratio'] = self.merged_data['bond_premium_ratio'].clip(-0.5, 2.0)

        # 计算ZL模型套利空间
        self.calculate_arbitrage_space()

    def calculate_arbitrage_space(self):
        """从数据库读取ZL模型套利空间和动态套利空间"""
        print("正在从数据库读取ZL模型套利空间数据...")

        try:
            conn = sqlite3.connect(self.db_path)

            # 从cb_factor表读取已计算的因子数据
            factor_query = """
            SELECT trade_date, zl_price, arbitrage_space
            FROM cb_factor
            WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """

            factor_data = pd.read_sql(
                factor_query,
                conn,
                params=[self.cb_code, self.start_date, self.end_date]
            )

            conn.close()

            if factor_data.empty:
                print(f"❌ 数据库中没有找到 {self.cb_code} 在指定时间段的因子数据")
                print("💡 提示：请先运行因子计算：python main.py --mode factor --precision high")
                self.merged_data['theoretical_price'] = np.nan
                self.merged_data['arbitrage_space'] = np.nan
                self.merged_data['dynamic_arbitrage_space'] = np.nan
                self.zl_model_available = False
                return

            # 将因子数据合并到主数据中
            factor_data['trade_date'] = factor_data['trade_date'].astype(str)
            self.merged_data['trade_date_str'] = self.merged_data['trade_date'].astype(str)

            # 合并数据
            merged_with_factors = pd.merge(
                self.merged_data,
                factor_data,
                left_on='trade_date_str',
                right_on='trade_date',
                how='left',
                suffixes=('', '_factor')
            )

            # 更新数据
            self.merged_data['theoretical_price'] = merged_with_factors['zl_price']
            self.merged_data['arbitrage_space'] = merged_with_factors['arbitrage_space']

            # 计算动态套利空间（当前值 - 20日均值）
            self.merged_data['arbitrage_space_ma20'] = self.merged_data['arbitrage_space'].rolling(window=20, min_periods=5).mean()
            self.merged_data['dynamic_arbitrage_space'] = self.merged_data['arbitrage_space'] - self.merged_data['arbitrage_space_ma20']

            # 统计信息
            valid_count = self.merged_data['arbitrage_space'].notna().sum()
            print(f"✅ 成功读取 {valid_count}/{len(self.merged_data)} 个交易日的套利空间数据")

            if valid_count > 0:
                avg_arbitrage = self.merged_data['arbitrage_space'].mean()
                avg_dynamic = self.merged_data['dynamic_arbitrage_space'].mean()
                print(f"📊 平均套利空间: {avg_arbitrage:.2f}")
                print(f"📊 平均动态套利空间: {avg_dynamic:.2f}")
            else:
                print("⚠️ 没有有效的套利空间数据")
                self.zl_model_available = False

        except Exception as e:
            print(f"❌ 读取套利空间数据失败: {e}")
            self.merged_data['theoretical_price'] = np.nan
            self.merged_data['arbitrage_space'] = np.nan
            self.merged_data['dynamic_arbitrage_space'] = np.nan
            self.zl_model_available = False

    def create_plot(self):
        """创建带滚动条的交互式图表"""
        # 创建tkinter窗口
        self.root = tk.Tk()
        self.root.title(f'{self.bond_name} ({self.cb_code}) 价格走势与溢价率分析')
        self.root.geometry('1200x900')

        # 创建主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布框架（用于滚动）
        canvas_frame = tk.Frame(main_frame)
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建滚动条
        scrollbar = tk.Scrollbar(main_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建可滚动的画布
        scroll_canvas = tk.Canvas(canvas_frame, yscrollcommand=scrollbar.set)
        scroll_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=scroll_canvas.yview)

        # 创建内部框架（包含所有图表）
        inner_frame = tk.Frame(scroll_canvas)
        scroll_canvas.create_window((0, 0), window=inner_frame, anchor="nw")

        # 创建matplotlib图表（增加套利空间图表）
        self.fig, (self.ax1, self.ax2, self.ax3, self.ax4, self.ax5) = plt.subplots(5, 1, figsize=(12, 20))
        self.fig.suptitle(f'{self.bond_name} ({self.cb_code}) 价格走势与溢价率分析', fontsize=14, fontweight='bold')

        # 绘制可转债价格走势（收盘价和全价）
        self.ax1.plot(self.merged_data['date'], self.merged_data['close_cb'], 'b-', linewidth=2, label='收盘价(净价)')
        
        # 检查全价数据是否存在，如果不存在则使用收盘价作为备用
        if 'full_price_cb' in self.merged_data.columns:
            full_price_data = self.merged_data['full_price_cb']
            full_price_label = '全价'
        else:
            full_price_data = self.merged_data['close_cb']
            full_price_label = '全价(备用净价)'
        
        self.ax1.plot(self.merged_data['date'], full_price_data, 'r--', linewidth=2, label=full_price_label)
        self.ax1.set_ylabel('可转债价格 (元)', fontsize=10)
        self.ax1.grid(True, alpha=0.3)
        self.ax1.legend()
        self.ax1.set_title('可转债价格走势 (净价 vs 全价)', fontsize=11)

        # 绘制正股价格
        self.ax2.plot(self.merged_data['date'], self.merged_data['close_stock'], 'purple', linewidth=2, label='正股价格')
        self.ax2.set_ylabel('正股价格 (元)', fontsize=10)
        self.ax2.grid(True, alpha=0.3)
        self.ax2.legend()
        self.ax2.set_title('正股价格走势', fontsize=11)

        # 绘制ZL模型套利空间（移动到第三个位置）
        if self.zl_model_available and 'arbitrage_space' in self.merged_data.columns:
            # 过滤有效数据
            valid_data = self.merged_data.dropna(subset=['arbitrage_space'])
            if not valid_data.empty:
                self.ax3.plot(valid_data['date'], valid_data['arbitrage_space'], 'red', linewidth=2, label='套利空间')

                # 如果有动态套利空间数据，也显示
                if 'dynamic_arbitrage_space' in valid_data.columns:
                    valid_dynamic = valid_data.dropna(subset=['dynamic_arbitrage_space'])
                    if not valid_dynamic.empty:
                        self.ax3.plot(valid_dynamic['date'], valid_dynamic['dynamic_arbitrage_space'], 'blue', linewidth=2, label='动态套利空间')

                # 添加零线
                self.ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)

                self.ax3.set_ylabel('套利空间 (元)', fontsize=10)
                self.ax3.grid(True, alpha=0.3)
                self.ax3.legend()
                self.ax3.set_title('ZL模型套利空间分析', fontsize=11)
            else:
                # 如果没有有效数据，显示提示
                self.ax3.text(0.5, 0.5, 'ZL模型数据不可用', transform=self.ax3.transAxes,
                             ha='center', va='center', fontsize=12, color='red')
                self.ax3.set_title('ZL模型套利空间分析（数据不可用）', fontsize=11)
        else:
            # ZL模型不可用时的提示
            self.ax3.text(0.5, 0.5, 'ZL模型未初始化', transform=self.ax3.transAxes,
                         ha='center', va='center', fontsize=12, color='red')
            self.ax3.set_title('ZL模型套利空间分析（模型不可用）', fontsize=11)

        # 绘制转股溢价率（移动到第四个位置）
        self.ax4.plot(self.merged_data['date'], self.merged_data['premium_ratio'] * 100, 'g-', linewidth=2, label='转股溢价率')
        self.ax4.set_ylabel('转股溢价率 (%)', fontsize=10)
        self.ax4.grid(True, alpha=0.3)
        self.ax4.legend()
        self.ax4.set_title('转股溢价率', fontsize=11)

        # 绘制纯债溢价率（移动到第五个位置）
        self.ax5.plot(self.merged_data['date'], self.merged_data['bond_premium_ratio'] * 100, 'cyan', linewidth=2, label='纯债溢价率')
        self.ax5.set_ylabel('纯债溢价率 (%)', fontsize=10)
        self.ax5.set_xlabel('日期', fontsize=10)
        self.ax5.grid(True, alpha=0.3)
        self.ax5.legend()
        self.ax5.set_title('纯债溢价率', fontsize=11)

        # 设置日期格式
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4, self.ax5]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 添加游标线
        self.cursor_line = []
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4, self.ax5]:
            line = ax.axvline(x=self.merged_data['date'].iloc[0], color='red', linestyle='--', alpha=0.7)
            self.cursor_line.append(line)

        plt.tight_layout()

        # 将matplotlib图表嵌入tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, inner_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # 添加工具栏
        toolbar = NavigationToolbar2Tk(self.canvas, inner_frame)
        toolbar.update()

        # 更新滚动区域
        inner_frame.update_idletasks()
        scroll_canvas.configure(scrollregion=scroll_canvas.bbox("all"))

        # 添加信息文本框（在tkinter窗口底部）
        info_frame = tk.Frame(self.root)
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)

        self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, font=('Arial', 10))
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加信息文本滚动条
        info_scrollbar = tk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.config(yscrollcommand=info_scrollbar.set)

        # 更新初始信息
        self.update_info()

        # 绑定键盘事件
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()  # 确保窗口可以接收键盘事件
    
    def on_key_press(self, event):
        """处理键盘事件"""
        key = event.keysym.lower()

        if key in ['right', 'd']:
            # 向右移动
            if self.current_index < len(self.merged_data) - 1:
                self.current_index += 1
                self.update_cursor()
        elif key in ['left', 'a']:
            # 向左移动
            if self.current_index > 0:
                self.current_index -= 1
                self.update_cursor()
        elif key == 'home':
            # 移动到开始
            self.current_index = 0
            self.update_cursor()
        elif key == 'end':
            # 移动到结束
            self.current_index = len(self.merged_data) - 1
            self.update_cursor()
        elif key in ['up', 'w']:
            # 向前跳跃10天
            if self.current_index < len(self.merged_data) - 10:
                self.current_index += 10
            else:
                self.current_index = len(self.merged_data) - 1
            self.update_cursor()
        elif key in ['down', 's']:
            # 向后跳跃10天
            if self.current_index >= 10:
                self.current_index -= 10
            else:
                self.current_index = 0
            self.update_cursor()
    
    def update_cursor(self):
        """更新游标位置"""
        current_date = self.merged_data['date'].iloc[self.current_index]

        # 更新游标线位置
        for line in self.cursor_line:
            line.set_xdata([current_date, current_date])

        # 更新信息
        self.update_info()

        # 刷新图表
        self.canvas.draw()
    
    def update_info(self):
        """更新信息文本"""
        row = self.merged_data.iloc[self.current_index]

        # 计算一些额外的有用信息
        cb_volume = row['vol_cb'] if row['vol_cb'] > 0 else 0
        stock_volume = row['vol_stock'] if row['vol_stock'] > 0 else 0
        accrued_interest = row.get('accrued_interest_cb', 0.0)

        # ZL模型相关信息
        theoretical_price = row.get('theoretical_price', np.nan)
        arbitrage_space = row.get('arbitrage_space', np.nan)
        dynamic_arbitrage_space = row.get('dynamic_arbitrage_space', np.nan)
        arbitrage_space_ma20 = row.get('arbitrage_space_ma20', np.nan)

        # 获取全价数据，如果不存在则使用净价
        full_price = row.get('full_price_cb', row['close_cb'])
        full_price_note = "" if 'full_price_cb' in row and pd.notna(row['full_price_cb']) else " (备用净价)"
        
        info_str = f"""当前日期: {row['date'].strftime('%Y-%m-%d')} ({self.current_index + 1}/{len(self.merged_data)})

=== 价格信息 ===
可转债净价: {row['close_cb']:.2f} 元    成交量: {cb_volume:,.0f} 手
可转债全价: {full_price:.2f} 元{full_price_note}    应计利息: {accrued_interest:.4f} 元
正股价格: {row['close_stock']:.2f} 元    成交量: {stock_volume:,.0f} 手

=== 转换信息 ===
转股价: {self.conv_price:.2f} 元
转股价值: {row['conversion_value']:.2f} 元

=== 溢价率分析 ===
转股溢价率: {row['premium_ratio']*100:.2f}%
纯债溢价率: {row['bond_premium_ratio']*100:.2f}%

=== ZL模型分析 ==="""

        # 添加ZL模型信息
        if self.zl_model_available and not pd.isna(theoretical_price):
            info_str += f"""
理论价格: {theoretical_price:.2f} 元
套利空间: {arbitrage_space:.2f} 元
20日均值: {arbitrage_space_ma20:.2f} 元
动态套利空间: {dynamic_arbitrage_space:.2f} 元"""
        else:
            info_str += f"""
理论价格: 不可用
套利空间: 不可用
动态套利空间: 不可用"""

        info_str += f"""

=== 操作提示 ===
← → 或 A D: 单日移动    ↑ ↓ 或 W S: 10日跳跃
Home/End: 跳转到首尾    关闭窗口: 退出程序"""

        # 清空并更新文本
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_str)
    
    def show(self):
        """显示图表"""
        try:
            self.create_plot()
            print("\n图表已打开，使用以下键盘操作:")
            print("← → 或 A D 键: 单日移动游标")
            print("↑ ↓ 或 W S 键: 10日跳跃移动")
            print("Home 键: 跳转到开始")
            print("End 键: 跳转到结束")
            print("关闭窗口退出程序")
            print("\n功能特性:")
            print("- 第一张图：同时显示可转债净价和全价走势对比")
            print("- 信息面板：显示净价、全价和应计利息详细信息")
            print("- ZL模型：显示套利空间和动态套利空间分析")
            print("- 支持键盘交互和滚动查看所有图表")

            # 启动tkinter主循环
            self.root.mainloop()
        except Exception as e:
            print(f"GUI显示失败: {e}")
            print("正在生成静态图片作为替代...")
            self.create_static_plot()

    def create_static_plot(self):
        """创建静态图片版本"""
        # 创建图表（增加套利空间子图）
        fig = plt.figure(figsize=(20, 15))
        fig.suptitle(f'{self.bond_name} ({self.cb_code}) 价格走势与溢价率分析', fontsize=16, fontweight='bold')

        # 创建子图布局：3行2列，第三行跨两列显示套利空间
        ax1 = plt.subplot(3, 2, 1)  # 第一行左：可转债价格
        ax2 = plt.subplot(3, 2, 2)  # 第一行右：正股价格
        ax3 = plt.subplot(3, 1, 2)  # 第二行跨列：ZL模型套利空间
        ax4 = plt.subplot(3, 2, 5)  # 第三行左：转股溢价率
        ax5 = plt.subplot(3, 2, 6)  # 第三行右：纯债溢价率

        # 绘制可转债价格走势（收盘价和全价）
        ax1.plot(self.merged_data['date'], self.merged_data['close_cb'], 'b-', linewidth=2, label='收盘价(净价)')
        
        # 检查全价数据是否存在，如果不存在则使用收盘价作为备用
        if 'full_price_cb' in self.merged_data.columns:
            full_price_data = self.merged_data['full_price_cb']
            full_price_label = '全价'
        else:
            full_price_data = self.merged_data['close_cb']
            full_price_label = '全价(备用净价)'
        
        ax1.plot(self.merged_data['date'], full_price_data, 'r--', linewidth=2, label=full_price_label)
        ax1.set_ylabel('可转债价格 (元)', fontsize=10)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_title('可转债价格走势 (净价 vs 全价)', fontsize=12)

        # 绘制正股价格
        ax2.plot(self.merged_data['date'], self.merged_data['close_stock'], 'purple', linewidth=2, label='正股价格')
        ax2.set_ylabel('正股价格 (元)', fontsize=10)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_title('正股价格走势', fontsize=12)

        # 绘制ZL模型套利空间（第三个位置，跨列显示）
        if self.zl_model_available and 'arbitrage_space' in self.merged_data.columns:
            valid_data = self.merged_data.dropna(subset=['arbitrage_space'])
            if not valid_data.empty:
                ax3.plot(valid_data['date'], valid_data['arbitrage_space'], 'red', linewidth=2, label='套利空间')

                if 'dynamic_arbitrage_space' in valid_data.columns:
                    valid_dynamic = valid_data.dropna(subset=['dynamic_arbitrage_space'])
                    if not valid_dynamic.empty:
                        ax3.plot(valid_dynamic['date'], valid_dynamic['dynamic_arbitrage_space'], 'blue', linewidth=2, label='动态套利空间')

                ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
                ax3.set_ylabel('套利空间 (元)', fontsize=10)
                ax3.grid(True, alpha=0.3)
                ax3.legend()
                ax3.set_title('ZL模型套利空间分析', fontsize=12)
            else:
                ax3.text(0.5, 0.5, 'ZL模型数据不可用', transform=ax3.transAxes,
                        ha='center', va='center', fontsize=12, color='red')
                ax3.set_title('ZL模型套利空间分析（数据不可用）', fontsize=12)
        else:
            ax3.text(0.5, 0.5, 'ZL模型未初始化', transform=ax3.transAxes,
                    ha='center', va='center', fontsize=12, color='red')
            ax3.set_title('ZL模型套利空间分析（模型不可用）', fontsize=12)

        # 绘制转股溢价率
        ax4.plot(self.merged_data['date'], self.merged_data['premium_ratio'] * 100, 'g-', linewidth=2, label='转股溢价率')
        ax4.set_ylabel('转股溢价率 (%)', fontsize=10)
        ax4.set_xlabel('日期', fontsize=10)
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        ax4.set_title('转股溢价率', fontsize=12)

        # 绘制纯债溢价率
        ax5.plot(self.merged_data['date'], self.merged_data['bond_premium_ratio'] * 100, 'cyan', linewidth=2, label='纯债溢价率')
        ax5.set_ylabel('纯债溢价率 (%)', fontsize=10)
        ax5.set_xlabel('日期', fontsize=10)
        ax5.grid(True, alpha=0.3)
        ax5.legend()
        ax5.set_title('纯债溢价率', fontsize=12)

        # 设置日期格式
        for ax in [ax1, ax2, ax3, ax4, ax5]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图片
        filename = f'cb_info_{self.cb_code.replace(".", "_")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"静态图表已保存为 {filename}")

        # 显示统计信息
        print(f"\n=== 数据统计 ===")
        print(f"平均价格: {self.merged_data['close_cb'].mean():.2f} 元")
        print(f"平均转股溢价率: {self.merged_data['premium_ratio'].mean()*100:.2f}%")
        print(f"平均纯债溢价率: {self.merged_data['bond_premium_ratio'].mean()*100:.2f}%")

        # ZL模型统计信息
        if self.zl_model_available and 'arbitrage_space' in self.merged_data.columns:
            valid_arbitrage = self.merged_data['arbitrage_space'].dropna()
            valid_dynamic = self.merged_data['dynamic_arbitrage_space'].dropna()

            if not valid_arbitrage.empty:
                print(f"\n=== ZL模型统计 ===")
                print(f"平均理论价格: {self.merged_data['theoretical_price'].mean():.2f} 元")
                print(f"平均套利空间: {valid_arbitrage.mean():.2f} 元")
                print(f"套利空间标准差: {valid_arbitrage.std():.2f} 元")
                print(f"套利空间最大值: {valid_arbitrage.max():.2f} 元")
                print(f"套利空间最小值: {valid_arbitrage.min():.2f} 元")

                if not valid_dynamic.empty:
                    print(f"平均动态套利空间: {valid_dynamic.mean():.2f} 元")
                    print(f"动态套利空间标准差: {valid_dynamic.std():.2f} 元")
            else:
                print(f"\n=== ZL模型统计 ===")
                print("ZL模型数据不可用")
        else:
            print(f"\n=== ZL模型统计 ===")
            print("ZL模型未初始化或数据不可用")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='可转债信息交互式展示工具')
    parser.add_argument('cb_code', help='可转债代码 (如: 113050.SZ)')
    parser.add_argument('start_date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('end_date', help='结束日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    try:
        # 创建查看器
        viewer = CBInfoViewer(args.cb_code, args.start_date, args.end_date)
        
        # 显示图表
        viewer.show()
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
