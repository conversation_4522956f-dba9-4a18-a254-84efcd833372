"""
实时模拟交易模块，负责实时数据获取、因子计算和交易执行
"""
import numpy as np
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
import time
import os
import threading
import schedule

# 导入配置管理器
from config.config import config_manager

class RealtimeDataFetcher:
    """实时数据获取类，负责实时获取数据"""
    
    def __init__(self):
        """初始化实时数据获取器"""
        # 设置数据库路径
        self.db_path = config_manager.get_db_path()
        
        # 导入数据获取器
        from data.fetcher import DataFetcher
        self.data_fetcher = DataFetcher()
        
        # 导入数据缓存器
        from data.fetcher import DataCache
        self.data_cache = DataCache()
        
        # 导入数据处理器
        from data.fetcher import DataProcessor
        self.data_processor = DataProcessor()
    
    def fetch_realtime_data(self):
        """
        获取实时数据
        
        Returns:
            是否成功获取数据
        """
        print(f"获取实时数据: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 更新数据
            self.data_fetcher.update_data()
            return True
        except Exception as e:
            print(f"获取实时数据失败: {e}")
            return False
    
    def check_special_events(self):
        """
        检查特殊事件（强赎、下修等）
        
        Returns:
            特殊事件列表
        """
        print(f"检查特殊事件: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 获取当前日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取可转债基本信息
            df_basic = self.data_cache.get_cb_basic()
            
            # 获取可转债代码列表
            ts_codes = df_basic['ts_code'].tolist()
            
            # 导入事件处理器
            from strategy.topndropoutk import EventHandler
            event_handler = EventHandler()
            
            # 检查强赎事件
            call_events = event_handler.check_call_events(ts_codes, today.replace('%Y%m%d', '%Y-%m-%d'))
            
            # 检查回售事件
            put_events = event_handler.check_put_events(ts_codes, today.replace('%Y%m%d', '%Y-%m-%d'))
            
            # 检查转股价调整事件
            price_adjustment_events = event_handler.check_price_adjustment_events(ts_codes, today.replace('%Y%m%d', '%Y-%m-%d'))
            
            # 合并事件
            events = {
                'call_events': call_events,
                'put_events': put_events,
                'price_adjustment_events': price_adjustment_events
            }
            
            return events
        except Exception as e:
            print(f"检查特殊事件失败: {e}")
            return {}


class RealtimeSignalGenerator:
    """实时信号生成类，负责实时生成交易信号"""
    
    def __init__(self, strategy=None):
        """
        初始化实时信号生成器
        
        Args:
            strategy: 策略对象，默认为None，表示使用TopNDropoutK策略
        """
        # 设置策略
        if strategy is None:
            from strategy.topndropoutk import TopNDropoutKStrategy
            self.strategy = TopNDropoutKStrategy(
                n=config_manager.get_default_n(),
                k=config_manager.get_default_k()
            )
        else:
            self.strategy = strategy
        
        # 导入因子计算器
        from factor.zl_model import FactorCalculator
        self.factor_calculator = FactorCalculator()
        
        # 导入信号生成器
        from strategy.topndropoutk import SignalGenerator
        self.signal_generator = SignalGenerator(self.strategy)
    
    def generate_realtime_signals(self, trade_date=None, factor_name='combined_factor', ascending=False):
        """
        生成实时交易信号
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD，默认为None，表示当前日期
            factor_name: 因子名称，默认为'combined_factor'
            ascending: 排序方向，默认为False（降序）
        
        Returns:
            交易信号DataFrame
        """
        # 如果未指定交易日期，使用当前日期
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"生成实时交易信号: {trade_date}")
        
        try:
            # 计算因子
            self.factor_calculator.calculate_factors_for_date(trade_date)
            
            # 生成信号
            signals = self.signal_generator.generate_signals(trade_date, factor_name, ascending)
            
            return signals
        except Exception as e:
            print(f"生成实时交易信号失败: {e}")
            return pd.DataFrame()


class TradingExecutor:
    """交易执行类，负责执行交易"""
    
    def __init__(self, initial_capital=1000000.0):
        """
        初始化交易执行器
        
        Args:
            initial_capital: 初始资金，默认为100万
        """
        # 设置初始资金
        self.initial_capital = initial_capital
        
        # 导入交易模拟器
        from backtest.backtester import TradingSimulator
        self.simulator = TradingSimulator(initial_capital)
    
    def execute_trades(self, signals, trade_date=None):
        """
        执行交易
        
        Args:
            signals: 交易信号DataFrame
            trade_date: 交易日期，格式YYYY-MM-DD，默认为None，表示当前日期
        
        Returns:
            交易记录列表
        """
        # 如果未指定交易日期，使用当前日期
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"执行交易: {trade_date}")
        
        try:
            # 执行交易
            trades = self.simulator.execute_trades(signals, trade_date)
            
            return trades
        except Exception as e:
            print(f"执行交易失败: {e}")
            return []
    
    def get_position_snapshot(self, trade_date=None):
        """
        获取持仓快照
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD，默认为None，表示当前日期
        
        Returns:
            持仓快照
        """
        # 如果未指定交易日期，使用当前日期
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        return self.simulator.get_position_snapshot(trade_date)
    
    def get_net_value(self, trade_date=None):
        """
        获取净值
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD，默认为None，表示当前日期
        
        Returns:
            净值
        """
        # 如果未指定交易日期，使用当前日期
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        return self.simulator.get_net_value(trade_date)


class MonitoringSystem:
    """监控系统类，负责实时监控"""
    
    def __init__(self):
        """初始化监控系统"""
        # 设置日志目录
        self.log_dir = config_manager.get_log_dir()
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 设置报告目录
        self.report_dir = os.path.join(config_manager.root_dir, 'reports')
        os.makedirs(self.report_dir, exist_ok=True)
    
    def log_event(self, event_type, event_data):
        """
        记录事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
        """
        # 获取当前时间
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建日志文件路径
        log_file = os.path.join(self.log_dir, f"{datetime.now().strftime('%Y%m%d')}.log")
        
        # 构建日志内容
        log_content = f"[{now}] [{event_type}] {event_data}\n"
        
        # 写入日志
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_content)
    
    def generate_daily_report(self, trades, positions, net_value):
        """
        生成每日报告
        
        Args:
            trades: 交易记录列表
            positions: 持仓快照
            net_value: 净值
        
        Returns:
            报告文件路径
        """
        # 获取当前日期
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 构建报告文件路径
        report_file = os.path.join(self.report_dir, f"daily_report_{today}.html")
        
        # 生成HTML报告
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>每日报告 {today}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
            </style>
        </head>
        <body>
            <h1>每日报告</h1>
            <p>日期: {today}</p>
            <p>净值: {net_value:.2f}</p>
            
            <h2>持仓情况</h2>
            <table>
                <tr>
                    <th>代码</th>
                    <th>数量</th>
                    <th>成本</th>
                </tr>
        """
        
        # 添加持仓信息
        for ts_code, position in positions['positions'].items():
            html += f"""
                <tr>
                    <td>{ts_code}</td>
                    <td>{position['quantity']}</td>
                    <td>{position['cost']:.2f}</td>
                </tr>
            """
        
        html += f"""
            </table>
            
            <p>现金: {positions['cash']:.2f}</p>
            
            <h2>交易记录</h2>
            <table>
                <tr>
                    <th>代码</th>
                    <th>方向</th>
                    <th>价格</th>
                    <th>数量</th>
                    <th>金额</th>
                </tr>
        """
        
        # 添加交易记录
        for trade in trades:
            # 转换方向为中文
            direction = '买入' if trade['direction'] == 'buy' else '卖出'
            
            html += f"""
                <tr>
                    <td>{trade['ts_code']}</td>
                    <td>{direction}</td>
                    <td>{trade['price']:.2f}</td>
                    <td>{trade['quantity']}</td>
                    <td>{trade['amount']:.2f}</td>
                </tr>
            """
        
        html += """
            </table>
        </body>
        </html>
        """
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        return report_file


class RealtimeSimulator:
    """实时模拟交易类，负责实时模拟交易"""
    
    def __init__(self, strategy=None, initial_capital=1000000.0):
        """
        初始化实时模拟交易器
        
        Args:
            strategy: 策略对象，默认为None，表示使用TopNDropoutK策略
            initial_capital: 初始资金，默认为100万
        """
        # 设置策略
        if strategy is None:
            from strategy.topndropoutk import TopNDropoutKStrategy
            self.strategy = TopNDropoutKStrategy(
                n=config_manager.get_default_n(),
                k=config_manager.get_default_k()
            )
        else:
            self.strategy = strategy
        
        # 初始化实时数据获取器
        self.data_fetcher = RealtimeDataFetcher()
        
        # 初始化实时信号生成器
        self.signal_generator = RealtimeSignalGenerator(self.strategy)
        
        # 初始化交易执行器
        self.trading_executor = TradingExecutor(initial_capital)
        
        # 初始化监控系统
        self.monitoring_system = MonitoringSystem()
        
        # 初始化交易记录
        self.trades = []
        
        # 初始化运行标志
        self.running = False
        
        # 初始化线程
        self.thread = None
    
    def start(self):
        """启动实时模拟交易"""
        if self.running:
            print("实时模拟交易已经在运行中")
            return
        
        self.running = True
        
        # 创建线程
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        
        print("实时模拟交易已启动")
    
    def stop(self):
        """停止实时模拟交易"""
        if not self.running:
            print("实时模拟交易未在运行")
            return
        
        self.running = False
        
        # 等待线程结束
        if self.thread:
            self.thread.join()
        
        print("实时模拟交易已停止")
    
    def _run(self):
        """运行实时模拟交易"""
        # 设置定时任务
        # 每天9:30获取数据
        schedule.every().day.at("09:30").do(self._fetch_data)
        
        # 每天9:35检查特殊事件
        schedule.every().day.at("09:35").do(self._check_special_events)
        
        # 每天9:40生成信号
        schedule.every().day.at("09:40").do(self._generate_signals)
        
        # 每天9:45执行交易
        schedule.every().day.at("09:45").do(self._execute_trades)
        
        # 每天15:00生成每日报告
        schedule.every().day.at("15:00").do(self._generate_daily_report)
        
        # 运行定时任务
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def _fetch_data(self):
        """获取数据"""
        self.monitoring_system.log_event("DATA_FETCH", "开始获取数据")
        
        success = self.data_fetcher.fetch_realtime_data()
        
        if success:
            self.monitoring_system.log_event("DATA_FETCH", "数据获取成功")
        else:
            self.monitoring_system.log_event("DATA_FETCH", "数据获取失败")
    
    def _check_special_events(self):
        """检查特殊事件"""
        self.monitoring_system.log_event("EVENT_CHECK", "开始检查特殊事件")
        
        events = self.data_fetcher.check_special_events()
        
        if events:
            self.monitoring_system.log_event("EVENT_CHECK", f"检测到特殊事件: {events}")
        else:
            self.monitoring_system.log_event("EVENT_CHECK", "未检测到特殊事件")
    
    def _generate_signals(self):
        """生成信号"""
        self.monitoring_system.log_event("SIGNAL_GENERATE", "开始生成信号")
        
        signals = self.signal_generator.generate_realtime_signals()
        
        if not signals.empty:
            self.monitoring_system.log_event("SIGNAL_GENERATE", f"信号生成成功: {len(signals)}个信号")
        else:
            self.monitoring_system.log_event("SIGNAL_GENERATE", "信号生成失败或无信号")
    
    def _execute_trades(self):
        """执行交易"""
        self.monitoring_system.log_event("TRADE_EXECUTE", "开始执行交易")
        
        # 生成信号
        signals = self.signal_generator.generate_realtime_signals()
        
        if signals.empty:
            self.monitoring_system.log_event("TRADE_EXECUTE", "无交易信号，跳过交易")
            return
        
        # 执行交易
        trades = self.trading_executor.execute_trades(signals)
        
        # 记录交易
        self.trades.extend(trades)
        
        if trades:
            self.monitoring_system.log_event("TRADE_EXECUTE", f"交易执行成功: {len(trades)}笔交易")
        else:
            self.monitoring_system.log_event("TRADE_EXECUTE", "无交易执行")
    
    def _generate_daily_report(self):
        """生成每日报告"""
        self.monitoring_system.log_event("REPORT_GENERATE", "开始生成每日报告")
        
        # 获取持仓快照
        positions = self.trading_executor.get_position_snapshot()
        
        # 获取净值
        net_value = self.trading_executor.get_net_value()
        
        # 生成报告
        report_file = self.monitoring_system.generate_daily_report(self.trades, positions, net_value)
        
        # 清空交易记录
        self.trades = []
        
        self.monitoring_system.log_event("REPORT_GENERATE", f"每日报告生成成功: {report_file}")
    
    def run_once(self):
        """运行一次实时模拟交易流程"""
        print("运行一次实时模拟交易流程")
        
        # 获取数据
        self._fetch_data()
        
        # 检查特殊事件
        self._check_special_events()
        
        # 生成信号
        self._generate_signals()
        
        # 执行交易
        self._execute_trades()
        
        # 生成每日报告
        self._generate_daily_report()
        
        print("实时模拟交易流程运行完成")
