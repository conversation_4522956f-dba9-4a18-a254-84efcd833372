"""
到期筛选器
过滤掉即将到期的转债，默认过滤掉6个月内到期的转债
"""

import pandas as pd
import sqlite3
from datetime import datetime, timedelta

from .base_filter import BaseFilter
from config.config import config_manager


class MaturityFilter(BaseFilter):
    """
    到期筛选器
    
    过滤掉即将到期的转债和已公告强赎条件的转债，避免投资风险较高的转债
    筛选逻辑：
    1. 获取转债的到期日期
    2. 计算距离到期的天数
    3. 过滤掉距离到期小于指定天数的转债
    4. 过滤掉已公告触发强赎条件的转债
    """
    
    def __init__(self, min_days_to_maturity=180):
        """
        初始化到期筛选器
        
        Args:
            min_days_to_maturity: 最小到期天数，默认180天（6个月）
        """
        super().__init__("到期筛选器")
        self.min_days_to_maturity = min_days_to_maturity
        self.db_path = config_manager.get_db_path()
        
        print(f"到期筛选器初始化完成: 最小到期天数={min_days_to_maturity}天，同时过滤已公告强赎条件的转债")
    
    def filter_bonds(self, data: pd.DataFrame, top_pct: float = 1.0) -> pd.DataFrame:
        """
        过滤掉即将到期的转债和已公告强赎条件的转债
        
        Args:
            data: 包含可转债数据的DataFrame
            top_pct: 筛选比例，默认1.0表示不再按比例筛选
        
        Returns:
            筛选后的DataFrame，已排除即将到期和已公告强赎条件的转债
        """
        if not self.validate_data(data):
            return pd.DataFrame()
        
        # 获取增强数据（包含到期日期）
        enriched_data = self._enrich_data(data)
        
        if enriched_data.empty:
            print(f"警告：{self.name} 无法获取到期日期数据")
            return pd.DataFrame()
        
        # 筛选掉即将到期的转债和已公告强赎条件的转债
        filtered_data = self._filter_by_maturity_and_call(enriched_data)
        
        if filtered_data.empty:
            print(f"警告：{self.name} 筛选后无可投资转债")
            return pd.DataFrame()
        
        # 计算筛选得分（所有通过筛选的转债得分相同）
        filtered_data = self.calculate_score(filtered_data)
        
        return filtered_data
    
    def calculate_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算筛选得分
        
        Args:
            data: 包含可转债数据的DataFrame
        
        Returns:
            添加了筛选得分的DataFrame
        """
        result = data.copy()
        result['filter_score'] = 1.0  # 所有通过筛选的债券得分相同
        return result
    
    def _enrich_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        丰富数据，获取计算所需的到期日期信息
        
        Args:
            data: 原始数据
        
        Returns:
            包含到期日期的DataFrame
        """
        if data.empty:
            return pd.DataFrame()
        
        # 获取唯一的交易日期
        trade_dates = data['trade_date'].unique()
        
        if len(trade_dates) == 0:
            return pd.DataFrame()
        
        # 取第一个交易日期作为当前日期
        current_date = trade_dates[0]
        
        # 获取转债代码列表
        ts_codes = data['ts_code'].unique().tolist()
        
        if not ts_codes:
            return pd.DataFrame()
        
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            
            # 构建SQL查询
            placeholders = ', '.join(['?' for _ in ts_codes])
            sql = f"""
            SELECT 
                cb.ts_code,
                cb.maturity_date,
                cb.delist_date
            FROM cb_basic cb
            WHERE cb.ts_code IN ({placeholders})
            """
            
            # 执行查询
            df_maturity = pd.read_sql(sql, conn, params=ts_codes)
            conn.close()
            
            if df_maturity.empty:
                print(f"警告：{self.name} 无法获取转债基本信息")
                return pd.DataFrame()
            
            # 合并数据
            enriched_data = data.merge(df_maturity, on='ts_code', how='left')
            
            # 添加当前日期
            enriched_data['current_date'] = current_date
            
            return enriched_data
            
        except Exception as e:
            print(f"错误：{self.name} 获取到期日期数据失败: {e}")
            return pd.DataFrame()
    
    def _filter_by_maturity_and_call(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        根据到期日期和强赎条件过滤转债
        
        Args:
            data: 包含到期日期的DataFrame
        
        Returns:
            筛选后的DataFrame
        """
        if data.empty:
            return pd.DataFrame()
        
        # 过滤掉没有到期日期的转债
        data = data[data['maturity_date'].notna()]
        data = data[data['maturity_date'] != '']
        
        if data.empty:
            return pd.DataFrame()
        
        # 计算距离到期天数
        def calculate_days_to_maturity(row):
            try:
                current_date = datetime.strptime(str(row['current_date']), '%Y%m%d')
                maturity_date = datetime.strptime(str(row['maturity_date']), '%Y%m%d')
                
                # 计算天数差
                days_diff = (maturity_date - current_date).days
                return days_diff
            except:
                return -1  # 如果日期解析失败，标记为-1
        
        # 计算距离到期天数
        data['days_to_maturity'] = data.apply(calculate_days_to_maturity, axis=1)
        
        # 过滤掉即将到期的转债
        filtered_data = data[data['days_to_maturity'] >= self.min_days_to_maturity]
        
        # 同时过滤掉已经退市的转债（如果有delist_date列的话）
        if 'delist_date' in filtered_data.columns:
            filtered_data = filtered_data[
                (filtered_data['delist_date'].isna()) | 
                (filtered_data['delist_date'] == '') | 
                (filtered_data['delist_date'] > filtered_data['current_date'])
            ]
        
        # 过滤掉已公告强赎条件的转债
        filtered_data = self._filter_by_call_events(filtered_data)
        
        return filtered_data
    
    def _filter_by_call_events(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        过滤掉已公告强赎条件的转债
        
        Args:
            data: 包含转债数据的DataFrame
        
        Returns:
            筛选后的DataFrame，排除已公告强赎条件的转债
        """
        if data.empty:
            return pd.DataFrame()
        
        # 获取转债代码列表
        ts_codes = data['ts_code'].unique().tolist()
        current_date = data['current_date'].iloc[0]
        
        if not ts_codes:
            return data
        
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            
            # 构建SQL查询，查找已公告强赎条件的转债
            # 查询强赎日期在当前日期之后的转债（即将被强赎）
            # 以及强赎日期在当前日期的转债（当日强赎）
            placeholders = ', '.join(['?' for _ in ts_codes])
            sql = f"""
            SELECT DISTINCT ts_code
            FROM cb_call
            WHERE ts_code IN ({placeholders})
            AND call_date >= ?
            """
            
            # 执行查询，查找有强赎公告的转债
            df_call_bonds = pd.read_sql(sql, conn, params=ts_codes + [current_date])
            conn.close()
            
            if df_call_bonds.empty:
                # 没有强赎公告的转债，返回原数据
                return data
            
            # 获取有强赎公告的转债代码
            call_ts_codes = df_call_bonds['ts_code'].tolist()
            
            # 过滤掉有强赎公告的转债
            filtered_data = data[~data['ts_code'].isin(call_ts_codes)]
            
            return filtered_data
            
        except Exception as e:
            print(f"警告：{self.name} 查询强赎事件失败: {e}")
            return data