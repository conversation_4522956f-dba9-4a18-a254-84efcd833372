"""
双低策略筛选器
基于"低价格"和"低转股溢价"的双低策略进行可转债筛选
"""

import pandas as pd
import sqlite3

from .base_filter import BaseFilter
from config.config import config_manager


class DoubleLowFilter(BaseFilter):
    """
    双低策略筛选器

    基于简化的双低策略对可转债进行筛选
    筛选逻辑：
    1. 价格因子：转债当前价格
    2. 溢价因子：转股溢价率 * 100
    3. 双低得分：价格因子 + 溢价因子
    4. 按双低得分从小到大排序，选择前N%
    """

    def __init__(self, alpha=0.5):
        """
        初始化双低筛选器

        Args:
            alpha: 价格因子权重，范围[0,1]，默认0.5
        """
        super().__init__("双低策略筛选器")
        self.alpha = alpha
        self.db_path = config_manager.get_db_path()

        print(f"双低筛选器初始化完成: α={alpha:.2f} (价格权重:{alpha:.1%}, 溢价权重:{1-alpha:.1%})")
    
    def filter_bonds(self, data: pd.DataFrame, top_pct: float = 0.2) -> pd.DataFrame:
        """
        使用双低策略筛选可转债
        
        Args:
            data: 包含可转债数据的DataFrame
            top_pct: 筛选比例，默认0.2表示前20%
        
        Returns:
            筛选后的DataFrame，按双低得分降序排列
        """
        if not self.validate_data(data):
            return pd.DataFrame()
        
        # 计算双低得分
        scored_data = self.calculate_score(data)
        
        if scored_data.empty:
            print(f"警告：{self.name} 无法计算得分")
            return pd.DataFrame()
        
        # 按双低得分升序排列（得分越小越好）
        scored_data = scored_data.sort_values('filter_score', ascending=True)
        
        # 选择前N%
        n_select = max(1, int(len(scored_data) * top_pct))
        filtered_data = scored_data.head(n_select)
        

        return filtered_data
    
    def calculate_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算双低得分

        Args:
            data: 包含可转债数据的DataFrame

        Returns:
            添加了双低得分的DataFrame
        """
        result = data.copy()

        # 获取必要的数据
        enriched_data = self._enrich_data(result)

        if enriched_data.empty:
            return pd.DataFrame()

        # 计算双低得分
        enriched_data = self._calculate_double_low_score(enriched_data)

        return enriched_data
    
    def _enrich_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        丰富数据，获取计算双低得分所需的额外信息
        
        优先使用传入数据中已有的字段，如果缺失再查询数据库
        
        Args:
            data: 原始数据
        
        Returns:
            丰富后的数据，包含正股价格、转股价等信息
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        # 检查数据中是否已经包含必要字段
        has_stock_close = 'stock_close' in result.columns
        if has_stock_close:
            has_stock_close = result['stock_close'].notna().sum() > 0
        
        # 检查conv_price字段
        has_conv_price = 'conv_price' in result.columns
        if has_conv_price:
            has_conv_price = result['conv_price'].notna().sum() > 0
        
        # 如果传入数据中已经有stock_close和conv_price，直接使用
        if has_stock_close and has_conv_price:
            return result
        
        # 如果缺少字段，从数据库查询
        print(f"传入数据缺少必要字段，从数据库查询 (has_stock_close: {has_stock_close}, has_conv_price: {has_conv_price})")

        # 获取第一个交易日期作为查询日期
        trade_date = data['trade_date'].iloc[0]
        ts_codes = data['ts_code'].tolist()

        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询条件
            ts_codes_str = "', '".join(ts_codes)

            # 只查询缺失的字段
            if not has_conv_price:
                # 查询转股价格
                query = f"""
                SELECT
                    ts_code,
                    conv_price
                FROM cb_basic
                WHERE ts_code IN ('{ts_codes_str}')
                AND conv_price > 0
                """

                conv_df = pd.read_sql(query, conn)
                if not conv_df.empty:
                    result = result.merge(
                        conv_df[['ts_code', 'conv_price']],
                        on='ts_code',
                        how='left'
                    )
                    print(f"从cb_basic表获取了{len(conv_df)}条转股价格数据")
                else:
                    print("警告：无法从cb_basic表获取转股价格数据")
            
            if not has_stock_close:
                # 查询正股价格
                query = f"""
                SELECT
                    cb.ts_code,
                    stock.close as stock_close
                FROM cb_daily cb
                LEFT JOIN cb_basic basic ON cb.ts_code = basic.ts_code
                LEFT JOIN stock_daily stock ON basic.stk_code = stock.ts_code
                    AND cb.trade_date = stock.trade_date
                WHERE cb.ts_code IN ('{ts_codes_str}')
                AND cb.trade_date = '{trade_date}'
                AND cb.close > 0
                """
                
                stock_df = pd.read_sql(query, conn)
                if not stock_df.empty:
                    result = result.merge(
                        stock_df[['ts_code', 'stock_close']], 
                        on='ts_code', 
                        how='left'
                    )
                    print(f"从stock_daily表获取了{len(stock_df)}条正股价格数据")
                else:
                    print("警告：无法从stock_daily表获取正股价格数据")
            
            conn.close()
            return result
            
        except Exception as e:
            print(f"警告：获取丰富数据失败: {e}")
            return result

    def _calculate_days_difference(self, list_date, trade_date):
        """
        计算上市日期和交易日期的天数差

        Args:
            list_date: 上市日期，格式YYYYMMDD
            trade_date: 交易日期，格式YYYYMMDD

        Returns:
            int: 天数差
        """
        try:
            from datetime import datetime

            if not list_date or not trade_date:
                return 0

            list_dt = datetime.strptime(str(list_date), '%Y%m%d')
            trade_dt = datetime.strptime(str(trade_date), '%Y%m%d')

            return (trade_dt - list_dt).days
        except:
            return 0
    
    def _calculate_double_low_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算双低得分

        双低得分 = α * 价格因子 + (1-α) * 溢价因子
        其中：价格因子 = 转债价格，溢价因子 = 转股溢价率 * 100
        得分越小越好（低价格 + 低溢价）
        """
        result = data.copy()

        # 价格因子：直接使用转债价格
        result['price_factor'] = result['close']

        # 溢价因子：转股溢价率 * 100
        result['premium_factor'] = result['premium_ratio'] * 100

        # 双低得分：加权组合
        result['filter_score'] = self.alpha * result['price_factor'] + (1 - self.alpha) * result['premium_factor']

        return result
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证双低筛选器的输入数据
        
        Args:
            data: 输入数据
        
        Returns:
            是否符合要求
        """
        # 调用基类验证
        if not super().validate_data(data):
            return False
        
        # 双低筛选器特定验证
        if 'vol' not in data.columns:
            print(f"警告：{self.name} 建议包含成交量(vol)列以提高筛选质量")
        
        return True
