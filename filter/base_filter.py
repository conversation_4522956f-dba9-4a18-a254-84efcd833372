"""
基础筛选接口模块
定义抽象的筛选接口，用于在回测调仓时预筛选可转债
"""

from abc import ABC, abstractmethod
import pandas as pd
from typing import List, Optional


class BaseFilter(ABC):
    """
    抽象筛选接口
    
    用于在回测调仓时对所有可转债进行预筛选，
    选出符合特定条件的前N%可转债，然后再进行因子筛选
    """
    
    def __init__(self, name: str):
        """
        初始化筛选器
        
        Args:
            name: 筛选器名称
        """
        self.name = name
    
    @abstractmethod
    def filter_bonds(self, data: pd.DataFrame, top_pct: float = 0.2) -> pd.DataFrame:
        """
        筛选可转债
        
        Args:
            data: 包含可转债数据的DataFrame，必须包含以下列：
                 - ts_code: 可转债代码
                 - trade_date: 交易日期
                 - close: 收盘价
                 - 其他筛选所需的列
            top_pct: 筛选比例，默认0.2表示前20%
        
        Returns:
            筛选后的DataFrame，按筛选得分降序排列
        """
        pass
    
    @abstractmethod
    def calculate_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算筛选得分
        
        Args:
            data: 包含可转债数据的DataFrame
        
        Returns:
            添加了筛选得分列的DataFrame
        """
        pass
    
    def get_name(self) -> str:
        """获取筛选器名称"""
        return self.name
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否符合要求
        
        Args:
            data: 输入数据
        
        Returns:
            是否符合要求
        """
        required_columns = ['ts_code', 'trade_date', 'close']
        
        if data.empty:
            print(f"警告：{self.name} 筛选器收到空数据")
            return False
        
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            print(f"警告：{self.name} 筛选器缺少必要列: {missing_columns}")
            return False
        
        return True


class NoFilter(BaseFilter):
    """
    无筛选器
    
    不进行任何筛选，直接返回原始数据
    """
    
    def __init__(self):
        super().__init__("无筛选")
    
    def filter_bonds(self, data: pd.DataFrame, top_pct: float = 0.2) -> pd.DataFrame:
        """
        不进行筛选，直接返回原始数据
        
        Args:
            data: 原始数据
            top_pct: 忽略此参数
        
        Returns:
            原始数据
        """
        if not self.validate_data(data):
            return pd.DataFrame()
        
        return data.copy()
    
    def calculate_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        不计算得分，添加默认得分列
        
        Args:
            data: 原始数据
        
        Returns:
            添加了默认得分的数据
        """
        result = data.copy()
        result['filter_score'] = 1.0  # 所有债券得分相同
        return result


class CompositeFilter(BaseFilter):
    """
    组合筛选器
    
    支持多个筛选器的链式筛选，按顺序依次执行每个筛选器
    """
    
    def __init__(self, filters: List[BaseFilter]):
        """
        初始化组合筛选器
        
        Args:
            filters: 筛选器列表，按顺序执行
        """
        filter_names = [f.get_name() for f in filters]
        super().__init__(f"组合筛选({' -> '.join(filter_names)})")
        self.filters = filters
    
    def filter_bonds(self, data: pd.DataFrame, top_pct: float = 0.2) -> pd.DataFrame:
        """
        链式筛选可转债
        
        Args:
            data: 原始数据
            top_pct: 筛选比例，传递给每个筛选器
        
        Returns:
            经过所有筛选器处理的数据
        """
        if not self.validate_data(data):
            return pd.DataFrame()
        
        result = data.copy()
        
        # 依次执行每个筛选器
        for filter_instance in self.filters:
            if result.empty:
                break
                
            try:
                result = filter_instance.filter_bonds(result, top_pct)
            except Exception as e:
                print(f"警告：筛选器 {filter_instance.get_name()} 执行失败: {e}")
                continue
        
        return result
    
    def calculate_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算组合筛选得分
        
        使用最后一个筛选器的得分，或者可以实现更复杂的组合逻辑
        
        Args:
            data: 原始数据
        
        Returns:
            添加了组合得分的数据
        """
        if not self.filters:
            result = data.copy()
            result['filter_score'] = 1.0
            return result
        
        # 使用最后一个筛选器的得分
        return self.filters[-1].calculate_score(data)
