"""
筛选器工厂模块
用于创建和管理不同类型的筛选器
"""

from typing import Optional
from .base_filter import BaseFilter, NoFilter, CompositeFilter
from .double_low_filter import DoubleLowFilter
from .maturity_filter import MaturityFilter


class FilterFactory:
    """
    筛选器工厂类
    
    负责创建和管理不同类型的筛选器实例
    """
    
    @staticmethod
    def create_filter(filter_type: str, **kwargs) -> BaseFilter:
        """
        创建筛选器实例
        
        Args:
            filter_type: 筛选器类型
                - 'none': 无筛选
                - 'double_low': 双低策略筛选
                - 'maturity': 到期筛选（过滤6个月内到期的转债）
            **kwargs: 筛选器特定参数
        
        Returns:
            筛选器实例
        
        Raises:
            ValueError: 不支持的筛选器类型
        """
        filter_type = filter_type.lower()
        
        if filter_type == 'none':
            return NoFilter()
        
        elif filter_type == 'double_low':
            alpha = kwargs.get('alpha', 0.5)
            return DoubleLowFilter(alpha=alpha)
        
        elif filter_type == 'maturity':
            min_days_to_maturity = kwargs.get('min_days_to_maturity', 180)
            return MaturityFilter(min_days_to_maturity=min_days_to_maturity)
        
        else:
            raise ValueError(f"不支持的筛选器类型: {filter_type}")
    
    @staticmethod
    def create_nested_filter(filter_type: str, **kwargs) -> BaseFilter:
        """
        创建嵌套筛选器实例
        
        根据用户需求：
        - 默认情况（不带参数）：使用maturity filter
        - 当指定--filter double_low时：先执行maturity再执行double_low
        
        Args:
            filter_type: 筛选器类型
            **kwargs: 筛选器特定参数
        
        Returns:
            筛选器实例（可能是组合筛选器）
        """
        filter_type = filter_type.lower()
        
        if filter_type == 'none':
            return NoFilter()
        
        elif filter_type == 'maturity':
            # 默认情况，只使用maturity filter
            min_days_to_maturity = kwargs.get('min_days_to_maturity', 180)
            return MaturityFilter(min_days_to_maturity=min_days_to_maturity)
        
        elif filter_type == 'double_low':
            # 嵌套筛选：先maturity再double_low
            min_days_to_maturity = kwargs.get('min_days_to_maturity', 180)
            alpha = kwargs.get('alpha', 0.5)
            
            maturity_filter = MaturityFilter(min_days_to_maturity=min_days_to_maturity)
            double_low_filter = DoubleLowFilter(alpha=alpha)
            
            return CompositeFilter([maturity_filter, double_low_filter])
        
        else:
            raise ValueError(f"不支持的筛选器类型: {filter_type}")
    
    @staticmethod
    def get_available_filters() -> list:
        """
        获取所有可用的筛选器类型
        
        Returns:
            筛选器类型列表
        """
        return ['none', 'double_low', 'maturity']
    
    @staticmethod
    def get_filter_description(filter_type: str) -> str:
        """
        获取筛选器描述
        
        Args:
            filter_type: 筛选器类型
        
        Returns:
            筛选器描述
        """
        descriptions = {
            'none': '无筛选 - 不进行任何预筛选，直接使用因子选择',
            'double_low': '双低策略筛选 - 基于低价格和低转股溢价进行预筛选',
            'maturity': '到期筛选 - 过滤掉6个月内到期的转债'
        }
        
        return descriptions.get(filter_type.lower(), '未知筛选器类型')


def create_filter_from_args(filter_type: str, **kwargs) -> BaseFilter:
    """
    便捷函数：从参数创建筛选器
    
    Args:
        filter_type: 筛选器类型
        **kwargs: 筛选器参数
    
    Returns:
        筛选器实例
    """
    return FilterFactory.create_nested_filter(filter_type, **kwargs)
