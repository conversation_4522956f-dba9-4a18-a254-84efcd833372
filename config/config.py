"""
配置管理模块，负责加载和管理环境变量和配置参数
"""
import os
import dotenv
from pathlib import Path

class ConfigManager:
    """配置管理类，负责加载和管理配置参数"""
    
    def __init__(self, env_file=None):
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径，默认为None，表示使用默认路径
        """
        # 获取项目根目录
        self.root_dir = Path(__file__).parent.parent.absolute()
        
        # 设置环境变量文件路径
        if env_file is None:
            env_file = os.path.join(self.root_dir, 'config', '.env')
        
        # 加载环境变量
        self.load_env(env_file)
        
        # 初始化配置参数
        self.init_config()
    
    def load_env(self, env_file):
        """
        加载环境变量
        
        Args:
            env_file: 环境变量文件路径
        """
        # 检查环境变量文件是否存在
        if not os.path.exists(env_file):
            # 如果不存在，则从示例文件复制
            example_env_file = os.path.join(self.root_dir, 'config', '.env.example')
            if os.path.exists(example_env_file):
                import shutil
                shutil.copy(example_env_file, env_file)
                # print(f"环境变量文件不存在，已从示例文件创建: {env_file}")
                pass
            else:
                # print(f"环境变量文件不存在，且示例文件也不存在: {example_env_file}")
                pass
        
        # 加载环境变量
        dotenv.load_dotenv(env_file)
        # print(f"已加载环境变量文件: {env_file}")  # 注释掉减少输出
    
    def init_config(self):
        """初始化配置参数"""
        # 数据库配置 - 使用独立的数据存储目录
        self.db_path = os.getenv('DB_PATH', os.path.join(self.root_dir, 'data_storage', 'convertible_bond.db'))
        
        # 缓存配置
        self.cache_dir = os.getenv('CACHE_DIR', os.path.join(self.root_dir, 'cache'))
        
        # 日志配置
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_dir = os.getenv('LOG_DIR', os.path.join(self.root_dir, 'logs'))
        
        # 回测配置
        self.default_start_date = os.getenv('DEFAULT_START_DATE', '2020-01-01')
        self.default_end_date = os.getenv('DEFAULT_END_DATE', '2025-06-06')
        
        # 策略配置
        self.default_strategy = os.getenv('DEFAULT_STRATEGY', 'topndropoutk')
        self.default_n = int(os.getenv('DEFAULT_N', '10'))
        self.default_k = int(os.getenv('DEFAULT_K', '2'))
        
        # ZL模型参数配置
        self.default_simulation_paths = int(os.getenv('DEFAULT_SIMULATION_PATHS', '10000'))
        self.default_time_steps = int(os.getenv('DEFAULT_TIME_STEPS', '252'))
        self.default_risk_free_rate = float(os.getenv('DEFAULT_RISK_FREE_RATE', '0.03'))
        
        # 回测参数配置
        self.default_initial_capital = float(os.getenv('DEFAULT_INITIAL_CAPITAL', '1000000'))
        
        # Tushare配置
        self.tushare_token = os.getenv('TUSHARE_TOKEN', '')
        if not self.tushare_token:
            # print("警告: 未设置Tushare API Token，请在.env文件中设置TUSHARE_TOKEN")
            pass
    
    def get_tushare_token(self):
        """获取Tushare API Token"""
        return self.tushare_token
    
    def get_db_path(self):
        """获取数据库路径"""
        return self.db_path
    
    def get_cache_dir(self):
        """获取缓存目录"""
        return self.cache_dir
    
    def get_log_dir(self):
        """获取日志目录"""
        return self.log_dir
    
    def get_log_level(self):
        """获取日志级别"""
        return self.log_level
    
    def get_default_start_date(self):
        """获取默认开始日期"""
        return self.default_start_date
    
    def get_default_end_date(self):
        """获取默认结束日期"""
        return self.default_end_date
    
    def get_default_strategy(self):
        """获取默认策略"""
        return self.default_strategy
    
    def get_default_n(self):
        """获取默认N值"""
        return self.default_n
    
    def get_default_k(self):
        """获取默认K值"""
        return self.default_k
    
    def get_default_simulation_paths(self):
        """获取默认模拟路径数"""
        return self.default_simulation_paths
    
    def get_default_time_steps(self):
        """获取默认时间步数"""
        return self.default_time_steps
    
    def get_default_risk_free_rate(self):
        """获取默认无风险利率"""
        return self.default_risk_free_rate
    
    def get_default_initial_capital(self):
        """获取默认初始资金"""
        return self.default_initial_capital


# 创建全局配置管理器实例
config_manager = ConfigManager()
