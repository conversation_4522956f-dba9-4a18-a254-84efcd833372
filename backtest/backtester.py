#!/usr/bin/env python3
"""
优化版回测模块 - 集成最佳实践
包含：季度调整 + 事件驱动 + 最优参数 + 性能优化
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor
import warnings
import sys
import os
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import config_manager


class Backtester:
    """
    优化版回测器 - 集成最佳策略和性能优化
    
    默认配置：
    - 季度调整频率（降低交易成本）
    - 事件驱动的即时调整（强制转股、转换价变动）
    - 最优策略参数 (N=10, K=2)
    - 严格因子筛选（前20%）
    - 多精度计算支持
    """
    
    def __init__(self, start_date, end_date, strategy=None, initial_capital=1000000.0,
                 precision_level='medium', rebalance_frequency='weekly'):
        """
        初始化优化版回测器

        Args:
            start_date: 开始日期
            end_date: 结束日期
            strategy: 策略对象（自动使用最优参数N=10, K=2）
            initial_capital: 初始资金
            precision_level: 计算精度 ('fast', 'medium', 'high', 'full')
            rebalance_frequency: 调整频率 ('daily', 'weekly', 'monthly', 'quarterly')
        """
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.precision_level = precision_level
        self.rebalance_frequency = rebalance_frequency
        self.db_path = config_manager.get_db_path()
        
        # 设置最优策略参数
        if strategy is None:
            from strategy.topndropoutk import TopNDropoutKStrategy
            strategy = TopNDropoutKStrategy(n=15, k=3)  # 调整参数：更大的组合，更少的换手
        self.strategy = strategy
        
        # 最优配置
        self.min_factor_rank_pct = 0.25  # 前25%的债券
        self.transaction_cost = 0.002   # 0.2%交易成本
        self.factor_ascending = False   # 降序排列（因子值高的优先）
        
        # 强制转股等特殊事件的处理阈值
        self.force_conversion_threshold = 130.0  # 强制转股价阈值
        self.conversion_price_change_threshold = 0.05  # 转换价变动5%以上触发调整

        # 筛选器
        self.bond_filter = None  # 可转债预筛选器

        # 线性回归模型
        self.linear_model = None
        self.linear_model_name = None
        
        # ML模型
        self.ml_model = None
        self.ml_model_name = None
        
        # FT-Transformer模型
        self.ft_model = None
        self.ft_model_name = None

        # LightGBM模型
        self.lgbm_model = None
        self.lgbm_model_name = None

        # 强制赎回价格信息
        self.call_prices = {}

        print(f"优化版回测器初始化完成: {start_date} - {end_date}")
        print(f"策略参数: N={self.strategy.n}, K={self.strategy.k}")
        print(f"调整频率: {rebalance_frequency}, 因子筛选: 前{self.min_factor_rank_pct*100}%")

    def set_filter(self, bond_filter):
        """
        设置可转债筛选器

        Args:
            bond_filter: 筛选器实例
        """
        self.bond_filter = bond_filter
        print(f"设置筛选器: {bond_filter.get_name()}")

    def set_linear_model(self, model_name, factor_config_name='default'):
        """
        设置线性回归模型

        Args:
            model_name: 模型名称
            factor_config_name: 因子配置名称 ('default' 或 'best_ten')
        """
        self.linear_model_name = model_name
        self.linear_model_factor_config_name = factor_config_name
        self._load_linear_model()

    def _load_linear_model(self):
        """加载线性回归模型"""
        if not self.linear_model_name:
            return

        try:
            from factor.linear_regression_optimizer import FactorLinearRegression

            # 获取因子配置名称，默认为'default'
            factor_config_name = getattr(self, 'linear_model_factor_config_name', 'default')
            
            self.linear_model = FactorLinearRegression(
                model_name=self.linear_model_name,
                factor_config_name=factor_config_name
            )

            if self.linear_model.load_model():
                print(f"✅ 线性回归模型已加载: {self.linear_model_name} (因子配置: {factor_config_name})")
            else:
                print(f"❌ 线性回归模型加载失败: {self.linear_model_name}")
                self.linear_model = None

        except Exception as e:
            print(f"❌ 加载线性回归模型时出错: {e}")
            self.linear_model = None

    def _apply_linear_model(self, data):
        """
        应用线性回归模型生成组合因子

        Args:
            data: 包含因子数据的DataFrame

        Returns:
            包含线性组合因子的DataFrame
        """
        if self.linear_model is None:
            return data

        try:
            # 使用线性模型创建组合因子
            result_data = self.linear_model.create_combined_factor(
                data,
                factor_name='linear_combined_factor'
            )

            # 如果成功创建了线性组合因子，使用它替换原有的combined_factor
            if 'linear_combined_factor' in result_data.columns:
                result_data['combined_factor'] = result_data['linear_combined_factor']
                print(f"✅ 应用线性回归模型，将预测值赋值到combined_factor，生成 {len(result_data)} 条组合因子")

            return result_data

        except Exception as e:
            print(f"❌ 应用线性回归模型时出错: {e}")
            return data

    def set_ml_model(self, model_name):
        """
        设置AutoGluon ML模型

        Args:
            model_name: ML模型名称
        """
        self.ml_model_name = model_name
        self._load_ml_model()

    def set_ft_model(self, model_name):
        """
        设置FT-Transformer模型

        Args:
            model_name: FT-Transformer模型名称
        """
        self.ft_model_name = model_name
        self._load_ft_model()

    def set_lgbm_model(self, model_name):
        """
        设置LightGBM模型

        Args:
            model_name: LightGBM模型名称
        """
        self.lgbm_model_name = model_name
        self._load_lgbm_model()

    def _load_ml_model(self):
        """加载AutoGluon ML模型"""
        if not self.ml_model_name:
            return

        try:
            from factor.autogluon_predictor import AutoGluonFactorPredictor

            self.ml_model = AutoGluonFactorPredictor(
                model_name=self.ml_model_name,
                create_unified_preprocessor_now=False  # 关键：不创建新的预处理器，等加载模型时使用保存的状态
            )

            if self.ml_model.load_model():
                print(f"✅ AutoGluon ML模型已加载: {self.ml_model_name}")
            else:
                print(f"❌ AutoGluon ML模型加载失败: {self.ml_model_name}")
                self.ml_model = None

        except Exception as e:
            print(f"❌ 加载AutoGluon ML模型时出错: {e}")
            self.ml_model = None

    def _load_ft_model(self):
        """加载FT-Transformer模型"""
        if not self.ft_model_name:
            return

        try:
            from factor.ft_transformer_predictor import FTTransformerPredictor

            self.ft_model = FTTransformerPredictor()

            if self.ft_model.load_model(self.ft_model_name):
                print(f"✅ FT-Transformer模型已加载: {self.ft_model_name}")
            else:
                print(f"❌ FT-Transformer模型加载失败: {self.ft_model_name}")
                self.ft_model = None

        except Exception as e:
            print(f"❌ 加载FT-Transformer模型时出错: {e}")
            self.ft_model = None

    def _load_lgbm_model(self):
        """加载LightGBM模型"""
        if not self.lgbm_model_name:
            return

        try:
            from factor.lightgbm_predictor import LightGBMPredictor

            self.lgbm_model = LightGBMPredictor(
                model_name=self.lgbm_model_name,
                create_unified_preprocessor_now=False  # 关键：不创建新的预处理器，等加载模型时使用保存的状态
            )

            if self.lgbm_model.load_model():
                print(f"✅ LightGBM模型已加载: {self.lgbm_model_name}")
            else:
                print(f"❌ LightGBM模型加载失败: {self.lgbm_model_name}")
                self.lgbm_model = None

        except Exception as e:
            print(f"❌ 加载LightGBM模型时出错: {e}")
            self.lgbm_model = None

    def _apply_ml_model(self, data):
        """
        应用AutoGluon ML模型生成预测因子

        Args:
            data: 包含因子数据的DataFrame

        Returns:
            包含ML预测因子的DataFrame
        """
        if self.ml_model is None:
            return data

        try:
            # 直接使用ML模型预测，让预测器自己处理特征工程和一致性
            
            # 让AutoGluon预测器自己处理特征一致性和工程特征
            result_data = self.ml_model.create_prediction_factor(
                data,
                factor_name='ml_predicted_return'
            )

            # 如果成功创建了ML预测因子，使用它替换原有的combined_factor
            if 'ml_predicted_return' in result_data.columns:
                result_data['combined_factor'] = result_data['ml_predicted_return']
                print(f"✅ 应用AutoGluon ML模型，生成 {len(result_data)} 条预测因子")

            # 重置索引以避免重复索引问题
            result_data = result_data.reset_index(drop=True)
            return result_data

        except Exception as e:
            print(f"❌ 应用AutoGluon ML模型时出错: {e}")
            import traceback
            traceback.print_exc()
            return data

    def _apply_ft_model(self, data):
        """
        应用FT-Transformer模型生成预测因子

        Args:
            data: 包含因子数据的DataFrame

        Returns:
            包含FT预测因子的DataFrame
        """
        if self.ft_model is None:
            return data

        try:
            # 直接使用FT模型预测，让预测器自己处理特征工程和一致性
            result_data = self.ft_model.create_prediction_factor(
                data,
                factor_name='ft_predicted_return'
            )

            # 如果成功创建了FT预测因子，使用它替换原有的combined_factor
            if 'ft_predicted_return' in result_data.columns:
                result_data['combined_factor'] = result_data['ft_predicted_return']
                print(f"✅ 应用FT-Transformer模型，生成 {len(result_data)} 条预测因子")

            # 重置索引以避免重复索引问题
            result_data = result_data.reset_index(drop=True)
            return result_data

        except Exception as e:
            print(f"❌ 应用FT-Transformer模型时出错: {e}")
            import traceback
            traceback.print_exc()
            return data

    def _apply_lgbm_model(self, data):
        """
        应用LightGBM模型生成预测因子

        Args:
            data: 包含因子数据的DataFrame

        Returns:
            包含LightGBM预测因子的DataFrame
        """
        if self.lgbm_model is None:
            return data

        try:
            # 直接使用LightGBM模型预测，让预测器自己处理特征工程和一致性
            result_data = self.lgbm_model.create_prediction_factor(
                data,
                factor_name='lgbm_predicted_return'
            )

            # 如果成功创建了LightGBM预测因子，使用它替换原有的combined_factor
            if 'lgbm_predicted_return' in result_data.columns:
                result_data['combined_factor'] = result_data['lgbm_predicted_return']
                print(f"✅ 应用LightGBM模型，生成 {len(result_data)} 条预测因子")

            # 重置索引以避免重复索引问题
            result_data = result_data.reset_index(drop=True)
            return result_data

        except Exception as e:
            print(f"❌ 应用LightGBM模型时出错: {e}")
            import traceback
            traceback.print_exc()
            return data

    def _create_missing_features(self, data, base_features, missing_features):
        """
        创建缺失的工程特征
        
        Args:
            data: 原始数据
            base_features: 可用的基础特征
            missing_features: 缺失的特征列表
            
        Returns:
            包含工程特征的数据
        """
        enhanced_data = data.copy()
        
        try:
            # 创建交互特征 (feat1_x_feat2, feat1_div_feat2)
            for missing_feat in missing_features:
                if '_x_' in missing_feat:
                    # 乘积特征
                    parts = missing_feat.split('_x_')
                    if len(parts) == 2 and parts[0] in base_features and parts[1] in base_features:
                        enhanced_data[missing_feat] = enhanced_data[parts[0]] * enhanced_data[parts[1]]
                        print(f"  创建交互特征: {missing_feat}")
                
                elif '_div_' in missing_feat:
                    # 除法特征
                    parts = missing_feat.split('_div_')
                    if len(parts) == 2 and parts[0] in base_features and parts[1] in base_features:
                        # 避免除零错误
                        mask = enhanced_data[parts[1]] != 0
                        enhanced_data[missing_feat] = 0.0
                        enhanced_data.loc[mask, missing_feat] = enhanced_data.loc[mask, parts[0]] / enhanced_data.loc[mask, parts[1]]
                        print(f"  创建比率特征: {missing_feat}")
                
                elif '_squared' in missing_feat:
                    # 平方特征
                    base_name = missing_feat.replace('_squared', '')
                    if base_name in base_features:
                        enhanced_data[missing_feat] = enhanced_data[base_name] ** 2
                        print(f"  创建平方特征: {missing_feat}")
                
                elif '_lag1' in missing_feat:
                    # 滞后特征
                    base_name = missing_feat.replace('_lag1', '')
                    if base_name in base_features and 'ts_code' in enhanced_data.columns and 'trade_date' in enhanced_data.columns:
                        enhanced_data = enhanced_data.sort_values(['ts_code', 'trade_date'])
                        enhanced_data[missing_feat] = enhanced_data.groupby('ts_code')[base_name].shift(1)
                        enhanced_data[missing_feat] = enhanced_data[missing_feat].fillna(0)
                        print(f"  创建滞后特征: {missing_feat}")
                
                elif '_ma3' in missing_feat:
                    # 移动平均特征
                    base_name = missing_feat.replace('_ma3', '')
                    if base_name in base_features and 'ts_code' in enhanced_data.columns and 'trade_date' in enhanced_data.columns:
                        enhanced_data = enhanced_data.sort_values(['ts_code', 'trade_date'])
                        enhanced_data[missing_feat] = enhanced_data.groupby('ts_code')[base_name].rolling(3, min_periods=1).mean().values
                        enhanced_data[missing_feat] = enhanced_data[missing_feat].fillna(0)
                        print(f"  创建移动平均特征: {missing_feat}")
            
            print(f"✅ 成功创建工程特征，数据列数: {data.shape[1]} -> {enhanced_data.shape[1]}")
            return enhanced_data
            
        except Exception as e:
            print(f"❌ 创建工程特征失败: {e}")
            return data

    def run(self, factor_name='combined_factor', ascending=None):
        """
        运行优化版回测

        Args:
            factor_name: 因子名称
            ascending: 排序方向（None时使用最优配置False）

        Returns:
            回测结果
        """
        # 设置因子名称
        self.factor_name = factor_name

        if ascending is None:
            ascending = self.factor_ascending

        print(f"开始优化版回测: {self.start_date} - {self.end_date}")
        
        try:
            # 确保因子数据存在
            self._ensure_factor_data()
            
            # 获取调整日期和事件日期
            rebalance_dates = self._get_rebalance_dates()
            event_dates = self._get_event_dates()
            
            # 合并调整日期（去重）
            all_adjustment_dates = sorted(set(rebalance_dates + event_dates))
            
            print(f"计划调整次数: {len(rebalance_dates)}次 (定期) + {len(event_dates)}次 (事件驱动)")
            
            # 加载数据
            all_data = self._load_all_data()
            
            # 执行回测
            results = self._execute_backtest(all_data, all_adjustment_dates, ascending)
            
            # 计算性能指标
            performance = self._calculate_performance(results)
            
            # 计算平均收益和IC值
            market_stats = self._calculate_market_statistics(all_data)
            ic_stats = self._calculate_ic_statistics(all_data)
            
            # 生成报告
            report_path = self._generate_report(results, performance)
            
            print("优化版回测完成")
            print(f"\n🎯 回测结果:")
            print(f"总收益率: {performance['total_return']:.2%}")
            print(f"年化收益率: {performance['annual_return']:.2%}")
            print(f"最大回撤: {performance['max_drawdown']:.2%}")
            print(f"夏普比率: {performance['sharpe_ratio']:.2f}")
            print(f"年化波动率: {performance['volatility']:.2%}")
            print(f"胜率: {performance['win_rate']:.2%}")
            print(f"总交易次数: {performance['total_trades']}")
            
            # 输出市场统计信息
            if market_stats:
                print(f"\n📊 市场统计信息:")
                print(f"可转债平均收益率: {market_stats['avg_cb_return']:.2%}")
                print(f"可转债收益率标准差: {market_stats['cb_return_std']:.2%}")
                if 'conversion_count' in market_stats:
                    print(f"转股事件数量: {market_stats['conversion_count']}")
                if 'redemption_count' in market_stats:
                    print(f"强赎事件数量: {market_stats['redemption_count']}")
            
            # 输出IC统计信息
            if ic_stats:
                print(f"\n🎯 模型预测IC统计:")
                if 'ic_1d' in ic_stats:
                    print(f"1日收益IC: {ic_stats['ic_1d']:.4f}")
                if 'ic_5d' in ic_stats:
                    print(f"5日收益IC: {ic_stats['ic_5d']:.4f}")
                if 'ic_10d' in ic_stats:
                    print(f"10日收益IC: {ic_stats['ic_10d']:.4f}")
            
            return {
                'performance': performance, 
                'results': results,
                'report': report_path,
                'market_stats': market_stats,
                'ic_stats': ic_stats
            }
            
        except Exception as e:
            print(f"回测执行失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _ensure_factor_data(self):
        """确保因子数据存在，如果没有则计算"""
        conn = sqlite3.connect(self.db_path)
        
        # 检查是否有因子数据
        start_date_str = self.start_date.replace('-', '')
        end_date_str = self.end_date.replace('-', '')
        
        query = f"""
        SELECT COUNT(*) as count
        FROM cb_factor
        WHERE trade_date >= '{start_date_str}'
        AND trade_date <= '{end_date_str}'
        """
        
        result = pd.read_sql(query, conn)
        conn.close()
        
        if result.iloc[0]['count'] == 0:
            print("未找到因子数据，开始计算...")
            self._calculate_factors()
        else:
            print(f"发现因子数据 {result.iloc[0]['count']} 条记录")
    
    def _calculate_factors(self):
        """计算因子数据（使用优化精度）"""
        try:
            from factor.enhanced_zl_model import EnhancedFactorCalculator
            
            calculator = EnhancedFactorCalculator(precision_level=self.precision_level)
            factors = calculator.calculate_factors_for_period(self.start_date, self.end_date)
            
            if len(factors) > 0:
                # 保存到数据库（使用INSERT OR REPLACE避免重复）
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # 使用INSERT OR REPLACE来处理重复数据
                saved_count = 0
                for _, row in factors.iterrows():
                    try:
                        # 构建动态SQL语句
                        columns = list(row.index)
                        placeholders = ', '.join(['?' for _ in columns])
                        column_names = ', '.join(columns)

                        cursor.execute(f'''
                            INSERT OR REPLACE INTO cb_factor ({column_names})
                            VALUES ({placeholders})
                        ''', tuple(row.values))
                        saved_count += 1
                    except Exception as e:
                        print(f"保存因子记录失败: {e}")

                conn.commit()
                conn.close()
                print(f"计算并保存因子数据: {saved_count} 条记录")
            else:
                print("因子计算失败，将使用简单排名替代")
        except Exception as e:
            print(f"因子计算出错: {e}，将使用简单排名替代")
    
    def _get_rebalance_dates(self):
        """获取定期调整日期"""
        conn = sqlite3.connect(self.db_path)
        
        query = f"""
        SELECT DISTINCT trade_date
        FROM cb_daily
        WHERE trade_date >= '{self.start_date.replace('-', '')}'
        AND trade_date <= '{self.end_date.replace('-', '')}'
        ORDER BY trade_date
        """
        
        trading_days = pd.read_sql(query, conn)
        conn.close()
        
        if trading_days.empty:
            return []
        
        trading_days['date'] = pd.to_datetime(trading_days['trade_date'], format='%Y%m%d')
        rebalance_dates = []
        
        if self.rebalance_frequency == 'daily':
            # 每个交易日都调整
            rebalance_dates = trading_days['trade_date'].tolist()
        elif self.rebalance_frequency == 'weekly':
            # 每周第一个交易日
            trading_days['week'] = trading_days['date'].dt.isocalendar().week
            trading_days['year'] = trading_days['date'].dt.year
            for (year, week), group in trading_days.groupby(['year', 'week']):
                rebalance_dates.append(group.iloc[0]['trade_date'])
        elif self.rebalance_frequency == 'quarterly':
            # 每季度第一个交易日
            for year_quarter, group in trading_days.groupby(trading_days['date'].dt.to_period('Q')):
                rebalance_dates.append(group.iloc[0]['trade_date'])
        else:  # monthly
            # 每月第一个交易日
            for year_month, group in trading_days.groupby(trading_days['date'].dt.to_period('M')):
                rebalance_dates.append(group.iloc[0]['trade_date'])
        
        return rebalance_dates
    
    def _get_event_dates(self):
        """获取事件驱动的调整日期（强制转股、转换价变动等）"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 检查必要的表是否存在
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            if 'cb_daily' not in tables:
                print("cb_daily表不存在，跳过事件检测")
                conn.close()
                return []
            
            if 'cb_basic' not in tables:
                print("cb_basic表不存在，跳过事件检测")
                conn.close()
                return []
            
            # 检查cb_basic表中是否有转换价字段
            cursor = conn.execute("PRAGMA table_info(cb_basic)")
            cb_basic_columns = [col[1] for col in cursor.fetchall()]
            
            if 'conv_price' not in cb_basic_columns:
                print("cb_basic表中无转换价字段，跳过事件检测")
                conn.close()
                return []
            
            # 查找转换价显著变动的日期
            event_dates = []
            
            # 方法1：从cb_price_chg表获取转股价调整事件
            if 'cb_price_chg' in tables:
                query1 = f"""
                SELECT DISTINCT change_date as trade_date
                FROM cb_price_chg
                WHERE change_date >= '{self.start_date.replace('-', '')}'
                AND change_date <= '{self.end_date.replace('-', '')}'
                AND convertprice_bef > 0 
                AND convertprice_aft > 0
                AND ABS(convertprice_aft - convertprice_bef) / convertprice_bef > {self.conversion_price_change_threshold}
                ORDER BY change_date
                """
                
                try:
                    price_change_events = pd.read_sql(query1, conn)
                    if not price_change_events.empty:
                        event_dates.extend(price_change_events['trade_date'].tolist())
                        print(f"从cb_price_chg表找到 {len(price_change_events)} 个转股价调整事件")
                except Exception as e:
                    print(f"查询cb_price_chg表失败: {e}")
            
            # 方法2：从cb_call表获取强制赎回事件
            if 'cb_call' in tables:
                query2 = f"""
                SELECT DISTINCT call_date as trade_date
                FROM cb_call
                WHERE call_date >= '{self.start_date.replace('-', '')}'
                AND call_date <= '{self.end_date.replace('-', '')}'
                ORDER BY call_date
                """
                
                try:
                    call_events = pd.read_sql(query2, conn)
                    if not call_events.empty:
                        event_dates.extend(call_events['trade_date'].tolist())
                        print(f"从cb_call表找到 {len(call_events)} 个强制赎回事件")
                except Exception as e:
                    print(f"查询cb_call表失败: {e}")
            
            # 去重并排序
            event_dates = sorted(list(set(event_dates)))
            
            if not event_dates:
                print("未找到转换价变动或强制赎回事件")
                conn.close()
                return []
            
            # 转换为日期格式
            formatted_dates = []
            for date_str in event_dates:
                try:
                    date_obj = datetime.strptime(str(date_str), '%Y%m%d')
                    formatted_dates.append(date_obj.strftime('%Y-%m-%d'))
                except ValueError:
                    continue
            
            print(f"找到 {len(formatted_dates)} 个事件驱动调整日期")
            conn.close()
            return formatted_dates
            
        except Exception as e:
            print(f"获取事件日期失败: {e}")
            conn.close()
            return []
    
    def _get_cb_factor_columns(self, conn):
        """动态获取cb_factor表的所有字段名"""
        cursor = conn.execute("PRAGMA table_info(cb_factor)")
        columns = [row[1] for row in cursor.fetchall()]
        # 排除主键字段，因为它们会在JOIN中处理
        factor_columns = [col for col in columns if col not in ['ts_code', 'trade_date']]
        return factor_columns

    def _load_all_data(self):
        """加载所有数据"""
        conn = sqlite3.connect(self.db_path)

        start_date_str = self.start_date.replace('-', '')
        end_date_str = self.end_date.replace('-', '')

        # 检查表是否存在
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        if 'cb_daily' not in tables:
            print("❌ 数据库中没有cb_daily表，请先运行数据获取:")
            print("python main.py --mode data --action fetch --start_date 2020-01-01 --end_date 2025-01-01")
            conn.close()
            return pd.DataFrame()

        # 尝试加载因子数据
        try:
            if 'cb_factor' in tables:
                # 构建安全的查询（支持动态因子名称），过滤已退市债券
                factor_column = getattr(self, 'factor_name', 'combined_factor')

                # 如果使用线性回归模型、ML模型、FT模型或LightGBM模型，需要加载所有因子数据
                if self.linear_model or self.ml_model or self.ft_model or self.lgbm_model:
                    # 动态获取cb_factor表的所有字段
                    factor_columns = self._get_cb_factor_columns(conn)
                    print(f"获取到的因子列数量: {len(factor_columns)}")
                    print(f"因子列: {factor_columns[:10]}...")  # 只显示前10个
                    # 为字段名添加表别名前缀
                    factor_columns_with_prefix = [f'f.{col}' for col in factor_columns]
                    factor_columns_str = ', '.join(factor_columns_with_prefix)

                    # 对于线性模型和ML模型，使用更宽松的条件，确保有基本的价格数据即可
                    # 不依赖单一因子字段的非空条件
                    query = f"""
                    SELECT f.trade_date, f.ts_code, {factor_columns_str},
                           d.close, d.full_price, d.vol, cb.delist_date, cb.conv_price, stock.close as stock_close_daily
                    FROM cb_factor f
                    JOIN cb_daily d ON f.trade_date = d.trade_date AND f.ts_code = d.ts_code
                    JOIN cb_basic cb ON f.ts_code = cb.ts_code
                    LEFT JOIN stock_daily stock ON cb.stk_code = stock.ts_code AND f.trade_date = stock.trade_date
                    WHERE f.trade_date >= '{start_date_str}'
                    AND f.trade_date <= '{end_date_str}'
                    AND d.close > 0
                    AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > f.trade_date)
                    ORDER BY f.trade_date, d.close DESC
                    """
                else:
                    query = f"""
                    SELECT f.trade_date, f.ts_code, f.{factor_column}, cb.conv_price,
                           d.close, d.full_price, d.vol, cb.delist_date, stock.close as stock_close
                    FROM cb_factor f
                    JOIN cb_daily d ON f.trade_date = d.trade_date AND f.ts_code = d.ts_code
                    JOIN cb_basic cb ON f.ts_code = cb.ts_code
                    LEFT JOIN stock_daily stock ON cb.stk_code = stock.ts_code AND f.trade_date = stock.trade_date
                    WHERE f.trade_date >= '{start_date_str}'
                    AND f.trade_date <= '{end_date_str}'
                    AND f.{factor_column} IS NOT NULL
                    AND d.close > 0
                    AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > f.trade_date)
                    ORDER BY f.trade_date, f.{factor_column} DESC
                    """
                
                data = pd.read_sql(query, conn)
                print(f"SQL查询返回的数据列数: {len(data.columns)}")
                print(f"数据列: {list(data.columns)[:15]}...")  # 只显示前15个

                # 重置索引以避免重复索引问题
                data = data.reset_index(drop=True)

                # 重命名因子列为统一的名称，便于后续处理
                if factor_column != 'combined_factor' and factor_column in data.columns:
                    data['combined_factor'] = data[factor_column]

                # 确保full_price字段存在，如果为空则用close替代
                if 'full_price' not in data.columns or data['full_price'].isna().all():
                    data['full_price'] = data['close']

                # conv_price现在直接从cb_basic表获取，无需额外处理

                # 添加默认字段
                data['force_redeem_flag'] = 0

                if not data.empty:
                    conn.close()
                    print(f"加载因子数据: {len(data)} 条记录")

                    # 应用线性回归模型（如果设置了）
                    data = self._apply_linear_model(data)

                    # 应用ML模型（如果设置了）
                    data = self._apply_ml_model(data)

                    # 应用FT-Transformer模型（如果设置了）
                    data = self._apply_ft_model(data)

                    # 应用LightGBM模型（如果设置了）
                    data = self._apply_lgbm_model(data)

                    return data
                    
        except Exception as e:
            print(f"加载因子数据失败: {e}")
        
        # 如果没有因子数据，使用简单排名（使用净价）
        try:
            print("使用简单价格排名替代因子（基于净价），过滤已退市债券")
            query = f"""
            SELECT d.trade_date, d.ts_code, d.close, d.full_price, d.vol,
                   ROW_NUMBER() OVER (PARTITION BY d.trade_date ORDER BY d.close DESC) as rank_factor,
                   cb.delist_date, cb.conv_price, stock.close as stock_close
            FROM cb_daily d
            JOIN cb_basic cb ON d.ts_code = cb.ts_code
            LEFT JOIN stock_daily stock ON cb.stk_code = stock.ts_code AND d.trade_date = stock.trade_date
            WHERE d.trade_date >= '{start_date_str}'
            AND d.trade_date <= '{end_date_str}'
            AND d.close > 0
            AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > d.trade_date)
            ORDER BY d.trade_date, d.close DESC
            """
            data = pd.read_sql(query, conn)

            # 确保full_price字段存在，如果为空则用close替代
            if 'full_price' not in data.columns or data['full_price'].isna().all():
                data['full_price'] = data['close']
            
            if data.empty:
                raise Exception("No daily data found")
            
            # 将排名转换为因子值（排名越高，因子值越大）
            data['combined_factor'] = 1.0 / data['rank_factor']
            data['force_redeem_flag'] = 0
            
        except Exception as e:
            print(f"❌ 加载基础数据也失败: {e}")
            print("请检查数据库中是否有完整的cb_daily数据")
            data = pd.DataFrame()
        
        conn.close()
        
        print(f"加载数据: {len(data)} 条记录")
        return data

    def _check_call_events(self, trade_date):
        """
        检查强制赎回事件

        Args:
            trade_date: 交易日期，格式YYYYMMDD

        Returns:
            list: 当日需要强制赎回的债券代码列表
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 查询当日强制赎回的债券
            query = """
            SELECT ts_code, call_price, call_type
            FROM cb_call
            WHERE call_date = ?
            """

            call_events = pd.read_sql(query, conn, params=[trade_date])
            conn.close()

            if not call_events.empty:
                # 存储强制赎回价格信息，供后续使用
                self.call_prices = {}
                for _, row in call_events.iterrows():
                    self.call_prices[row['ts_code']] = {
                        'call_price': row['call_price'],
                        'call_type': row['call_type']
                    }
                return call_events['ts_code'].tolist()

            return []

        except Exception as e:
            print(f"检查强制赎回事件失败: {e}")
            return []

    def _get_latest_price(self, ts_code, trade_date):
        """
        获取指定债券在指定日期之前的最近有效价格
        
        Args:
            ts_code: 债券代码
            trade_date: 交易日期，格式YYYYMMDD
            
        Returns:
            float: 最近的有效价格，如果找不到则返回0
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询该债券在指定日期之前最近的有效价格
            query = """
            SELECT close
            FROM cb_daily
            WHERE ts_code = ? AND trade_date <= ? AND close > 0
            ORDER BY trade_date DESC
            LIMIT 1
            """
            
            result = conn.execute(query, (ts_code, trade_date)).fetchone()
            conn.close()
            
            if result:
                return result[0]
            else:
                return 0
                
        except Exception as e:
            print(f"获取 {ts_code} 最近价格失败: {e}")
            return 0

    def _calculate_optimal_exit_price(self, ts_code, trade_date):
        """
        计算强制赎回时的最优退出价格（转股vs强制赎回）

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期，格式YYYYMMDD

        Returns:
            float: 最优退出价格
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 获取转债基本信息
            basic_query = """
            SELECT conv_price, stk_code
            FROM cb_basic
            WHERE ts_code = ?
            """
            basic_data = pd.read_sql(basic_query, conn, params=[ts_code])

            if basic_data.empty:
                conn.close()
                return 0

            conv_price = basic_data.iloc[0]['conv_price']
            stk_code = basic_data.iloc[0]['stk_code']

            # 获取正股价格
            stock_query = """
            SELECT close, COALESCE(org_close, close) as org_close
            FROM stock_daily
            WHERE ts_code = ? AND trade_date = ?
            """
            stock_data = pd.read_sql(stock_query, conn, params=[stk_code, trade_date])

            if stock_data.empty:
                conn.close()
                return 0

            stock_price = stock_data.iloc[0]['org_close']

            # 计算转股价值
            conv_value = (100.0 / conv_price) * stock_price

            # 获取强制赎回价格
            redeem_price = 0
            if hasattr(self, 'call_prices') and ts_code in self.call_prices:
                redeem_price = self.call_prices[ts_code]['call_price']
                if pd.isna(redeem_price):
                    redeem_price = 0

            conn.close()

            # 选择最优价格
            optimal_price = max(conv_value, redeem_price)
            optimal_price = optimal_price * stock_data.iloc[0]['close'] / stock_price
            return optimal_price

        except Exception as e:
            print(f"计算最优退出价格失败: {e}")
            return 0

    def _calculate_interest_income(self, ts_code, buy_date, sell_date):
        """
        计算持有期间的利息分配收益

        Args:
            ts_code: 债券代码
            buy_date: 买入日期，格式YYYYMMDD
            sell_date: 卖出日期，格式YYYYMMDD

        Returns:
            float: 持有期间的累积利息收益
        """
        try:
            # 获取或创建数据获取器实例（避免重复创建）
            if not hasattr(self, '_fetcher'):
                from data.fetcher import DataFetcher
                self._fetcher = DataFetcher()

            # 获取该债券在持有期间内的除息日和利息金额（回测模式）
            dividend_data = self._fetcher._get_dividend_dates_and_amounts(ts_code, backtest_mode=True)

            if not dividend_data:
                return 0.0

            # 计算持有期间内的累积利息
            total_interest = 0.0
            for dividend_date, interest_amount in dividend_data:
                # 只计算在持有期间内的利息分配
                # 买入日当天不算，卖出日当天算
                if buy_date < dividend_date <= sell_date:
                    total_interest += interest_amount

            return total_interest

        except Exception as e:
            print(f"计算 {ts_code} 持有期间利息收益失败: {e}")
            return 0.0

    def _execute_backtest(self, all_data, adjustment_dates, ascending):
        """执行回测"""
        daily_data = all_data.groupby('trade_date')
        
        # 初始化状态
        portfolio = {}  # {ts_code: {'quantity': quantity, 'buy_date': buy_date, 'buy_price': buy_price}}
        cash = self.initial_capital
        daily_values = []
        trades = []
        total_costs = 0
        
        print("开始执行回测...")
        
        all_dates = sorted(all_data['trade_date'].unique())
        
        for i, trade_date in enumerate(all_dates):
            if i % 50 == 0:
                print(f"回测进度: {i+1}/{len(all_dates)} - {trade_date}")
            
            # 获取当日数据
            day_data = daily_data.get_group(trade_date) if trade_date in daily_data.groups else pd.DataFrame()
            
            if day_data.empty:
                continue
            
            # 检查事件：强制赎回、退市等
            forced_redeem_bonds = day_data[day_data.get('force_redeem_flag', 0) == 1]['ts_code'].tolist()

            # 检查强制赎回事件（从cb_call表）
            call_redeem_bonds = self._check_call_events(trade_date)

            # 检查退市债券：如果债券在当日或之前退市，需要强制卖出
            delisted_bonds = []
            if 'delist_date' in day_data.columns:
                delisted_bonds = day_data[
                    (day_data['delist_date'].notna()) &
                    (day_data['delist_date'] != '') &
                    (day_data['delist_date'] <= trade_date)
                ]['ts_code'].tolist()

            # 合并强制卖出的债券
            forced_sell_bonds = list(set(forced_redeem_bonds + call_redeem_bonds + delisted_bonds))
            
            # 首先处理强制赎回债券（即使当天没有交易数据）
            for ts_code in call_redeem_bonds:
                if ts_code in portfolio and portfolio[ts_code]['quantity'] > 0:
                    position = portfolio[ts_code]
                    quantity = position['quantity']
                    buy_date = position['buy_date']

                    # 计算最优退出价格
                    optimal_price = self._calculate_optimal_exit_price(ts_code, trade_date)
                    if optimal_price > 0:
                        # 强制赎回，无交易费用
                        revenue = quantity * optimal_price

                        # 计算持有期间的利息收益
                        interest_income = self._calculate_interest_income(ts_code, buy_date, trade_date)
                        total_revenue = revenue + interest_income

                        cash += total_revenue

                        trades.append({
                            'date': f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:]}",
                            'ts_code': ts_code,
                            'action': 'forced_redeem',
                            'quantity': quantity,
                            'price': optimal_price,
                            'amount': revenue,
                            'interest_income': interest_income,
                            'total_amount': total_revenue,
                            'cost': 0,
                            'reason': 'force_redeem'
                        })

                        # 从持仓中移除
                        del portfolio[ts_code]

            # 计算当前持仓价值并处理其他强制事件
            portfolio_value = 0
            valid_positions = {}

            for ts_code, position in portfolio.items():
                bond_data = day_data[day_data['ts_code'] == ts_code]
                quantity = position['quantity']
                buy_date = position['buy_date']

                if quantity > 0:
                    # 获取估值价格：优先使用当日价格，否则查询最近价格
                    if not bond_data.empty:
                        # 使用当日净价进行估值
                        price = bond_data.iloc[0]['close']
                    else:
                        # 当日无数据，查询最近的有效价格
                        price = self._get_latest_price(ts_code, trade_date)
                        if price <= 0:
                            print(f"⚠️  {ts_code} 无法获取有效价格，跳过估值")
                            continue

                    if price > 0:
                        # 检查是否需要强制卖出（赎回或退市）
                        if ts_code in forced_sell_bonds:
                            # 确定卖出原因和价格
                            if ts_code in forced_redeem_bonds:
                                reason = 'force_redeem'
                            elif ts_code in delisted_bonds:
                                reason = 'delisted'
                            else:
                                reason = 'forced_sell'

                            # 强制卖出，立即卖出（强制赎回不收取交易费用）
                            if reason == 'force_redeem':
                                revenue = quantity * price  # 强制赎回无交易费用
                                cost = 0
                            else:
                                revenue = quantity * price * (1 - self.transaction_cost)
                                cost = quantity * price * self.transaction_cost

                            # 计算持有期间的利息收益
                            interest_income = self._calculate_interest_income(ts_code, buy_date, trade_date)
                            total_revenue = revenue + interest_income

                            cash += total_revenue
                            total_costs += cost

                            trades.append({
                                'date': f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:]}",
                                'ts_code': ts_code,
                                'action': 'forced_sell',
                                'quantity': quantity,
                                'price': price,
                                'amount': revenue,
                                'interest_income': interest_income,
                                'total_amount': total_revenue,
                                'cost': cost,
                                'reason': reason
                            })
                        else:
                            portfolio_value += quantity * price
                            valid_positions[ts_code] = position
            
            portfolio = valid_positions
            total_value = cash + portfolio_value
            
            # 记录净值
            daily_values.append({
                'date': f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:]}",
                'net_value': total_value / self.initial_capital,
                'total_value': total_value,
                'cash': cash,
                'portfolio_value': portfolio_value
            })
            
            # 检查是否需要调整持仓
            if trade_date in adjustment_dates:
                is_event_driven = trade_date not in self._get_rebalance_dates()
                reason = 'event_driven' if is_event_driven else 'regular_rebalance'
                
                # 执行调整，排除强制赎回的债券
                new_trades, new_costs, new_cash, new_portfolio = self._execute_rebalance(
                    day_data, portfolio, cash, trade_date, ascending, reason,
                    exclude_bonds=forced_sell_bonds
                )
                
                trades.extend(new_trades)
                total_costs += new_costs
                cash = new_cash
                portfolio = new_portfolio
        
        # 回测结束时进行虚拟平仓，确保所有持仓都被平仓
        final_trade_date = all_dates[-1] if all_dates else self.end_date.replace('-', '')
        if portfolio:
            print(f"回测结束，对 {len(portfolio)} 个持仓进行虚拟平仓...")
            final_day_data = daily_data.get_group(final_trade_date) if final_trade_date in daily_data.groups else pd.DataFrame()
            
            for ts_code, position in portfolio.items():
                quantity = position['quantity']
                buy_date = position['buy_date']
                
                if quantity > 0:
                    # 获取最终价格
                    bond_data = final_day_data[final_day_data['ts_code'] == ts_code] if not final_day_data.empty else pd.DataFrame()
                    if not bond_data.empty:
                        final_price = bond_data.iloc[0]['close']
                    else:
                        # 如果最后一天没有数据，获取最近的有效价格
                        final_price = self._get_latest_price(ts_code, final_trade_date)
                    
                    if final_price > 0:
                        # 虚拟卖出（不收取交易费用，因为是虚拟平仓）
                        revenue = quantity * final_price
                        
                        # 计算持有期间的利息收益
                        interest_income = self._calculate_interest_income(ts_code, buy_date, final_trade_date)
                        total_revenue = revenue + interest_income
                        
                        cash += total_revenue
                        
                        trades.append({
                            'date': f"{final_trade_date[:4]}-{final_trade_date[4:6]}-{final_trade_date[6:]}",
                            'ts_code': ts_code,
                            'action': 'final_liquidation',
                            'quantity': quantity,
                            'price': final_price,
                            'amount': revenue,
                            'interest_income': interest_income,
                            'total_amount': total_revenue,
                            'cost': 0,  # 虚拟平仓不收费用
                            'reason': 'final_liquidation'
                        })
            
            # 清空持仓
            portfolio.clear()
            
            # 更新最终净值
            final_total_value = cash
            if daily_values:
                daily_values[-1]['total_value'] = final_total_value
                daily_values[-1]['net_value'] = final_total_value / self.initial_capital
                daily_values[-1]['cash'] = cash
                daily_values[-1]['portfolio_value'] = 0
        
        print(f"回测完成，总交易次数: {len(trades)}, 总交易成本: {total_costs:,.0f}")
        
        return {
            'daily_values': daily_values,
            'trades': trades,
            'total_costs': total_costs
        }
    
    def _execute_rebalance(self, day_data, portfolio, cash, trade_date, ascending, reason, exclude_bonds=None):
        """执行再平衡"""
        trades = []
        total_costs = 0

        # 排除强制赎回的债券
        if exclude_bonds is None:
            exclude_bonds = []

        # 第一步：使用筛选器进行预筛选（如果设置了筛选器）
        if self.bond_filter is not None:
            try:
                filtered_data = self.bond_filter.filter_bonds(day_data, top_pct=0.2)
                if not filtered_data.empty:
                    day_data = filtered_data
            except Exception as e:
                print(f"警告：筛选器执行失败: {e}，使用原始数据")

        # 第二步：在筛选后的数据中选择优质因子债券（排名前X%），排除强制赎回债券
        factor_threshold = day_data['combined_factor'].quantile(1 - self.min_factor_rank_pct)
        quality_bonds = day_data[day_data['combined_factor'] >= factor_threshold].copy()

        # 排除强制赎回的债券
        if exclude_bonds:
            quality_bonds = quality_bonds[~quality_bonds['ts_code'].isin(exclude_bonds)]

        if ascending:
            quality_bonds = quality_bonds.sort_values('combined_factor', ascending=True)
        else:
            quality_bonds = quality_bonds.sort_values('combined_factor', ascending=False)

        # 当前持仓
        current_holdings = set(portfolio.keys())
        
        # 目标持仓：优先保留当前持仓中的优质债券，然后补充新的债券
        # 第一步：在当前持仓中筛选出仍然符合条件的债券
        current_quality_bonds = []
        for ts_code in current_holdings:
            if ts_code in quality_bonds['ts_code'].values and ts_code not in exclude_bonds:
                current_quality_bonds.append(ts_code)
        
        # 第二步：从剩余的优质债券中选择新债券，补足到N个
        remaining_slots = self.strategy.n - len(current_quality_bonds)
        if remaining_slots > 0:
            # 排除当前持仓和强制赎回债券
            available_bonds = quality_bonds[
                (~quality_bonds['ts_code'].isin(current_holdings)) &
                (~quality_bonds['ts_code'].isin(exclude_bonds))
            ]
            new_bonds = available_bonds.head(remaining_slots)['ts_code'].tolist()
        else:
            new_bonds = []
            # 如果当前优质持仓超过N个，需要卖出表现最差的
            if len(current_quality_bonds) > self.strategy.n:
                # 按因子值排序，保留表现最好的N个
                current_quality_data = quality_bonds[quality_bonds['ts_code'].isin(current_quality_bonds)]
                current_quality_data = current_quality_data.sort_values('combined_factor', ascending=not ascending)
                current_quality_bonds = current_quality_data.head(self.strategy.n)['ts_code'].tolist()
        
        target_bonds = current_quality_bonds + new_bonds
        target_holdings = set(target_bonds)
        
        # 卖出不在目标中的债券 - 应用策略K限制
        to_sell_candidates = current_holdings - target_holdings
        
        # 应用策略的K限制：每日最多卖出K个
        if len(to_sell_candidates) > self.strategy.k:
            # 按因子值排序，优先卖出表现最差的K个
            to_sell_data = day_data[day_data['ts_code'].isin(to_sell_candidates)]
            if not to_sell_data.empty:
                # 按因子值排序（升序，表现差的在前）
                to_sell_data = to_sell_data.sort_values('combined_factor', ascending=ascending)
                to_sell = set(to_sell_data.head(self.strategy.k)['ts_code'].tolist())
            else:
                to_sell = set(list(to_sell_candidates)[:self.strategy.k])
        else:
            to_sell = to_sell_candidates
        
        for ts_code in to_sell:
            if ts_code in portfolio and portfolio[ts_code]['quantity'] > 0:
                bond_data = day_data[day_data['ts_code'] == ts_code]
                if not bond_data.empty:
                    # 使用净价进行交易
                    price = bond_data.iloc[0]['close']
                    if pd.isna(price) or price <= 0:
                        # 如果净价无效，尝试使用最近的有效价格
                        price = self._get_latest_price(ts_code, trade_date)
                        if pd.isna(price) or price <= 0:
                            print(f"警告：债券 {ts_code} 无有效价格，跳过卖出")
                            continue

                    position = portfolio[ts_code]
                    quantity = position['quantity']
                    buy_date = position['buy_date']

                    revenue = quantity * price * (1 - self.transaction_cost)
                    cost = quantity * price * self.transaction_cost

                    # 计算持有期间的利息收益
                    interest_income = self._calculate_interest_income(ts_code, buy_date, trade_date)
                    total_revenue = revenue + interest_income

                    total_costs += cost
                    cash += total_revenue

                    trades.append({
                        'date': f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:]}",
                        'ts_code': ts_code,
                        'action': 'sell',
                        'quantity': quantity,
                        'price': price,
                        'amount': revenue,
                        'interest_income': interest_income,
                        'total_amount': total_revenue,
                        'cost': cost,
                        'reason': reason
                    })

                    del portfolio[ts_code]
        
        # 买入新的目标债券 - 应用策略K限制
        to_buy_candidates = target_holdings - current_holdings
        
        # 判断是否为初始建仓（当前持仓为空或很少）
        is_initial_position = len(current_holdings) < self.strategy.n // 2
        
        if is_initial_position:
            # 初始建仓：买入全部目标债券，不受K限制
            to_buy = to_buy_candidates
        else:
            # 常规调仓：应用策略的K限制，每日最多买入K个
            if len(to_buy_candidates) > self.strategy.k:
                # 按因子值排序，优先买入表现最好的K个
                to_buy_data = day_data[day_data['ts_code'].isin(to_buy_candidates)]
                if not to_buy_data.empty:
                    # 按因子值排序（降序，表现好的在前）
                    to_buy_data = to_buy_data.sort_values('combined_factor', ascending=not ascending)
                    to_buy = set(to_buy_data.head(self.strategy.k)['ts_code'].tolist())
                else:
                    to_buy = set(list(to_buy_candidates)[:self.strategy.k])
            else:
                to_buy = to_buy_candidates
        
        if to_buy and cash > 10000:  # 保留1万现金
            equal_weight = (cash - 10000) / len(to_buy)
            
            for ts_code in to_buy:
                # 确保不会重复买入已持有的标的
                if ts_code in portfolio:
                    print(f"警告：尝试买入已持有的标的 {ts_code}，跳过")
                    continue
                    
                bond_data = quality_bonds[quality_bonds['ts_code'] == ts_code]
                if not bond_data.empty and equal_weight > 1000:
                    # 使用净价进行交易
                    price = bond_data.iloc[0]['close']
                    quantity = int(equal_weight / price / 10) * 10  # 最小交易单位

                    if quantity > 0:
                        total_cost = quantity * price * (1 + self.transaction_cost)
                        if cash >= total_cost:
                            cost = quantity * price * self.transaction_cost
                            total_costs += cost
                            cash -= total_cost

                            # 更新持仓信息，记录买入日期和价格
                            portfolio[ts_code] = {
                                'quantity': quantity,
                                'buy_date': trade_date,
                                'buy_price': price
                            }

                            trades.append({
                                'date': f"{trade_date[:4]}-{trade_date[4:6]}-{trade_date[6:]}",
                                'ts_code': ts_code,
                                'action': 'buy',
                                'quantity': quantity,
                                'price': price,
                                'amount': total_cost,
                                'cost': cost,
                                'reason': reason
                            })
        
        return trades, total_costs, cash, portfolio
    
    def _calculate_performance(self, results):
        """计算性能指标"""
        daily_values = results['daily_values']
        trades = results['trades']
        total_costs = results['total_costs']
        
        if not daily_values:
            return {}
        
        df = pd.DataFrame(daily_values)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # 计算收益率
        df['returns'] = df['net_value'].pct_change()
        
        # 基础指标
        total_return = df['net_value'].iloc[-1] - 1.0
        days = len(df)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0
        
        # 风险指标
        volatility = df['returns'].std() * np.sqrt(252) if len(df) > 1 else 0
        sharpe_ratio = (annual_return - 0.03) / volatility if volatility > 0 else 0
        
        # 最大回撤
        df['peak'] = df['net_value'].cummax()
        df['drawdown'] = (df['net_value'] - df['peak']) / df['peak']
        max_drawdown = df['drawdown'].min()
        
        # 胜率
        win_rate = (df['returns'] > 0).mean() if len(df) > 1 else 0
        
        # 交易成本分析
        avg_cost_ratio = total_costs / self.initial_capital if self.initial_capital > 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(trades),
            'trading_days': len(df),
            'avg_cost_ratio': avg_cost_ratio,
            'total_transaction_cost': total_costs,
            'cost_ratio': avg_cost_ratio
        }
    
    def _generate_report(self, results, performance):
        """生成回测报告"""
        try:
            # 确保报告目录存在
            reports_dir = os.path.join(config_manager.root_dir, 'reports')
            os.makedirs(reports_dir, exist_ok=True)
            
            # 生成报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(reports_dir, f'optimized_backtest_report_{timestamp}.html')
            
            # 生成HTML报告
            self._generate_html_report(results, performance, report_file)
            
            print(f"回测报告已生成: {report_file}")
            return report_file
        except Exception as e:
            print(f"生成报告失败: {e}")
            return None
    
    def _generate_html_report(self, results, performance, report_file):
        """生成HTML格式的回测报告"""
        daily_values = results['daily_values']
        trades = results['trades']
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>优化版可转债回测报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }}
                .metric {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; min-width: 200px; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #333; }}
                .metric-label {{ color: #666; margin-top: 5px; }}
                .highlight {{ color: #e74c3c; font-weight: bold; }}
                .positive {{ color: #27ae60; }}
                .negative {{ color: #e74c3c; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 优化版可转债量化回测报告</h1>
                <p>回测期间: {self.start_date} 至 {self.end_date}</p>
                <p>策略: TopNDropoutK (N={self.strategy.n}, K={self.strategy.k})</p>
                <p>调整频率: {self.rebalance_frequency}</p>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <h2>📊 核心绩效指标</h2>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value {'positive' if performance.get('total_return', 0) > 0 else 'negative'}">{performance.get('total_return', 0):.2%}</div>
                    <div class="metric-label">总收益率</div>
                </div>
                <div class="metric">
                    <div class="metric-value {'positive' if performance.get('annual_return', 0) > 0 else 'negative'}">{performance.get('annual_return', 0):.2%}</div>
                    <div class="metric-label">年化收益率</div>
                </div>
                <div class="metric">
                    <div class="metric-value negative">{performance.get('max_drawdown', 0):.2%}</div>
                    <div class="metric-label">最大回撤</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{performance.get('sharpe_ratio', 0):.2f}</div>
                    <div class="metric-label">夏普比率</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{performance.get('volatility', 0):.2%}</div>
                    <div class="metric-label">年化波动率</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{performance.get('win_rate', 0):.1%}</div>
                    <div class="metric-label">胜率</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{performance.get('total_trades', 0)}</div>
                    <div class="metric-label">总交易次数</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{performance.get('avg_cost_ratio', 0):.2%}</div>
                    <div class="metric-label">交易成本占比</div>
                </div>
            </div>
            
            <h2>💡 优化特性</h2>
            <ul>
                <li><strong>智能调整频率</strong>: {self.rebalance_frequency}调整，大幅降低交易成本</li>
                <li><strong>事件驱动调整</strong>: 强制转股、转换价变动时立即调整</li>
                <li><strong>严格因子筛选</strong>: 只选择前{self.min_factor_rank_pct*100}%的优质债券</li>
                <li><strong>最优策略参数</strong>: N={self.strategy.n}, K={self.strategy.k}</li>
                <li><strong>多精度计算</strong>: {self.precision_level}精度</li>
            </ul>
            
            <h2>📈 交易记录统计</h2>
            <p>总交易次数: {len(trades)}</p>
            <p>买入次数: {len([t for t in trades if t['action'] == 'buy'])}</p>
            <p>卖出次数: {len([t for t in trades if t['action'] == 'sell'])}</p>
            <p>强制卖出次数: {len([t for t in trades if t['action'] == 'forced_sell'])}</p>
            <p>总交易成本: {performance.get('total_transaction_cost', 0):,.0f} 元</p>
            
        </body>
        </html>
        """
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _calculate_market_statistics(self, all_data):
        """计算市场统计信息（包括平均收益和特殊事件）"""
        try:
            print("计算市场统计信息...")
            
            # 计算每只可转债的收益率
            cb_returns = []
            conversion_count = 0
            redemption_count = 0
            
            # 按债券分组计算收益率
            for ts_code in all_data['ts_code'].unique():
                bond_data = all_data[all_data['ts_code'] == ts_code].sort_values('trade_date')
                if len(bond_data) > 1:
                    # 计算总收益率（考虑转股和强赎的情况）
                    start_price = bond_data['close'].iloc[0]
                    end_price = bond_data['close'].iloc[-1]
                    
                    # 检查是否有转股事件（转股价值大于债券价格）
                    if 'conv_value' in bond_data.columns:
                        conv_events = bond_data[bond_data['conv_value'] > bond_data['close'] * 1.3]
                        if len(conv_events) > 0:
                            conversion_count += 1
                            # 如果有转股事件，使用转股价值
                            end_price = conv_events['conv_value'].iloc[-1]
                    
                    # 检查是否有强赎事件（价格>强赎价格）
                    if ts_code in self.call_prices and len(self.call_prices[ts_code]) > 0:
                        for call_date, call_price in self.call_prices[ts_code].items():
                            if call_date in bond_data['trade_date'].values:
                                redemption_count += 1
                                # 如果有强赎，使用强赎价格
                                end_price = call_price
                                break
                    
                    if start_price > 0:
                        total_return = (end_price - start_price) / start_price
                        cb_returns.append(total_return)
            
            if cb_returns:
                avg_cb_return = np.mean(cb_returns)
                cb_return_std = np.std(cb_returns)
                
                return {
                    'avg_cb_return': avg_cb_return,
                    'cb_return_std': cb_return_std,
                    'conversion_count': conversion_count,
                    'redemption_count': redemption_count,
                    'total_bonds': len(cb_returns)
                }
            else:
                return {}
                
        except Exception as e:
            print(f"计算市场统计信息失败: {e}")
            return {}
    
    def _calculate_ic_statistics(self, all_data):
        """计算IC统计信息（预测值与实际收益的相关性）"""
        try:
            print("计算IC统计信息...")
            
            # 确定预测值列名
            prediction_column = None
            model_type = None
            
            if self.ml_model and 'ml_predicted_return' in all_data.columns:
                prediction_column = 'ml_predicted_return'
                model_type = 'ML模型'
            elif self.ft_model and 'ft_predicted_return' in all_data.columns:
                prediction_column = 'ft_predicted_return'
                model_type = 'FT-Transformer模型'
            elif self.lgbm_model and 'lgbm_predicted_return' in all_data.columns:
                prediction_column = 'lgbm_predicted_return'
                model_type = 'LightGBM模型'
            elif self.linear_model and 'combined_factor' in all_data.columns:
                prediction_column = 'combined_factor'
                model_type = '线性模型(预测值已赋值到combined_factor)'
            
            if prediction_column is None:
                print("未找到预测值，跳过IC计算")
                return {}
            
            print(f"使用{model_type}预测值计算IC: {prediction_column}")
                
            ic_results = {}
            
            # 计算不同时间周期的IC值
            for days in [1, 5, 10]:
                try:
                    # 计算实际收益率
                    all_data = all_data.sort_values(['ts_code', 'trade_date'])
                    all_data[f'actual_return_{days}d'] = all_data.groupby('ts_code')['close'].pct_change(periods=days).shift(-days)
                    
                    # 过滤有效数据
                    valid_data = all_data[
                        (all_data[prediction_column].notna()) & 
                        (all_data[f'actual_return_{days}d'].notna()) &
                        (all_data[prediction_column].abs() < 1) &  # 过滤异常预测值
                        (all_data[f'actual_return_{days}d'].abs() < 1)  # 过滤异常收益率
                    ]
                    
                    if len(valid_data) > 10:  # 至少需要10个样本
                        # 计算IC值（皮尔逊相关系数）
                        ic_value = np.corrcoef(
                            valid_data[prediction_column],
                            valid_data[f'actual_return_{days}d']
                        )[0, 1]
                        
                        if not np.isnan(ic_value):
                            ic_results[f'ic_{days}d'] = ic_value
                            print(f"{days}日IC值: {ic_value:.4f} (样本数: {len(valid_data)})")
                    else:
                        print(f"{days}日收益IC计算跳过，有效样本数不足: {len(valid_data)}")
                        
                except Exception as e:
                    print(f"计算{days}日IC失败: {e}")
                    continue
            
            return ic_results
            
        except Exception as e:
            print(f"计算IC统计信息失败: {e}")
            return {}


# 为了兼容性，保留一些原有的类结构
class TradingSimulator:
    """兼容性类，实际功能已集成到Backtester中"""
    def __init__(self, initial_capital=1000000.0):
        self.initial_capital = initial_capital
        print("注意：TradingSimulator已被优化集成，请直接使用Backtester类")


class PerformanceAnalyzer:
    """兼容性类，实际功能已集成到Backtester中"""
    def __init__(self):
        print("注意：PerformanceAnalyzer已被优化集成，请直接使用Backtester类")


class ReportGenerator:
    """兼容性类，实际功能已集成到Backtester中"""
    def __init__(self):
        print("注意：ReportGenerator已被优化集成，请直接使用Backtester类")
