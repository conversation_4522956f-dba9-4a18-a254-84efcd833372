#!/usr/bin/env python3
"""
处理IC值为负时的回测策略
当因子IC值为负时，采用反向策略或调整权重
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sqlite3
from config.config import config_manager


class NegativeICStrategy:
    """处理IC值为负的策略类"""
    
    def __init__(self, ic_threshold=-0.05, lookback_days=60):
        """
        初始化负IC策略
        
        Args:
            ic_threshold: IC阈值，低于此值时启用反向策略
            lookback_days: 回看天数，用于计算滚动IC
        """
        self.ic_threshold = ic_threshold
        self.lookback_days = lookback_days
        self.ic_history = {}  # 存储历史IC值
        
    def calculate_rolling_ic(self, factor_data, price_data, factor_name='arbitrage_space'):
        """
        计算滚动IC值
        
        Args:
            factor_data: 因子数据
            price_data: 价格数据
            factor_name: 因子名称
            
        Returns:
            滚动IC序列
        """
        # 按日期分组计算IC
        daily_ic = {}
        
        # 获取所有交易日期
        dates = sorted(factor_data['trade_date'].unique())
        
        for i, date in enumerate(dates):
            if i < self.lookback_days:
                continue
                
            # 获取回看期间的数据
            start_idx = max(0, i - self.lookback_days)
            lookback_dates = dates[start_idx:i]
            
            # 计算期间IC
            ic_value = self._calculate_period_ic(
                factor_data, price_data, lookback_dates, factor_name
            )
            
            if ic_value is not None:
                daily_ic[date] = ic_value
                
        return pd.Series(daily_ic)
    
    def _calculate_period_ic(self, factor_data, price_data, dates, factor_name):
        """计算指定期间的IC值"""
        try:
            # 筛选期间数据
            period_factor = factor_data[factor_data['trade_date'].isin(dates)].copy()
            
            if len(period_factor) == 0:
                return None
            
            # 计算收益率
            period_factor['trade_date'] = pd.to_datetime(period_factor['trade_date'], format='%Y%m%d')
            
            # 合并价格数据计算收益
            results = []
            for _, row in period_factor.iterrows():
                ts_code = row['ts_code']
                trade_date = row['trade_date']
                factor_value = row[factor_name]
                
                # 查找下一日价格计算收益
                next_date = trade_date + pd.Timedelta(days=1)
                current_price = price_data.get((trade_date, ts_code))
                next_price = price_data.get((next_date, ts_code))
                
                if current_price and next_price and not pd.isna(factor_value):
                    return_1d = (next_price - current_price) / current_price
                    if np.isfinite(return_1d):
                        results.append({
                            'factor_value': factor_value,
                            'return_1d': return_1d
                        })
            
            if len(results) < 10:  # 至少需要10个样本
                return None
                
            results_df = pd.DataFrame(results)
            ic_value = results_df['factor_value'].corr(results_df['return_1d'])
            
            return ic_value if not pd.isna(ic_value) else None
            
        except Exception as e:
            print(f"计算IC失败: {e}")
            return None
    
    def should_reverse_strategy(self, current_date, factor_data, price_data):
        """
        判断是否应该采用反向策略
        
        Args:
            current_date: 当前日期
            factor_data: 因子数据
            price_data: 价格数据
            
        Returns:
            bool: 是否采用反向策略
        """
        # 计算当前的滚动IC
        rolling_ic = self.calculate_rolling_ic(factor_data, price_data)
        
        if current_date in rolling_ic.index:
            current_ic = rolling_ic[current_date]
            
            # 记录IC历史
            self.ic_history[current_date] = current_ic
            
            # 判断是否需要反向
            if current_ic < self.ic_threshold:
                print(f"日期 {current_date}: IC值 {current_ic:.4f} < 阈值 {self.ic_threshold}, 启用反向策略")
                return True
            else:
                print(f"日期 {current_date}: IC值 {current_ic:.4f} >= 阈值 {self.ic_threshold}, 使用正向策略")
                return False
        
        return False
    
    def adjust_factor_direction(self, factor_data, reverse=False):
        """
        调整因子方向
        
        Args:
            factor_data: 因子数据
            reverse: 是否反向
            
        Returns:
            调整后的因子数据
        """
        adjusted_data = factor_data.copy()
        
        if reverse:
            # 反向因子：将因子值取负
            factor_columns = ['arbitrage_space', 'combined_factor']
            for col in factor_columns:
                if col in adjusted_data.columns:
                    adjusted_data[col] = -adjusted_data[col]
                    
        return adjusted_data
    
    def get_strategy_weights(self, current_ic, base_weights):
        """
        根据IC值调整策略权重
        
        Args:
            current_ic: 当前IC值
            base_weights: 基础权重
            
        Returns:
            调整后的权重
        """
        adjusted_weights = base_weights.copy()
        
        # 根据IC绝对值调整权重强度
        ic_strength = abs(current_ic)
        
        if current_ic < 0:
            # IC为负时，降低权重或反向
            if ic_strength > 0.1:  # 强负相关
                # 完全反向
                for key in adjusted_weights:
                    adjusted_weights[key] *= -1
            elif ic_strength > 0.05:  # 中等负相关
                # 降低权重
                for key in adjusted_weights:
                    adjusted_weights[key] *= 0.5
            else:  # 弱负相关
                # 轻微降低权重
                for key in adjusted_weights:
                    adjusted_weights[key] *= 0.8
        else:
            # IC为正时，根据强度调整权重
            if ic_strength > 0.1:  # 强正相关
                # 增强权重
                for key in adjusted_weights:
                    adjusted_weights[key] *= 1.5
            elif ic_strength > 0.05:  # 中等正相关
                # 保持权重
                pass
            else:  # 弱正相关
                # 轻微降低权重
                for key in adjusted_weights:
                    adjusted_weights[key] *= 0.9
                    
        return adjusted_weights


class AdaptiveBacktester:
    """自适应回测器，能够根据IC值调整策略"""
    
    def __init__(self, negative_ic_strategy=None):
        """
        初始化自适应回测器
        
        Args:
            negative_ic_strategy: 负IC策略对象
        """
        self.negative_ic_strategy = negative_ic_strategy or NegativeICStrategy()
        self.strategy_history = []  # 记录策略调整历史
        
    def run_adaptive_backtest(self, start_date, end_date, initial_capital=1000000):
        """
        运行自适应回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            
        Returns:
            回测结果
        """
        print(f"开始自适应回测: {start_date} - {end_date}")
        
        # 加载数据
        factor_data, price_data = self._load_data(start_date, end_date)
        
        if factor_data.empty:
            print("因子数据为空")
            return None
            
        # 执行回测
        results = self._execute_adaptive_backtest(
            factor_data, price_data, initial_capital
        )
        
        return results
    
    def _load_data(self, start_date, end_date):
        """加载回测数据"""
        conn = sqlite3.connect(config_manager.get_db_path())
        
        # 加载因子数据
        factor_query = f'''
        SELECT trade_date, ts_code, arbitrage_space, combined_factor
        FROM cb_factor 
        WHERE trade_date >= '{start_date}' AND trade_date <= '{end_date}'
        AND arbitrage_space IS NOT NULL
        ORDER BY trade_date, ts_code
        '''
        
        factor_data = pd.read_sql(factor_query, conn)
        
        # 加载价格数据
        price_query = f'''
        SELECT trade_date, ts_code, close
        FROM cb_daily 
        WHERE trade_date >= '{start_date}' AND trade_date <= '{end_date}'
        ORDER BY trade_date, ts_code
        '''
        
        price_data = pd.read_sql(price_query, conn)
        conn.close()
        
        # 转换价格数据为字典格式便于查找
        price_dict = {}
        for _, row in price_data.iterrows():
            date = pd.to_datetime(row['trade_date'], format='%Y%m%d')
            price_dict[(date, row['ts_code'])] = row['close']
            
        return factor_data, price_dict
    
    def _execute_adaptive_backtest(self, factor_data, price_data, initial_capital):
        """执行自适应回测逻辑"""
        # 获取所有交易日期
        dates = sorted(factor_data['trade_date'].unique())
        
        portfolio_values = []
        current_capital = initial_capital
        
        for i, date in enumerate(dates[60:], 60):  # 从第60天开始，确保有足够历史数据
            print(f"处理日期: {date} ({i+1}/{len(dates)})")
            
            # 获取当日数据
            daily_factor = factor_data[factor_data['trade_date'] == date].copy()
            
            if daily_factor.empty:
                continue
                
            # 判断是否需要反向策略
            should_reverse = self.negative_ic_strategy.should_reverse_strategy(
                date, factor_data[:i*100], price_data  # 使用历史数据
            )
            
            # 调整因子方向
            adjusted_factor = self.negative_ic_strategy.adjust_factor_direction(
                daily_factor, reverse=should_reverse
            )
            
            # 记录策略调整
            self.strategy_history.append({
                'date': date,
                'reverse_strategy': should_reverse,
                'ic_value': self.negative_ic_strategy.ic_history.get(date, np.nan)
            })
            
            # 计算当日组合价值（简化版）
            portfolio_values.append({
                'date': date,
                'value': current_capital,
                'strategy': 'reverse' if should_reverse else 'normal'
            })
        
        return {
            'portfolio_values': portfolio_values,
            'strategy_history': self.strategy_history
        }


def test_negative_ic_strategy():
    """测试负IC策略"""
    print("=== 测试负IC策略 ===")
    
    # 创建策略对象
    strategy = NegativeICStrategy(ic_threshold=-0.02, lookback_days=30)
    
    # 创建自适应回测器
    backtester = AdaptiveBacktester(strategy)
    
    # 运行回测（使用2024年数据，因为IC为负）
    results = backtester.run_adaptive_backtest('20240101', '20241231')
    
    if results:
        print("回测完成")
        print(f"策略调整次数: {len(results['strategy_history'])}")
        
        # 统计策略使用情况
        reverse_count = sum(1 for h in results['strategy_history'] if h['reverse_strategy'])
        total_count = len(results['strategy_history'])
        
        print(f"反向策略使用比例: {reverse_count}/{total_count} ({reverse_count/total_count*100:.1f}%)")
    else:
        print("回测失败")


if __name__ == "__main__":
    test_negative_ic_strategy()
