"""
日志模块，负责日志记录和错误处理
"""
import os
import logging
from datetime import datetime
import traceback
import json
import sys

# 导入配置管理器
from config.config import config_manager

class Logger:
    """日志记录类，负责记录日志"""
    
    def __init__(self, name='convertible_bond_quant', level=None):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称，默认为'convertible_bond_quant'
            level: 日志级别，默认为None，表示使用配置中的默认值
        """
        # 设置日志级别
        if level is None:
            level = config_manager.get_log_level()
        
        # 转换日志级别字符串为常量
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        level = level_map.get(level, logging.INFO)
        
        # 设置日志目录
        self.log_dir = config_manager.get_log_dir()
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 创建日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 清除已有的处理器
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 创建文件处理器
        log_file = os.path.join(self.log_dir, f"{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        
        # 创建格式化器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def debug(self, message):
        """
        记录调试级别日志
        
        Args:
            message: 日志消息
        """
        self.logger.debug(message)
    
    def info(self, message):
        """
        记录信息级别日志
        
        Args:
            message: 日志消息
        """
        self.logger.info(message)
    
    def warning(self, message):
        """
        记录警告级别日志
        
        Args:
            message: 日志消息
        """
        self.logger.warning(message)
    
    def error(self, message):
        """
        记录错误级别日志
        
        Args:
            message: 日志消息
        """
        self.logger.error(message)
    
    def critical(self, message):
        """
        记录严重错误级别日志
        
        Args:
            message: 日志消息
        """
        self.logger.critical(message)
    
    def exception(self, message):
        """
        记录异常日志
        
        Args:
            message: 日志消息
        """
        self.logger.exception(message)


class ErrorHandler:
    """错误处理类，负责处理错误和异常"""
    
    def __init__(self):
        """初始化错误处理器"""
        # 创建日志记录器
        self.logger = Logger(name='error_handler')
    
    def handle_error(self, error, context=None):
        """
        处理错误
        
        Args:
            error: 错误对象
            context: 错误上下文，默认为None
        
        Returns:
            错误处理结果
        """
        # 获取错误信息
        error_type = type(error).__name__
        error_message = str(error)
        error_traceback = traceback.format_exc()
        
        # 记录错误日志
        self.logger.error(f"错误类型: {error_type}")
        self.logger.error(f"错误信息: {error_message}")
        self.logger.error(f"错误堆栈: {error_traceback}")
        
        if context:
            self.logger.error(f"错误上下文: {context}")
        
        # 返回错误处理结果
        return {
            'error_type': error_type,
            'error_message': error_message,
            'error_traceback': error_traceback,
            'context': context
        }
    
    def handle_exception(self, func):
        """
        异常处理装饰器
        
        Args:
            func: 被装饰的函数
        
        Returns:
            装饰后的函数
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': args,
                    'kwargs': kwargs
                }
                return self.handle_error(e, context)
        
        return wrapper


class DocumentGenerator:
    """文档生成类，负责生成操作文档"""
    
    def __init__(self):
        """初始化文档生成器"""
        # 设置文档目录
        self.doc_dir = os.path.join(config_manager.root_dir, 'docs')
        os.makedirs(self.doc_dir, exist_ok=True)
    
    def generate_usage_doc(self):
        """
        生成使用文档
        
        Returns:
            文档文件路径
        """
        # 构建文档文件路径
        doc_file = os.path.join(self.doc_dir, 'usage.md')
        
        # 生成文档内容
        content = """# 可转债量化投资工具使用文档

## 1. 简介

本工具是一个基于ZL模型的可转债量化投资工具，可以帮助投资者进行可转债的量化投资。

## 2. 安装

### 2.1 环境要求

- Python 3.6+
- 依赖包：见requirements.txt

### 2.2 安装步骤

1. 克隆代码库
2. 安装依赖包：`pip install -r requirements.txt`
3. 配置环境变量：复制`config/.env.example`为`config/.env`，并填写相关配置

## 3. 配置

### 3.1 环境变量

在`config/.env`文件中配置以下环境变量：

- `TUSHARE_TOKEN`：Tushare API Token
- `DB_PATH`：数据库路径
- `CACHE_DIR`：缓存目录
- `LOG_LEVEL`：日志级别
- `LOG_DIR`：日志目录
- `DEFAULT_START_DATE`：默认开始日期
- `DEFAULT_END_DATE`：默认结束日期
- `DEFAULT_STRATEGY`：默认策略
- `DEFAULT_N`：默认N值
- `DEFAULT_K`：默认K值
- `DEFAULT_SIMULATION_PATHS`：默认模拟路径数
- `DEFAULT_TIME_STEPS`：默认时间步数
- `DEFAULT_RISK_FREE_RATE`：默认无风险利率

## 4. 使用方法

### 4.1 数据获取

```bash
# 获取指定时间段的数据
python main.py --mode data --action fetch --start_date 2020-01-01 --end_date 2025-06-06

# 更新数据到最新
python main.py --mode data --action update
```

### 4.2 因子计算

```bash
# 计算指定日期的因子
python main.py --mode factor --date 2025-06-06

# 计算指定时间段的因子
python main.py --mode factor --start_date 2020-01-01 --end_date 2025-06-06
```

### 4.3 回测

```bash
# 使用默认参数进行回测
python main.py --mode backtest --start_date 2020-01-01 --end_date 2025-06-06

# 使用自定义参数进行回测
python main.py --mode backtest --start_date 2020-01-01 --end_date 2025-06-06 --strategy topndropoutk --params "n=10,k=2"
```

### 4.4 实时模拟交易

```bash
# 使用默认参数进行实时模拟交易
python main.py --mode realtime

# 使用自定义参数进行实时模拟交易
python main.py --mode realtime --strategy topndropoutk --params "n=10,k=2"
```

### 4.5 参数优化

```bash
# 进行参数优化
python main.py --mode optimize --start_date 2020-01-01 --end_date 2025-06-06 --strategy topndropoutk --param_range "n=5:15:1,k=1:5:1"
```

## 5. 模块说明

### 5.1 数据模块

数据模块负责从tushare获取数据并缓存到本地，包括：

- 可转债基本信息
- 可转债日行情数据
- 正股日行情数据
- 可转债转股价调整记录
- 可转债强赎记录
- 可转债回售记录

### 5.2 因子模块

因子模块负责计算各种因子，包括：

- ZL模型理论价格
- 套利空间因子
- 价格动量因子
- RSI因子
- MACD因子
- 成交量变化因子

### 5.3 策略模块

策略模块实现了topndropoutk策略，即始终持有最佳的n个可转债，每天最多换出k个。

### 5.4 回测模块

回测模块负责进行历史数据回测，支持T+1交易规则。

### 5.5 实时模拟交易模块

实时模拟交易模块负责进行实时模拟交易，包括实时数据获取、因子计算、信号生成和交易执行。

## 6. 注意事项

- 本工具仅供学习和研究使用，不构成投资建议
- 实际投资需要考虑交易成本、滑点等因素
- 历史业绩不代表未来表现

## 7. 常见问题

### 7.1 如何获取Tushare API Token？

访问[Tushare官网](https://tushare.pro/)注册账号并获取API Token。

### 7.2 如何自定义策略？

可以通过继承`strategy.topndropoutk.TopNDropoutKStrategy`类并重写相关方法来自定义策略。

### 7.3 如何添加新的因子？

可以在`factor.zl_model.py`中添加新的因子计算方法，并在`FactorCalculator`类中调用。

## 8. 联系方式

如有问题或建议，请联系开发者。
"""
        
        # 保存文档
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return doc_file
    
    def generate_api_doc(self):
        """
        生成API文档
        
        Returns:
            文档文件路径
        """
        # 构建文档文件路径
        doc_file = os.path.join(self.doc_dir, 'api.md')
        
        # 生成文档内容
        content = """# 可转债量化投资工具API文档

## 1. 数据模块API

### 1.1 DataFetcher

#### 1.1.1 fetch_cb_basic()

获取可转债基本信息。

**返回值**：可转债基本信息DataFrame

#### 1.1.2 fetch_cb_daily(ts_code=None, start_date=None, end_date=None)

获取可转债日行情数据。

**参数**：
- ts_code：可转债代码，如果为None则获取所有可转债
- start_date：开始日期，格式YYYYMMDD
- end_date：结束日期，格式YYYYMMDD

**返回值**：可转债日行情数据DataFrame

#### 1.1.3 fetch_stock_daily(ts_code=None, start_date=None, end_date=None)

获取正股日行情数据。

**参数**：
- ts_code：股票代码，如果为None则获取所有可转债对应的正股
- start_date：开始日期，格式YYYYMMDD
- end_date：结束日期，格式YYYYMMDD

**返回值**：正股日行情数据DataFrame

#### 1.1.4 fetch_cb_price_chg(ts_code=None)

获取可转债转股价调整记录。

**参数**：
- ts_code：可转债代码，如果为None则获取所有可转债

**返回值**：可转债转股价调整记录DataFrame

#### 1.1.5 fetch_all_data(start_date=None, end_date=None)

获取所有数据。

**参数**：
- start_date：开始日期，格式YYYY-MM-DD
- end_date：结束日期，格式YYYY-MM-DD

**返回值**：无

#### 1.1.6 update_data()

更新数据到最新日期。

**返回值**：无

### 1.2 DataCache

#### 1.2.1 get_cb_basic()

获取可转债基本信息。

**返回值**：可转债基本信息DataFrame

#### 1.2.2 get_cb_daily(ts_code=None, start_date=None, end_date=None)

获取可转债日行情数据。

**参数**：
- ts_code：可转债代码，如果为None则获取所有可转债
- start_date：开始日期，格式YYYY-MM-DD
- end_date：结束日期，格式YYYY-MM-DD

**返回值**：可转债日行情数据DataFrame

#### 1.2.3 get_stock_daily(ts_code=None, start_date=None, end_date=None)

获取正股日行情数据。

**参数**：
- ts_code：股票代码，如果为None则获取所有正股
- start_date：开始日期，格式YYYY-MM-DD
- end_date：结束日期，格式YYYY-MM-DD

**返回值**：正股日行情数据DataFrame

#### 1.2.4 get_cb_price_chg(ts_code=None)

获取可转债转股价调整记录。

**参数**：
- ts_code：可转债代码，如果为None则获取所有可转债

**返回值**：可转债转股价调整记录DataFrame

#### 1.2.5 get_cb_factor(ts_code=None, trade_date=None)

获取可转债因子数据。

**参数**：
- ts_code：可转债代码，如果为None则获取所有可转债
- trade_date：交易日期，格式YYYY-MM-DD，如果为None则获取最新日期

**返回值**：可转债因子数据DataFrame

#### 1.2.6 save_factor(df)

保存因子数据。

**参数**：
- df：因子数据DataFrame

**返回值**：无

## 2. 因子模块API

### 2.1 ZLModel

#### 2.1.1 calculate_theoretical_price(cb_data, stock_data, price_chg_data=None)

计算可转债理论价格。

**参数**：
- cb_data：可转债数据，包含基本信息和当前价格
- stock_data：正股数据，包含当前价格和历史波动率
- price_chg_data：转股价调整记录，默认为None

**返回值**：理论价格

#### 2.1.2 calculate_arbitrage_space(market_price, theoretical_price)

计算套利空间。

**参数**：
- market_price：市场价格
- theoretical_price：理论价格

**返回值**：套利空间（百分比）

### 2.2 MomentumFactors

#### 2.2.1 calculate_price_momentum(ts_code, trade_date, window=5)

计算价格动量因子。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYYMMDD
- window：窗口大小，默认为5个交易日

**返回值**：价格动量（百分比）

#### 2.2.2 calculate_rsi(ts_code, trade_date, window=6)

计算RSI指标。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYYMMDD
- window：窗口大小，默认为6个交易日

**返回值**：RSI值（0-100）

#### 2.2.3 calculate_macd(ts_code, trade_date, fast=12, slow=26, signal=9)

计算MACD指标。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYYMMDD
- fast：快线周期，默认为12
- slow：慢线周期，默认为26
- signal：信号线周期，默认为9

**返回值**：MACD值

#### 2.2.4 calculate_volume_change(ts_code, trade_date, window=5)

计算成交量变化率。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYYMMDD
- window：窗口大小，默认为5个交易日

**返回值**：成交量变化率（百分比）

### 2.3 FactorCalculator

#### 2.3.1 calculate_factors_for_date(trade_date)

计算指定日期的所有因子。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：因子DataFrame

#### 2.3.2 calculate_factors_for_period(start_date, end_date)

计算指定时间段的所有因子。

**参数**：
- start_date：开始日期，格式YYYY-MM-DD
- end_date：结束日期，格式YYYY-MM-DD

**返回值**：因子DataFrame列表

### 2.4 FactorNormalizer

#### 2.4.1 normalize(df, method='zscore')

归一化因子。

**参数**：
- df：因子DataFrame
- method：归一化方法，可选'zscore'、'minmax'，默认为'zscore'

**返回值**：归一化后的因子DataFrame

### 2.5 FactorCombiner

#### 2.5.1 combine(df, weights=None)

组合因子。

**参数**：
- df：因子DataFrame
- weights：权重字典，格式为{'factor_name': weight, ...}，默认为None，表示等权重

**返回值**：组合后的因子DataFrame

## 3. 策略模块API

### 3.1 TopNDropoutKStrategy

#### 3.1.1 select_bonds(trade_date, factor_name='combined_factor', ascending=False)

根据因子选择可转债。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：选择的可转债列表，格式为[{'ts_code': code, 'weight': weight}, ...]

#### 3.1.2 update_holdings(trade_date, factor_name='combined_factor', ascending=False)

更新持仓。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：更新后的持仓列表，格式为[{'ts_code': code, 'weight': weight}, ...]

#### 3.1.3 handle_special_events(trade_date)

处理特殊事件（强赎、下修等）。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：处理后的持仓列表，格式为[{'ts_code': code, 'weight': weight}, ...]

#### 3.1.4 generate_signals(trade_date, factor_name='combined_factor', ascending=False)

生成交易信号。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：交易信号列表，格式为[{'ts_code': code, 'signal': signal, 'weight': weight}, ...]

### 3.2 EventHandler

#### 3.2.1 check_call_events(ts_codes, trade_date)

检查是否有强赎事件。

**参数**：
- ts_codes：可转债代码列表
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：强赎事件列表，格式为[{'ts_code': code, 'call_date': date, 'call_price': price}, ...]

#### 3.2.2 check_put_events(ts_codes, trade_date)

检查是否有回售事件。

**参数**：
- ts_codes：可转债代码列表
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：回售事件列表，格式为[{'ts_code': code, 'put_date': date, 'put_price': price}, ...]

#### 3.2.3 check_price_adjustment_events(ts_codes, trade_date)

检查是否有转股价调整事件。

**参数**：
- ts_codes：可转债代码列表
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：转股价调整事件列表，格式为[{'ts_code': code, 'change_date': date, 'convert_price_before': price_before, 'convert_price_after': price_after}, ...]

#### 3.2.4 check_downward_adjustment_condition(ts_code, trade_date)

检查是否满足下修条件（连续30个交易日收盘价格低于当期转股价格的80%）。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：是否满足下修条件

#### 3.2.5 check_call_condition(ts_code, trade_date)

检查是否满足强赎条件（连续30个交易日中至少15个交易日收盘价格不低于当期转股价格的130%）。

**参数**：
- ts_code：可转债代码
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：是否满足强赎条件

### 3.3 SignalGenerator

#### 3.3.1 generate_signals(trade_date, factor_name='combined_factor', ascending=False)

生成交易信号。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：交易信号DataFrame

### 3.4 ParameterOptimizer

#### 3.4.1 grid_search(param_grid)

网格搜索优化参数。

**参数**：
- param_grid：参数网格，格式为{'param_name': [value1, value2, ...], ...}

**返回值**：最优参数和对应的回测结果

## 4. 回测模块API

### 4.1 Backtester

#### 4.1.1 run(factor_name='combined_factor', ascending=False)

运行回测。

**参数**：
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：回测结果，包括每日净值、交易记录、绩效指标等

### 4.2 TradingSimulator

#### 4.2.1 execute_trades(signals, trade_date)

执行交易。

**参数**：
- signals：交易信号DataFrame
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：交易记录列表

#### 4.2.2 get_position_snapshot(trade_date)

获取持仓快照。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：持仓快照，格式为{'date': date, 'positions': {'ts_code': {'quantity': quantity, 'cost': cost}, ...}, 'cash': cash}

#### 4.2.3 get_net_value(trade_date)

获取净值。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD

**返回值**：净值

### 4.3 PerformanceAnalyzer

#### 4.3.1 calculate_performance(results)

计算绩效指标。

**参数**：
- results：回测结果，包括每日净值、交易记录等

**返回值**：绩效指标，包括年化收益率、最大回撤、夏普比率等

### 4.4 ReportGenerator

#### 4.4.1 generate_report(results, start_date, end_date)

生成回测报告。

**参数**：
- results：回测结果，包括每日净值、交易记录、绩效指标等
- start_date：回测开始日期，格式YYYY-MM-DD
- end_date：回测结束日期，格式YYYY-MM-DD

**返回值**：报告文件路径

## 5. 实时模拟交易模块API

### 5.1 RealtimeDataFetcher

#### 5.1.1 fetch_realtime_data()

获取实时数据。

**返回值**：是否成功获取数据

#### 5.1.2 check_special_events()

检查特殊事件（强赎、下修等）。

**返回值**：特殊事件列表

### 5.2 RealtimeSignalGenerator

#### 5.2.1 generate_realtime_signals(trade_date=None, factor_name='combined_factor', ascending=False)

生成实时交易信号。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD，默认为None，表示当前日期
- factor_name：因子名称，默认为'combined_factor'
- ascending：排序方向，默认为False（降序）

**返回值**：交易信号DataFrame

### 5.3 TradingExecutor

#### 5.3.1 execute_trades(signals, trade_date=None)

执行交易。

**参数**：
- signals：交易信号DataFrame
- trade_date：交易日期，格式YYYY-MM-DD，默认为None，表示当前日期

**返回值**：交易记录列表

#### 5.3.2 get_position_snapshot(trade_date=None)

获取持仓快照。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD，默认为None，表示当前日期

**返回值**：持仓快照

#### 5.3.3 get_net_value(trade_date=None)

获取净值。

**参数**：
- trade_date：交易日期，格式YYYY-MM-DD，默认为None，表示当前日期

**返回值**：净值

### 5.4 MonitoringSystem

#### 5.4.1 log_event(event_type, event_data)

记录事件。

**参数**：
- event_type：事件类型
- event_data：事件数据

**返回值**：无

#### 5.4.2 generate_daily_report(trades, positions, net_value)

生成每日报告。

**参数**：
- trades：交易记录列表
- positions：持仓快照
- net_value：净值

**返回值**：报告文件路径

### 5.5 RealtimeSimulator

#### 5.5.1 start()

启动实时模拟交易。

**返回值**：无

#### 5.5.2 stop()

停止实时模拟交易。

**返回值**：无

#### 5.5.3 run_once()

运行一次实时模拟交易流程。

**返回值**：无

## 6. 日志模块API

### 6.1 Logger

#### 6.1.1 debug(message)

记录调试级别日志。

**参数**：
- message：日志消息

**返回值**：无

#### 6.1.2 info(message)

记录信息级别日志。

**参数**：
- message：日志消息

**返回值**：无

#### 6.1.3 warning(message)

记录警告级别日志。

**参数**：
- message：日志消息

**返回值**：无

#### 6.1.4 error(message)

记录错误级别日志。

**参数**：
- message：日志消息

**返回值**：无

#### 6.1.5 critical(message)

记录严重错误级别日志。

**参数**：
- message：日志消息

**返回值**：无

#### 6.1.6 exception(message)

记录异常日志。

**参数**：
- message：日志消息

**返回值**：无

### 6.2 ErrorHandler

#### 6.2.1 handle_error(error, context=None)

处理错误。

**参数**：
- error：错误对象
- context：错误上下文，默认为None

**返回值**：错误处理结果

#### 6.2.2 handle_exception(func)

异常处理装饰器。

**参数**：
- func：被装饰的函数

**返回值**：装饰后的函数

### 6.3 DocumentGenerator

#### 6.3.1 generate_usage_doc()

生成使用文档。

**返回值**：文档文件路径

#### 6.3.2 generate_api_doc()

生成API文档。

**返回值**：文档文件路径
"""
        
        # 保存文档
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return doc_file
    
    def generate_example_doc(self):
        """
        生成示例文档
        
        Returns:
            文档文件路径
        """
        # 构建文档文件路径
        doc_file = os.path.join(self.doc_dir, 'example.md')
        
        # 生成文档内容
        content = """# 可转债量化投资工具示例

## 1. 数据获取示例

```python
from data.fetcher import DataFetcher

# 初始化数据获取器
data_fetcher = DataFetcher()

# 获取可转债基本信息
df_basic = data_fetcher.fetch_cb_basic()

# 获取可转债日行情数据
df_daily = data_fetcher.fetch_cb_daily(start_date='20200101', end_date='20250606')

# 获取正股日行情数据
df_stock = data_fetcher.fetch_stock_daily(start_date='20200101', end_date='20250606')

# 获取可转债转股价调整记录
df_price_chg = data_fetcher.fetch_cb_price_chg()

# 获取所有数据
data_fetcher.fetch_all_data(start_date='2020-01-01', end_date='2025-06-06')

# 更新数据到最新
data_fetcher.update_data()
```

## 2. 因子计算示例

```python
from factor.zl_model import FactorCalculator, FactorNormalizer, FactorCombiner

# 初始化因子计算器
factor_calculator = FactorCalculator()

# 计算指定日期的因子
df_factor = factor_calculator.calculate_factors_for_date('2025-06-06')

# 计算指定时间段的因子
df_factors = factor_calculator.calculate_factors_for_period('2020-01-01', '2025-06-06')

# 归一化因子
factor_normalizer = FactorNormalizer()
df_normalized = factor_normalizer.normalize(df_factor, method='zscore')

# 组合因子
factor_combiner = FactorCombiner()
weights = {
    'arbitrage_space': 0.5,
    'momentum_5d': 0.2,
    'rsi_6d': 0.2,
    'vol_change': 0.1
}
df_combined = factor_combiner.combine(df_normalized, weights)
```

## 3. 策略示例

```python
from strategy.topndropoutk import TopNDropoutKStrategy, SignalGenerator

# 初始化策略
strategy = TopNDropoutKStrategy(n=10, k=2)

# 选择可转债
bonds = strategy.select_bonds('2025-06-06', factor_name='combined_factor', ascending=False)

# 更新持仓
holdings = strategy.update_holdings('2025-06-06', factor_name='combined_factor', ascending=False)

# 处理特殊事件
holdings = strategy.handle_special_events('2025-06-06')

# 生成交易信号
signals = strategy.generate_signals('2025-06-06', factor_name='combined_factor', ascending=False)

# 使用信号生成器
signal_generator = SignalGenerator(strategy)
signals = signal_generator.generate_signals('2025-06-06', factor_name='combined_factor', ascending=False)
```

## 4. 回测示例

```python
from backtest.backtester import Backtester
from strategy.topndropoutk import TopNDropoutKStrategy

# 初始化策略
strategy = TopNDropoutKStrategy(n=10, k=2)

# 初始化回测器
backtester = Backtester(
    start_date='2020-01-01',
    end_date='2025-06-06',
    strategy=strategy,
    initial_capital=1000000.0
)

# 运行回测
results = backtester.run(factor_name='combined_factor', ascending=False)

# 查看回测结果
print(f"总收益率: {results['performance']['total_return']:.2%}")
print(f"年化收益率: {results['performance']['annual_return']:.2%}")
print(f"最大回撤: {results['performance']['max_drawdown']:.2%}")
print(f"夏普比率: {results['performance']['sharpe_ratio']:.2f}")
```

## 5. 实时模拟交易示例

```python
from realtime.simulator import RealtimeSimulator
from strategy.topndropoutk import TopNDropoutKStrategy

# 初始化策略
strategy = TopNDropoutKStrategy(n=10, k=2)

# 初始化实时模拟交易器
simulator = RealtimeSimulator(
    strategy=strategy,
    initial_capital=1000000.0
)

# 启动实时模拟交易
simulator.start()

# 运行一次实时模拟交易流程
simulator.run_once()

# 停止实时模拟交易
simulator.stop()
```

## 6. 日志示例

```python
from logging.logger import Logger, ErrorHandler

# 初始化日志记录器
logger = Logger(name='example', level='INFO')

# 记录日志
logger.debug('这是一条调试日志')
logger.info('这是一条信息日志')
logger.warning('这是一条警告日志')
logger.error('这是一条错误日志')
logger.critical('这是一条严重错误日志')

# 使用错误处理器
error_handler = ErrorHandler()

try:
    # 可能出错的代码
    result = 1 / 0
except Exception as e:
    # 处理错误
    error_handler.handle_error(e, context='除零错误示例')

# 使用异常处理装饰器
@error_handler.handle_exception
def divide(a, b):
    return a / b

result = divide(1, 0)  # 不会抛出异常，而是返回错误处理结果
```

## 7. 命令行示例

```bash
# 获取数据
python main.py --mode data --action fetch --start_date 2020-01-01 --end_date 2025-06-06

# 更新数据
python main.py --mode data --action update

# 计算因子
python main.py --mode factor --date 2025-06-06

# 回测
python main.py --mode backtest --start_date 2020-01-01 --end_date 2025-06-06 --strategy topndropoutk --params "n=10,k=2"

# 实时模拟交易
python main.py --mode realtime --strategy topndropoutk --params "n=10,k=2"

# 参数优化
python main.py --mode optimize --start_date 2020-01-01 --end_date 2025-06-06 --strategy topndropoutk --param_range "n=5:15:1,k=1:5:1"
```
"""
        
        # 保存文档
        with open(doc_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return doc_file
