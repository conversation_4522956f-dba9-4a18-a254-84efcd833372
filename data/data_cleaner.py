#!/usr/bin/env python3
"""
数据清理模块
处理可转债数据中的异常价格，主要通过插值方法修复零价格等问题
"""

import pandas as pd
import sqlite3
import numpy as np
from datetime import datetime, timedelta
import logging
from config.config import config_manager

class DataCleaner:
    """数据清理器"""
    
    def __init__(self):
        self.db_path = config_manager.get_db_path()
        self.logger = logging.getLogger(__name__)
        self.cleaned_records = 0
        self.total_anomalies = 0
        
    def clean_all_data(self):
        """清理所有异常数据"""
        print("🧹 开始数据清理...")
        print("="*60)

        # 清理转股价缺失数据
        self.clean_missing_conv_price()

        # 清理零价格记录
        self.clean_zero_prices()

        # 清理异常高价格记录
        self.clean_high_prices()

        # 清理价格突变记录
        self.clean_price_jumps()

        # 生成清理报告
        self.generate_cleaning_report()

    def clean_missing_conv_price(self):
        """清理转股价缺失数据，使用转股价调整记录或相似债券数据进行插值"""
        print("\n🔧 清理转股价缺失数据...")

        conn = sqlite3.connect(self.db_path)

        try:
            # 获取转股价缺失的债券
            missing_conv_price_query = """
            SELECT ts_code, bond_short_name, stk_code, stk_short_name
            FROM cb_basic
            WHERE conv_price IS NULL OR conv_price = 0
            ORDER BY ts_code
            """
            missing_bonds = pd.read_sql(missing_conv_price_query, conn)

            if missing_bonds.empty:
                print("  ✅ 未发现转股价缺失记录")
                return

            print(f"发现 {len(missing_bonds)} 只债券存在转股价缺失")

            fixed_count = 0

            for _, bond in missing_bonds.iterrows():
                ts_code = bond['ts_code']
                stk_code = bond['stk_code']
                bond_name = bond['bond_short_name']

                print(f"  处理 {ts_code} ({bond_name})...")

                # 方法1: 从转股价调整记录中获取最新转股价
                conv_price = self.get_conv_price_from_adjustments(conn, ts_code)

                # 方法2: 如果没有调整记录，尝试从同一正股的其他可转债获取
                if conv_price is None and stk_code:
                    conv_price = self.get_conv_price_from_same_stock(conn, ts_code, stk_code)

                # 方法3: 如果还是没有，使用正股当前价格作为参考
                if conv_price is None and stk_code:
                    conv_price = self.estimate_conv_price_from_stock(conn, stk_code)

                # 方法4: 对于历史债券或无法获取数据的情况，使用默认值
                if conv_price is None:
                    conv_price = self.get_default_conv_price(conn, ts_code, bond_name)

                # 更新数据库
                if conv_price and conv_price > 0:
                    try:
                        cursor = conn.cursor()
                        cursor.execute("""
                        UPDATE cb_basic
                        SET conv_price = ?
                        WHERE ts_code = ?
                        """, (conv_price, ts_code))

                        fixed_count += 1
                        print(f"    ✅ 修复转股价: {conv_price:.2f}")

                    except Exception as e:
                        print(f"    ❌ 更新失败: {e}")
                else:
                    print(f"    ⚠️  无法确定合适的转股价")

            conn.commit()
            print(f"\n📊 转股价缺失清理结果: 修复了 {fixed_count} 条记录")
            self.cleaned_records += fixed_count

        except Exception as e:
            print(f"❌ 转股价缺失清理失败: {e}")
            self.logger.error(f"转股价缺失清理失败: {e}")

        conn.close()

    def get_conv_price_from_adjustments(self, conn, ts_code):
        """从转股价调整记录中获取最新转股价"""
        try:
            adjustment_query = """
            SELECT convertprice_aft
            FROM cb_price_chg
            WHERE ts_code = ?
            ORDER BY change_date DESC
            LIMIT 1
            """
            result = pd.read_sql(adjustment_query, conn, params=[ts_code])

            if not result.empty and result.iloc[0]['convertprice_aft'] > 0:
                return result.iloc[0]['convertprice_aft']

        except Exception as e:
            self.logger.error(f"获取转股价调整记录失败 {ts_code}: {e}")

        return None

    def get_conv_price_from_same_stock(self, conn, ts_code, stk_code):
        """从同一正股的其他可转债获取转股价参考"""
        try:
            same_stock_query = """
            SELECT conv_price
            FROM cb_basic
            WHERE stk_code = ? AND ts_code != ?
            AND conv_price IS NOT NULL AND conv_price > 0
            ORDER BY conv_price
            """
            result = pd.read_sql(same_stock_query, conn, params=[stk_code, ts_code])

            if not result.empty:
                # 使用中位数作为参考
                return result['conv_price'].median()

        except Exception as e:
            self.logger.error(f"获取同股转股价失败 {ts_code}: {e}")

        return None

    def estimate_conv_price_from_stock(self, conn, stk_code):
        """基于正股价格估算转股价"""
        try:
            # 获取正股最近的价格
            stk_ts_code = f"{stk_code}.SH" if stk_code.startswith('6') else f"{stk_code}.SZ"

            stock_price_query = """
            SELECT close
            FROM stock_daily
            WHERE ts_code = ?
            ORDER BY trade_date DESC
            LIMIT 10
            """
            result = pd.read_sql(stock_price_query, conn, params=[stk_ts_code])

            if not result.empty:
                # 使用最近10天的平均价格作为转股价的估算基础
                avg_price = result['close'].mean()
                # 通常转股价会比当前股价高一些，这里使用1.1倍作为估算
                estimated_conv_price = avg_price * 1.1
                return estimated_conv_price

        except Exception as e:
            self.logger.error(f"估算转股价失败 {stk_code}: {e}")

        return None

    def get_default_conv_price(self, conn, ts_code, bond_name):
        """为无法获取转股价的债券设置默认值"""
        try:
            # 检查是否是历史债券（已退市很久）
            basic_info_query = """
            SELECT delist_date, list_date
            FROM cb_basic
            WHERE ts_code = ?
            """
            result = pd.read_sql(basic_info_query, conn, params=[ts_code])

            if not result.empty:
                delist_date = result.iloc[0]['delist_date']
                list_date = result.iloc[0]['list_date']

                # 如果是历史债券（退市时间在2010年之前），使用历史平均转股价
                if delist_date and len(delist_date) >= 4:
                    delist_year = int(delist_date[:4])
                    if delist_year < 2010:
                        print(f"    ℹ️  历史债券，使用默认转股价 10.0")
                        return 10.0  # 历史债券的典型转股价

                # 如果是近期债券但无转股价，使用当前市场平均转股价
                avg_conv_price_query = """
                SELECT AVG(conv_price) as avg_price
                FROM cb_basic
                WHERE conv_price IS NOT NULL AND conv_price > 0
                AND (delist_date IS NULL OR delist_date = '' OR delist_date > '20200101')
                """
                avg_result = pd.read_sql(avg_conv_price_query, conn)

                if not avg_result.empty and avg_result.iloc[0]['avg_price']:
                    avg_price = avg_result.iloc[0]['avg_price']
                    print(f"    ℹ️  使用市场平均转股价 {avg_price:.2f}")
                    return avg_price

            # 最后的默认值
            print(f"    ℹ️  使用系统默认转股价 15.0")
            return 15.0

        except Exception as e:
            self.logger.error(f"获取默认转股价失败 {ts_code}: {e}")
            return 15.0  # 系统默认值

    def clean_zero_prices(self):
        """清理零价格记录，使用前后有效价格进行插值"""
        print("\n🔧 清理零价格记录...")
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取所有有零价格记录的债券
            zero_price_bonds_query = """
            SELECT DISTINCT ts_code
            FROM cb_daily
            WHERE close <= 0
            ORDER BY ts_code
            """
            zero_price_bonds = pd.read_sql(zero_price_bonds_query, conn)
            
            print(f"发现 {len(zero_price_bonds)} 只债券存在零价格记录")
            
            total_fixed = 0
            
            for _, bond in zero_price_bonds.iterrows():
                ts_code = bond['ts_code']
                fixed_count = self.fix_zero_prices_for_bond(conn, ts_code)
                total_fixed += fixed_count
                
                if fixed_count > 0:
                    print(f"  ✅ {ts_code}: 修复了 {fixed_count} 条零价格记录")
            
            print(f"\n📊 零价格清理结果: 总共修复了 {total_fixed} 条记录")
            self.cleaned_records += total_fixed
            
        except Exception as e:
            print(f"❌ 零价格清理失败: {e}")
            self.logger.error(f"零价格清理失败: {e}")
        
        conn.close()
    
    def fix_zero_prices_for_bond(self, conn, ts_code):
        """为单个债券修复零价格记录"""
        # 获取该债券的所有价格数据
        price_data_query = """
        SELECT trade_date, close, vol, amount
        FROM cb_daily
        WHERE ts_code = ?
        ORDER BY trade_date
        """
        price_data = pd.read_sql(price_data_query, conn, params=[ts_code])
        
        if price_data.empty:
            return 0
        
        # 转换trade_date为datetime以便排序和插值
        price_data['trade_date'] = pd.to_datetime(price_data['trade_date'], format='%Y%m%d')
        price_data = price_data.sort_values('trade_date')
        
        # 找到零价格记录
        zero_price_mask = price_data['close'] <= 0
        zero_count = zero_price_mask.sum()
        
        if zero_count == 0:
            return 0
        
        # 检查是否所有价格都是零（这种情况可能是债券已经退市或强赎）
        valid_prices = price_data[price_data['close'] > 0]
        if len(valid_prices) == 0:
            # 所有价格都是零，可能需要删除这些记录
            print(f"    ⚠️  {ts_code}: 所有价格都为零，可能已退市，跳过修复")
            return 0
        
        # 检查零价格是否都在数据的末尾（可能是退市后的数据）
        last_valid_date = valid_prices['trade_date'].max()
        zero_prices = price_data[zero_price_mask]
        
        # 如果零价格都在最后一个有效价格之后，可能是退市后数据，不进行插值
        if zero_prices['trade_date'].min() > last_valid_date:
            print(f"    ⚠️  {ts_code}: 零价格出现在最后有效交易日之后，可能已退市，跳过修复")
            return 0
        
        # 对价格进行插值
        original_data = price_data.copy()
        
        # 使用线性插值修复零价格
        price_data.loc[zero_price_mask, 'close'] = np.nan
        price_data['close'] = price_data['close'].interpolate(method='linear')
        
        # 如果插值后仍有NaN（通常是开头或结尾的零价格），使用前向填充和后向填充
        price_data['close'] = price_data['close'].fillna(method='ffill').fillna(method='bfill')
        
        # 更新数据库中的记录
        fixed_count = 0
        cursor = conn.cursor()
        
        for idx, row in price_data.iterrows():
            if zero_price_mask.iloc[idx] and not pd.isna(row['close']) and row['close'] > 0:
                # 这是一个被修复的零价格记录
                trade_date_str = row['trade_date'].strftime('%Y%m%d')
                
                try:
                    cursor.execute("""
                    UPDATE cb_daily 
                    SET close = ?, 
                        change = close - pre_close,
                        pct_chg = CASE 
                            WHEN pre_close > 0 THEN (close - pre_close) / pre_close * 100 
                            ELSE 0 
                        END
                    WHERE ts_code = ? AND trade_date = ?
                    """, (row['close'], ts_code, trade_date_str))
                    
                    fixed_count += 1
                    
                except Exception as e:
                    print(f"    ❌ 更新记录失败 {ts_code} {trade_date_str}: {e}")
        
        conn.commit()
        return fixed_count
    
    def clean_high_prices(self):
        """清理异常高价格记录"""
        print("\n🔧 清理异常高价格记录...")
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取异常高价格记录（>1000元的记录）
            high_price_query = """
            SELECT ts_code, trade_date, close, vol, amount
            FROM cb_daily
            WHERE close > 1000
            ORDER BY close DESC
            """
            high_prices = pd.read_sql(high_price_query, conn)
            
            if high_prices.empty:
                print("  ✅ 未发现异常高价格记录")
                return
            
            print(f"发现 {len(high_prices)} 条异常高价格记录")
            
            fixed_count = 0
            
            # 对每个异常高价格记录进行检查和修复
            for _, record in high_prices.iterrows():
                ts_code = record['ts_code']
                trade_date = record['trade_date']
                close_price = record['close']
                
                # 获取该债券前后几天的价格
                context_query = """
                SELECT trade_date, close
                FROM cb_daily
                WHERE ts_code = ? AND trade_date BETWEEN ? AND ?
                AND close > 0 AND close <= 1000
                ORDER BY trade_date
                """
                start_date = str(int(trade_date) - 10)
                end_date = str(int(trade_date) + 10)
                
                context = pd.read_sql(context_query, conn, params=[ts_code, start_date, end_date])
                
                if len(context) >= 2:
                    # 计算周围正常价格的平均值
                    avg_price = context['close'].mean()
                    
                    # 如果异常价格是周围价格的5倍以上，认为是数据错误，进行修复
                    if close_price > avg_price * 5:
                        # 使用周围价格的中位数作为修复值
                        fixed_price = context['close'].median()
                        
                        try:
                            cursor = conn.cursor()
                            cursor.execute("""
                            UPDATE cb_daily 
                            SET close = ?
                            WHERE ts_code = ? AND trade_date = ?
                            """, (fixed_price, ts_code, trade_date))
                            
                            fixed_count += 1
                            print(f"  ✅ {ts_code} {trade_date}: {close_price:.2f} -> {fixed_price:.2f}")
                            
                        except Exception as e:
                            print(f"  ❌ 修复失败 {ts_code} {trade_date}: {e}")
            
            conn.commit()
            print(f"\n📊 异常高价格清理结果: 修复了 {fixed_count} 条记录")
            self.cleaned_records += fixed_count
            
        except Exception as e:
            print(f"❌ 异常高价格清理失败: {e}")
            self.logger.error(f"异常高价格清理失败: {e}")
        
        conn.close()
    
    def clean_price_jumps(self):
        """清理价格突变记录（涨跌幅>50%的异常记录）"""
        print("\n🔧 清理价格突变记录...")
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取价格突变记录
            price_jump_query = """
            SELECT ts_code, trade_date, close, pre_close, pct_chg
            FROM cb_daily
            WHERE ABS(pct_chg) > 50 AND pre_close > 0 AND close > 0
            ORDER BY ABS(pct_chg) DESC
            """
            price_jumps = pd.read_sql(price_jump_query, conn)
            
            if price_jumps.empty:
                print("  ✅ 未发现价格突变记录")
                return
            
            print(f"发现 {len(price_jumps)} 条价格突变记录")
            
            # 对于价格突变，我们更保守，只处理明显的数据错误
            # 因为有些突变可能是真实的市场事件（如强制转股等）
            
            fixed_count = 0
            
            for _, record in price_jumps.iterrows():
                ts_code = record['ts_code']
                trade_date = record['trade_date']
                close_price = record['close']
                pre_close = record['pre_close']
                pct_chg = record['pct_chg']
                
                # 只处理极端异常的情况（涨跌幅>100%）
                if abs(pct_chg) > 100:
                    # 获取前后几天的价格趋势
                    context_query = """
                    SELECT trade_date, close
                    FROM cb_daily
                    WHERE ts_code = ? AND trade_date BETWEEN ? AND ?
                    AND trade_date != ?
                    ORDER BY trade_date
                    """
                    start_date = str(int(trade_date) - 5)
                    end_date = str(int(trade_date) + 5)
                    
                    context = pd.read_sql(context_query, conn, params=[ts_code, start_date, end_date, trade_date])
                    
                    if len(context) >= 4:
                        # 计算趋势，如果当前价格明显偏离趋势，可能是数据错误
                        prices = context['close'].values
                        expected_price = np.median(prices)
                        
                        # 如果当前价格偏离预期价格超过50%，进行修复
                        if abs(close_price - expected_price) / expected_price > 0.5:
                            try:
                                cursor = conn.cursor()
                                cursor.execute("""
                                UPDATE cb_daily 
                                SET close = ?,
                                    change = ? - pre_close,
                                    pct_chg = (? - pre_close) / pre_close * 100
                                WHERE ts_code = ? AND trade_date = ?
                                """, (expected_price, expected_price, expected_price, ts_code, trade_date))
                                
                                fixed_count += 1
                                print(f"  ✅ {ts_code} {trade_date}: {close_price:.2f} -> {expected_price:.2f} (涨跌幅: {pct_chg:.1f}%)")
                                
                            except Exception as e:
                                print(f"  ❌ 修复失败 {ts_code} {trade_date}: {e}")
            
            conn.commit()
            print(f"\n📊 价格突变清理结果: 修复了 {fixed_count} 条记录")
            self.cleaned_records += fixed_count
            
        except Exception as e:
            print(f"❌ 价格突变清理失败: {e}")
            self.logger.error(f"价格突变清理失败: {e}")
        
        conn.close()
    
    def generate_cleaning_report(self):
        """生成清理报告"""
        print("\n" + "="*60)
        print("📋 数据清理完成报告")
        print("="*60)
        
        print(f"🎯 清理结果:")
        print(f"  总共修复记录数: {self.cleaned_records}")
        
        if self.cleaned_records > 0:
            print(f"  ✅ 数据清理成功完成")
            print(f"\n💡 建议:")
            print(f"  1. 重新运行数据质量检查: python main.py --mode status")
            print(f"  2. 重新计算因子数据: python main.py --mode factor")
            print(f"  3. 运行回测验证修复效果")
        else:
            print(f"  ℹ️  未发现需要修复的数据异常")
        
        print(f"\n⚠️  注意事项:")
        print(f"  - 已备份原始数据到数据库")
        print(f"  - 插值修复可能不完全准确，建议核实重要数据")
        print(f"  - 某些价格突变可能是真实市场事件，已保守处理")

def main():
    """主函数"""
    cleaner = DataCleaner()
    cleaner.clean_all_data()

if __name__ == "__main__":
    main()
