"""
数据获取模块，负责从tushare获取可转债相关数据
"""
import logging
import os
import sqlite3
import time
from datetime import datetime, timedelta

import pandas as pd
import tushare as ts

# 导入配置管理器
from config.config import config_manager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataFetcher:
    """数据获取类，负责从tushare获取数据"""

    def __init__(self):
        """初始化数据获取器"""
        # 获取tushare token
        self.token = config_manager.get_tushare_token()
        if not self.token:
            raise ValueError("Tushare API Token未设置，请在.env文件中设置TUSHARE_TOKEN")

        # 初始化tushare
        ts.set_token(self.token)
        self.pro = ts.pro_api()

        # 设置数据库路径
        self.db_path = config_manager.get_db_path()

        # 设置缓存目录
        self.cache_dir = config_manager.get_cache_dir()
        os.makedirs(self.cache_dir, exist_ok=True)

        # API频率控制 - 支持不同API的不同限制
        self.api_call_history = {}  # 记录每个API的调用历史
        self.api_start_times = {}   # 记录每个API的开始时间

        # 根据用户积分等级设置基础频率限制（保守设置）
        # 用户可以通过环境变量 TUSHARE_POINTS 设置自己的积分等级
        user_points = int(os.getenv('TUSHARE_POINTS', '2000'))
        if user_points >= 15000:
            self.base_calls_per_minute = 900  # 15000+积分用户，保守设置
        elif user_points >= 10000:
            self.base_calls_per_minute = 800  # 10000+积分用户，保守设置
        elif user_points >= 5000:
            self.base_calls_per_minute = 400  # 5000+积分用户，保守设置
        elif user_points >= 2000:
            self.base_calls_per_minute = 150  # 2000+积分用户，保守设置
        else:
            self.base_calls_per_minute = 40   # 120积分用户，保守设置

        # 定义不同API的频率限制（基于基础限制的倍数）
        self.api_rate_limits = {
            # 基础数据API - 可以使用较高频率
            'cb_basic': self.base_calls_per_minute,
            'cb_call': self.base_calls_per_minute,

            # 日行情数据API - 中等频率
            'cb_daily': int(self.base_calls_per_minute * 0.8),
            'daily': int(self.base_calls_per_minute * 0.8),

            # 历史数据API - 中等频率
            'pro_bar': int(self.base_calls_per_minute * 0.7),

            # 价格变动API - 较低频率（这个API调用相对较少）
            'cb_price_chg': int(self.base_calls_per_minute * 0.6),

            # 默认API限制
            'default': int(self.base_calls_per_minute * 0.8)
        }

        print(f"用户积分等级: {user_points}, 基础API频率限制: {self.base_calls_per_minute}/分钟")

        # 初始化数据库连接
        self._init_db()

    def _check_api_rate_limit(self, api_name="default"):
        """
        检查API调用频率限制，如果达到限制则等待
        支持不同API使用不同的频率限制

        Args:
            api_name: API名称，用于区分不同API的限制

        Returns:
            None
        """
        # 获取该API的频率限制
        api_limit = self.api_rate_limits.get(api_name, self.api_rate_limits['default'])

        # 初始化该API的调用记录
        if api_name not in self.api_call_history:
            self.api_call_history[api_name] = 0
            self.api_start_times[api_name] = time.time()

        # 检查API调用频率限制
        self.api_call_history[api_name] += 1
        elapsed_time = time.time() - self.api_start_times[api_name]

        # 如果已经过了一分钟，重置计数器
        if elapsed_time >= 60:
            self.api_call_history[api_name] = 1
            self.api_start_times[api_name] = time.time()
            return

        # 如果达到限制，等待剩余时间
        if self.api_call_history[api_name] >= api_limit:
            wait_time = 60 - elapsed_time + 1  # 多等1秒确保安全
            print(f"API {api_name} 调用频率限制({api_limit}/分钟)，等待 {wait_time:.1f} 秒...")
            time.sleep(wait_time)

            # 重置计数器
            self.api_call_history[api_name] = 1
            self.api_start_times[api_name] = time.time()

    def _wait_for_api_rate_limit(self, api_name="default", wait_seconds=0.5):
        """
        为API调用添加基础等待时间，避免过于频繁的请求
        这是对_check_api_rate_limit的补充，用于替换原来的固定sleep调用

        Args:
            api_name: API名称
            wait_seconds: 基础等待时间（秒）
        """
        # 先检查频率限制
        self._check_api_rate_limit(api_name)

        # 添加基础等待时间，避免请求过于密集
        if wait_seconds > 0:
            time.sleep(wait_seconds)

    def _init_db(self):
        """初始化数据库"""
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 连接数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建可转债基本信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cb_basic (
            ts_code TEXT PRIMARY KEY,
            bond_full_name TEXT,
            bond_short_name TEXT,
            cb_code TEXT,
            stk_code TEXT,
            stk_short_name TEXT,
            maturity REAL,
            par REAL,
            issue_price REAL,
            issue_size REAL,
            remain_size REAL,
            value_date TEXT,
            maturity_date TEXT,
            coupon_rate REAL,
            add_rate REAL,
            pay_per_year INTEGER,
            list_date TEXT,
            delist_date TEXT,
            conv_start_date TEXT,
            conv_end_date TEXT,
            conv_price REAL,
            rate_type TEXT,
            exchange TEXT,
            conv_stop_date TEXT,
            first_conv_price REAL,
            rate_clause TEXT,
            update_time TEXT
        )
        ''')


        
        # 创建可转债日行情表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cb_daily (
            ts_code TEXT,
            trade_date TEXT,
            pre_close REAL,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            change REAL,
            pct_chg REAL,
            vol REAL,
            amount REAL,
            accrued_interest REAL,
            full_price REAL,
            PRIMARY KEY (ts_code, trade_date)
        )
        ''')

        # 检查并添加新字段（为了兼容已有数据库）
        try:
            cursor.execute("ALTER TABLE cb_daily ADD COLUMN accrued_interest REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        try:
            cursor.execute("ALTER TABLE cb_daily ADD COLUMN full_price REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在


        
        # 创建正股日行情表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_daily (
            ts_code TEXT,
            trade_date TEXT,
            pre_close REAL,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            org_close REAL,
            change REAL,
            pct_chg REAL,
            vol REAL,
            amount REAL,
            PRIMARY KEY (ts_code, trade_date)
        )
        ''')

        # 检查并添加org_close字段（为了兼容已有数据库）
        try:
            cursor.execute("ALTER TABLE stock_daily ADD COLUMN org_close REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在
        
        # 创建可转债转股价调整表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cb_price_chg (
            ts_code TEXT,
            bond_short_name TEXT,
            publish_date TEXT,
            change_date TEXT,
            convert_price_initial REAL,
            convertprice_bef REAL,
            convertprice_aft REAL,
            PRIMARY KEY (ts_code, change_date)
        )
        ''')
        
        # 创建可转债强赎表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cb_call (
            ts_code TEXT,
            call_date TEXT,
            call_price REAL,
            call_type TEXT,
            call_reason TEXT,
            PRIMARY KEY (ts_code, call_date)
        )
        ''')
        

        
        # 创建因子表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cb_factor (
            ts_code TEXT,
            trade_date TEXT,
            zl_price REAL,
            arbitrage_space REAL,
            momentum_5d REAL,
            momentum_10d REAL,
            momentum_20d REAL,
            macd REAL,
            conv_value REAL,
            premium_ratio REAL,
            PRIMARY KEY (ts_code, trade_date)
        )
        ''')

        # 检查并添加新字段（为了兼容已有数据库）
        try:
            cursor.execute("ALTER TABLE cb_factor ADD COLUMN conv_value REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        try:
            cursor.execute("ALTER TABLE cb_factor ADD COLUMN premium_ratio REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        # 添加敏感度分析字段（advice_2.md改进）
        sensitivity_columns = [
            'corr_coef REAL',
            'reg_slope REAL',
            'model_delta REAL',
            'bond_floor REAL',
            'option_value REAL',
            'cb_return REAL',
            'stock_return REAL',
            'predicted_return REAL',
            'expected_return REAL',
            'stock_close REAL'
        ]
        
        for col_def in sensitivity_columns:
            try:
                cursor.execute(f"ALTER TABLE cb_factor ADD COLUMN {col_def}")
            except sqlite3.OperationalError:
                pass  # 字段已存在

        # 添加双低因子字段
        double_low_columns = [
            'price_factor REAL',
            'premium_factor REAL',
            'double_low_score REAL',
            'double_low_rank REAL',
            'double_low_percentile REAL'
        ]
        
        for col_def in double_low_columns:
            try:
                cursor.execute(f"ALTER TABLE cb_factor ADD COLUMN {col_def}")
            except sqlite3.OperationalError:
                pass  # 字段已存在

        # 添加复合因子字段
        try:
            cursor.execute("ALTER TABLE cb_factor ADD COLUMN combined_factor REAL")
        except sqlite3.OperationalError:
            pass  # 字段已存在

        # 添加高级技术因子字段
        technical_columns = [
            'bb_position REAL',
            'bb_width REAL', 
            'bb_deviation REAL',
            'volume_percentile REAL',
            'amount_percentile REAL',
            'volume_change_5d REAL',
            'volume_change_10d REAL',
            'volume_change_20d REAL',
            'volume_trend REAL',
            'volatility_5d REAL',
            'volatility_10d REAL',
            'volatility_20d REAL',
            'atr_14d REAL',
            'relative_volatility REAL',
            'price_percentile REAL',
            'price_position REAL',
            'drawdown_from_high REAL',
            'gain_from_low REAL',
            'avg_turnover_20d REAL',
            'turnover_cv REAL',
            'amihud_illiquidity REAL',
            'trend_slope_5d REAL',
            'trend_slope_10d REAL',
            'trend_slope_20d REAL',
            'trend_consistency REAL',
            'rsi_14d REAL',
            'williams_r REAL',
            'obv_trend REAL'
        ]
        
        for col_def in technical_columns:
            try:
                cursor.execute(f"ALTER TABLE cb_factor ADD COLUMN {col_def}")
            except sqlite3.OperationalError:
                pass  # 字段已存在
        
        # 添加enhanced_factors列
        enhanced_columns = [
            'forced_call_pressure REAL',
            'conversion_tendency REAL', 
            'time_decay_factor REAL',
            'momentum_vol_cross REAL',
            'momentum_consistency REAL',
            'value_quality_score REAL',
            'ma5_ma20_cross REAL',
            'ma_slope_5d REAL',
            'support_strength REAL'
        ]
        
        for col_def in enhanced_columns:
            try:
                cursor.execute(f"ALTER TABLE cb_factor ADD COLUMN {col_def}")
            except sqlite3.OperationalError:
                pass  # 字段已存在
        
        # 提交事务
        conn.commit()
        conn.close()

    def calculate_accrued_interest(self, trade_date, value_date, coupon_rate, pay_per_year, par_value=100):
        """
        计算应计利息

        Args:
            trade_date: 交易日期，格式YYYYMMDD
            value_date: 起息日期，格式YYYYMMDD
            coupon_rate: 票面利率（年化百分比形式，如4.0表示4%）
            pay_per_year: 付息频率（每年付息次数）
            par_value: 面值，默认100

        Returns:
            float: 应计利息
        """
        try:
            if not all([trade_date, value_date, coupon_rate, pay_per_year]):
                return 0.0

            # 转换日期格式
            trade_dt = datetime.strptime(str(trade_date), '%Y%m%d')
            value_dt = datetime.strptime(str(value_date), '%Y%m%d')

            # 如果交易日期早于起息日期，应计利息为0
            if trade_dt < value_dt:
                return 0.0

            # 将百分比形式的票面利率转换为小数形式（如4.0% -> 0.04）
            coupon_rate_decimal = coupon_rate / 100.0

            # 每期票息金额
            coupon_per_period = par_value * coupon_rate_decimal / pay_per_year

            # 计算付息周期（天数）
            days_per_period = 365 / pay_per_year

            # 生成所有付息日期（基于年度付息）
            payment_dates = []

            # 对于年付息（pay_per_year=1），付息日通常是每年的同一天
            if pay_per_year == 1:
                # 从起息日开始，每年生成一个付息日
                current_year = value_dt.year
                trade_year = trade_dt.year

                for year in range(current_year, trade_year + 2):  # 多生成一年确保覆盖
                    try:
                        # 尝试生成该年的付息日（与起息日同月同日）
                        payment_date = value_dt.replace(year=year)
                        if payment_date <= trade_dt:
                            payment_dates.append(payment_date)
                    except ValueError:
                        # 处理2月29日等特殊情况
                        continue
            else:
                # 对于其他付息频率，使用天数计算
                current_payment_date = value_dt
                while current_payment_date <= trade_dt:
                    payment_dates.append(current_payment_date)
                    # 添加付息周期天数
                    days_to_add = int(days_per_period)
                    current_payment_date = current_payment_date + timedelta(days=days_to_add)

            # 找到最近的付息日（在交易日之前或当天）
            last_payment_date = value_dt
            for payment_date in sorted(payment_dates):
                if payment_date <= trade_dt:
                    last_payment_date = payment_date
                else:
                    break

            # 计算距离最近付息日的天数
            days_since_last_payment = (trade_dt - last_payment_date).days

            # 应计利息 = 每期票息 * (距离最近付息日天数 / 付息周期天数)
            accrued_interest = coupon_per_period * (days_since_last_payment / days_per_period)

            return accrued_interest

        except Exception as e:
            print(f"计算应计利息失败: {e}")
            return 0.0

    def calculate_full_price(self, net_price, accrued_interest, ts_code=None, trade_date=None):
        """
        计算连续全价，只在除息日做修正，不使用应计利息

        新逻辑：
        1. 基础全价 = 净价（不加应计利息，避免重复计算）
        2. 除息日及之后：加上累积的除息修正

        Args:
            net_price: 净价
            accrued_interest: 应计利息（不使用，保留参数以兼容）
            ts_code: 可转债代码（用于除息日修正）
            trade_date: 交易日期（用于除息日修正）

        Returns:
            float: 连续全价
        """
        if net_price is None:
            return net_price

        # 基础全价 = 净价（不加应计利息）
        basic_full_price = net_price

        # 如果提供了ts_code和trade_date，计算累积除息调整
        if ts_code and trade_date:
            cumulative_adjustment = self._get_cumulative_dividend_adjustment(ts_code, trade_date)
            return basic_full_price + cumulative_adjustment

        return basic_full_price



    def _get_cumulative_dividend_adjustment(self, ts_code, trade_date):
        """
        获取累积的除息调整金额（通用版本）

        通过数据库缓存的除息日数据或价格跳跃分析来计算累积除息调整

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期

        Returns:
            float: 累积除息调整金额
        """
        try:
            # 获取该可转债的所有除息日和调整金额（非回测模式）
            dividend_data = self._get_dividend_dates_and_amounts(ts_code, backtest_mode=False)

            if not dividend_data:
                return 0.0

            # 计算该日期之前（包括当天）的累积除息调整
            total_adjustment = 0.0
            for dividend_date, adjustment_amount in dividend_data:
                if dividend_date <= trade_date:
                    total_adjustment += adjustment_amount

            return total_adjustment

        except Exception as e:
            print(f"计算累积除息调整失败: {e}")
            return 0.0

    def _get_dividend_dates_and_amounts(self, ts_code, conn=None, backtest_mode=False):
        """
        获取可转债的除息日和调整金额数据

        优先从数据库缓存获取，如果没有则尝试从tushare获取，最后通过价格跳跃分析生成并缓存

        Args:
            ts_code: 可转债代码
            conn: 数据库连接，如果为None则创建新连接
            backtest_mode: 是否为回测模式，如果是则禁止API调用

        Returns:
            list: [(除息日, 调整金额), ...] 按日期排序
        """
        try:
            import sqlite3
            
            # 如果没有提供连接，创建新连接
            need_close = False
            if conn is None:
                conn = sqlite3.connect(self.db_path)
                need_close = True

            # 首先尝试从缓存表获取
            try:
                cached_data = pd.read_sql("""
                    SELECT dividend_date, adjustment_amount
                    FROM cb_dividend_adjustments
                    WHERE ts_code = ?
                    ORDER BY dividend_date
                """, conn, params=[ts_code])

                # 检查是否已经缓存过（包括空结果的缓存标记）
                if not cached_data.empty:
                    # 过滤掉空结果标记，只返回真实的除息日数据
                    real_data = cached_data[cached_data['dividend_date'] != 'NO_DIVIDEND_DATA']

                    # 如果有真实的除息日数据，直接返回
                    if not real_data.empty:
                        if need_close:
                            conn.close()
                        return [(row['dividend_date'], row['adjustment_amount'])
                               for _, row in real_data.iterrows()]

                    # 如果没有真实数据但有缓存记录，说明存在空结果标记
                    # 检查是否存在空结果标记
                    if (cached_data['dividend_date'] == 'NO_DIVIDEND_DATA').any():
                        if need_close:
                            conn.close()
                        return []

            except:
                # 缓存表不存在，需要创建
                self._create_dividend_adjustments_table(conn)

            # 如果缓存中没有数据，检查是否为回测模式
            if backtest_mode:
                print(f"回测模式：跳过 {ts_code} 的API调用，使用现有缓存数据")
                if need_close:
                    conn.close()
                return []

            # 如果缓存中没有数据，先尝试从tushare获取
            dividend_data = self._fetch_dividend_dates_from_tushare(ts_code, conn)

            # 如果tushare没有数据，通过价格跳跃分析生成
            if not dividend_data:
                dividend_data = self._analyze_dividend_dates_from_price_jumps(ts_code, conn)

            # 将结果保存到缓存表（包括空结果）
            self._save_dividend_adjustments_to_cache(ts_code, dividend_data, conn)

            if need_close:
                conn.close()
            return dividend_data

        except Exception as e:
            print(f"获取除息日数据失败: {e}")
            if 'conn' in locals() and need_close:
                conn.close()
            return []

    def _fetch_dividend_dates_from_tushare(self, ts_code, conn=None):
        """
        尝试从tushare获取除息日数据
        
        由于tushare API限制，无法直接获取付息日，改为基于可转债基本信息计算理论付息日

        Args:
            ts_code: 可转债代码
            conn: 数据库连接，如果为None则创建新连接

        Returns:
            list: [(除息日, 调整金额), ...] 按日期排序，如果获取失败返回空列表
        """
        try:
            # 从数据库获取可转债基本信息
            need_close = False
            if conn is None:
                conn = sqlite3.connect(self.db_path)
                need_close = True
                
            basic_info = pd.read_sql("""
                SELECT coupon_rate, pay_per_year, value_date, par, maturity
                FROM cb_basic 
                WHERE ts_code = ?
            """, conn, params=[ts_code])
            
            if need_close:
                conn.close()
            
            if basic_info.empty:
                print(f"未找到 {ts_code} 的基本信息")
                return []
            
            info = basic_info.iloc[0]
            coupon_rate = info.get('coupon_rate', 0)
            pay_per_year = info.get('pay_per_year', 1)
            value_date = info.get('value_date')
            par = info.get('par', 100)
            maturity = info.get('maturity')
            
            if not coupon_rate or coupon_rate <= 0:
                print(f"{ts_code} 票息率为0或无效，无付息")
                return []
            
            if not pay_per_year or pay_per_year <= 0:
                print(f"{ts_code} 付息频率为0或无效，无付息")
                return []
            
            if not value_date:
                print(f"{ts_code} 缺少起息日期")
                return []
                
            # 计算每次付息金额
            annual_coupon = par * coupon_rate / 100
            payment_amount = annual_coupon / pay_per_year
            
            # 计算理论付息日
            dividend_data = []
            try:
                start_date = datetime.strptime(str(value_date), '%Y%m%d')
                
                # 处理到期日：可能是年限或具体日期
                if maturity:
                    try:
                        # 尝试解析为具体日期
                        end_date = datetime.strptime(str(maturity), '%Y%m%d')
                    except ValueError:
                        # 如果失败，假设是年限
                        maturity_years = float(maturity)
                        end_date = start_date + timedelta(days=int(365 * maturity_years))
                else:
                    # 如果没有到期日，假设从起息日起10年
                    end_date = start_date + timedelta(days=365*10)
                
                # 根据付息频率计算付息间隔（月）
                months_interval = 12 // pay_per_year
                
                # 从第一年开始，每年按照付息频率计算付息日
                current_year = start_date.year
                end_year = end_date.year
                
                # 从第二年开始计算付息日（第一年通常不付息）
                for year in range(current_year + 1, end_year + 1):
                    for payment_num in range(1, pay_per_year + 1):
                        # 计算该年第payment_num次付息日
                        # 通常可转债付息日是每年固定日期（如起息日的周年日）
                        payment_date = datetime(year, start_date.month, start_date.day)
                        
                        # 如果一年付息多次，需要调整月份
                        if pay_per_year > 1:
                            additional_months = (payment_num - 1) * months_interval
                            payment_month = start_date.month + additional_months
                            if payment_month > 12:
                                payment_date = datetime(year + 1, payment_month - 12, start_date.day)
                            else:
                                payment_date = datetime(year, payment_month, start_date.day)
                        
                        # 检查付息日是否在有效范围内
                        if payment_date <= start_date or payment_date > end_date:
                            continue
                        
                        if payment_date >= datetime(2020, 1, 1):
                            payment_date_str = payment_date.strftime('%Y%m%d')
                            dividend_data.append((payment_date_str, payment_amount))
                        
            except Exception as e:
                print(f"计算 {ts_code} 付息日期失败: {e}")
                return []
            
            if dividend_data:
                print(f"为 {ts_code} 计算出 {len(dividend_data)} 个付息日（2020-2025年间）")
            
            return sorted(dividend_data, key=lambda x: x[0])

        except Exception as e:
            print(f"从基本信息计算除息日数据失败: {e}")
            return []

    def _create_dividend_adjustments_table(self, conn):
        """创建除息调整缓存表"""
        try:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cb_dividend_adjustments (
                    ts_code TEXT,
                    dividend_date TEXT,
                    adjustment_amount REAL,
                    created_time TEXT,
                    PRIMARY KEY (ts_code, dividend_date)
                )
            """)
            conn.commit()
        except Exception as e:
            print(f"创建除息调整缓存表失败: {e}")

    def _analyze_dividend_dates_from_price_jumps(self, ts_code, conn):
        """
        通过价格跳跃分析识别除息日和调整金额

        Args:
            ts_code: 可转债代码
            conn: 数据库连接

        Returns:
            list: [(除息日, 调整金额), ...] 按日期排序
        """
        try:
            # 获取该可转债的票息金额
            coupon_amount = self._get_coupon_amount(ts_code)
            if coupon_amount <= 0:
                return []

            # 查找所有历史数据来识别除息日
            query = """
            SELECT trade_date, close,
                   LAG(close) OVER (ORDER BY trade_date) as prev_close
            FROM cb_daily
            WHERE ts_code = ?
            ORDER BY trade_date
            """

            all_data = pd.read_sql(query, conn, params=[ts_code])

            if all_data.empty:
                return []

            # 识别所有除息日
            dividend_dates = []

            for _, row in all_data.iterrows():
                if pd.notna(row['prev_close']) and pd.notna(row['close']):
                    price_drop = row['prev_close'] - row['close']
                    # 如果净价下跌幅度在票息金额的70%-130%之间，认为是除息日
                    if price_drop > coupon_amount * 0.7 and price_drop < coupon_amount * 1.3:
                        dividend_dates.append((row['trade_date'], price_drop))

            return sorted(dividend_dates, key=lambda x: x[0])

        except Exception as e:
            print(f"分析除息日失败: {e}")
            return []

    def _save_dividend_adjustments_to_cache(self, ts_code, dividend_data, conn):
        """将除息调整数据保存到缓存表（包括空结果）"""
        try:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            if dividend_data:
                # 保存真实的除息日数据
                for dividend_date, adjustment_amount in dividend_data:
                    conn.execute("""
                        INSERT OR REPLACE INTO cb_dividend_adjustments
                        (ts_code, dividend_date, adjustment_amount, created_time)
                        VALUES (?, ?, ?, ?)
                    """, (ts_code, dividend_date, adjustment_amount, current_time))

                print(f"已缓存 {ts_code} 的 {len(dividend_data)} 个除息日数据")
            else:
                # 保存空结果标记，避免重复API调用
                conn.execute("""
                    INSERT OR REPLACE INTO cb_dividend_adjustments
                    (ts_code, dividend_date, adjustment_amount, created_time)
                    VALUES (?, ?, ?, ?)
                """, (ts_code, 'NO_DIVIDEND_DATA', 0.0, current_time))

                print(f"已缓存 {ts_code} 的空除息日数据标记")

            conn.commit()

        except Exception as e:
            print(f"保存除息调整缓存失败: {e}")

    def _get_cumulative_payment_adjustment(self, ts_code, trade_date):
        """
        获取累积的除息调整金额

        计算该日期之前所有除息日的票息金额总和，确保全价连续性

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期

        Returns:
            float: 累积除息调整金额
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            # 获取该可转债的票息金额
            coupon_amount = self._get_coupon_amount(ts_code)
            if coupon_amount <= 0:
                return 0.0

            # 查找所有历史数据来识别除息日
            query = """
            SELECT trade_date, close,
                   LAG(close) OVER (ORDER BY trade_date) as prev_close
            FROM cb_daily
            WHERE ts_code = ?
            ORDER BY trade_date
            """

            all_data = pd.read_sql(query, conn, params=[ts_code])
            conn.close()

            if all_data.empty:
                return 0.0

            # 识别所有除息日
            payment_dates = []

            for _, row in all_data.iterrows():
                if pd.notna(row['prev_close']) and pd.notna(row['close']):
                    price_drop = row['prev_close'] - row['close']
                    # 如果净价下跌幅度在票息金额的70%-130%之间，认为是除息日
                    if price_drop > coupon_amount * 0.7 and price_drop < coupon_amount * 1.3:
                        payment_dates.append(row['trade_date'])

            # 计算该日期之前（包括当天）的除息次数
            payment_count = 0
            for payment_date in payment_dates:
                if payment_date <= trade_date:
                    payment_count += 1

            # 返回累积的除息调整金额
            return payment_count * coupon_amount

        except Exception as e:
            print(f"计算累积除息调整失败: {e}")
            return 0.0

    def _get_payment_adjustment(self, ts_code, trade_date):
        """
        获取付息日的调整金额（保留原方法以兼容）

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期

        Returns:
            float: 调整金额（付息日返回票息金额，其他日期返回0）
        """
        try:
            # 检查是否是付息日
            if not self._is_payment_date(ts_code, trade_date):
                return 0.0

            # 获取票息金额，但只在付息日当天加回
            coupon_amount = self._get_coupon_amount(ts_code)

            # 验证：检查净价是否确实下跌了（确认是真正的付息日）
            if self._verify_payment_date_by_price_drop(ts_code, trade_date, coupon_amount):
                return coupon_amount
            else:
                return 0.0

        except Exception as e:
            print(f"计算付息调整失败: {e}")
            return 0.0

    def _verify_payment_date_by_price_drop(self, ts_code, trade_date, expected_coupon):
        """
        通过净价下跌验证是否真的是付息日

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期
            expected_coupon: 预期票息金额

        Returns:
            bool: 是否确认为付息日
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            # 获取当日和前一交易日的净价
            query = """
            SELECT trade_date, close
            FROM cb_daily
            WHERE ts_code = ? AND trade_date <= ?
            ORDER BY trade_date DESC LIMIT 2
            """
            price_data = pd.read_sql(query, conn, params=[ts_code, trade_date])
            conn.close()

            if len(price_data) < 2:
                return False

            current_price = price_data.iloc[0]['close']
            prev_price = price_data.iloc[1]['close']

            # 计算净价下跌
            price_drop = prev_price - current_price

            # 如果净价下跌接近预期票息金额（误差在50%以内），确认为付息日
            if price_drop > expected_coupon * 0.5 and price_drop < expected_coupon * 1.5:
                return True

            return False

        except Exception as e:
            return False

    def _is_payment_date(self, ts_code, trade_date):
        """
        检查是否是付息日

        Args:
            ts_code: 可转债代码
            trade_date: 交易日期

        Returns:
            bool: 是否是付息日
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            # 获取基本信息
            query = """
            SELECT value_date, pay_per_year FROM cb_basic
            WHERE ts_code = ?
            """
            basic_info = pd.read_sql(query, conn, params=[ts_code])
            conn.close()

            if basic_info.empty:
                return False

            value_date = basic_info.iloc[0]['value_date']
            pay_per_year = basic_info.iloc[0]['pay_per_year']

            # 对于年付息，检查是否是起息日的周年日
            if pay_per_year == 1:
                trade_dt = datetime.strptime(str(trade_date), '%Y%m%d')
                value_dt = datetime.strptime(str(value_date), '%Y%m%d')

                # 检查月日是否相同，且年份大于起息年份
                return (trade_dt.month == value_dt.month and
                       trade_dt.day == value_dt.day and
                       trade_dt.year > value_dt.year)

            return False

        except Exception as e:
            return False

    def _get_coupon_amount(self, ts_code):
        """
        获取票息金额

        Args:
            ts_code: 可转债代码

        Returns:
            float: 票息金额
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            query = """
            SELECT coupon_rate, pay_per_year, par FROM cb_basic
            WHERE ts_code = ?
            """
            basic_info = pd.read_sql(query, conn, params=[ts_code])
            conn.close()

            if basic_info.empty:
                return 0.0

            coupon_rate = basic_info.iloc[0]['coupon_rate'] / 100.0  # 转换为小数
            pay_per_year = basic_info.iloc[0]['pay_per_year']
            par_value = basic_info.iloc[0]['par'] or 100
            pay_per_year = 1 if pay_per_year < 1 else pay_per_year
            return par_value * coupon_rate / pay_per_year

        except Exception as e:
            return 0.0

    def fetch_cb_call(self):
        """获取可转债强制赎回数据"""
        print("获取可转债强制赎回数据...")
        try:
            # 检查API频率限制
            self._check_api_rate_limit("cb_call")

            # 从tushare获取强制赎回数据，先获取所有字段看看有哪些
            df = self.pro.cb_call()

            if df.empty:
                print("未获取到强制赎回数据")
                return

            print(f"获取到 {len(df)} 条强制赎回记录")
            print(f"字段列表: {df.columns.tolist()}")

            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清空现有数据
            cursor.execute("DELETE FROM cb_call")

            # 插入新数据，根据实际字段调整
            for _, row in df.iterrows():
                cursor.execute("""
                INSERT OR REPLACE INTO cb_call
                (ts_code, call_date, call_price, call_type, call_reason)
                VALUES (?, ?, ?, ?, ?)
                """, (
                    row.get('ts_code', ''),
                    row.get('call_date', ''),
                    row.get('call_price', 0),
                    row.get('call_type', ''),
                    row.get('call_reason', '') or row.get('reason', '') or ''
                ))

            conn.commit()
            conn.close()
            print(f"已保存 {len(df)} 条强制赎回数据")

        except Exception as e:
            print(f"获取强制赎回数据失败: {e}")
            import traceback
            traceback.print_exc()

    def fetch_cb_basic(self):
        """获取可转债基本信息"""
        print("获取可转债基本信息...")
        try:
            # 检查API频率限制
            self._check_api_rate_limit("cb_basic")

            # 从tushare获取数据（所有字段都已验证存在于API中）
            df = self.pro.cb_basic(fields='ts_code,bond_full_name,bond_short_name,cb_code,stk_code,stk_short_name,maturity,par,issue_price,issue_size,remain_size,value_date,maturity_date,rate_type,coupon_rate,add_rate,pay_per_year,list_date,delist_date,exchange,conv_start_date,conv_end_date,conv_stop_date,first_conv_price,conv_price,rate_clause')

            # 添加更新时间
            df['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cb_basic'")
            table_exists = cursor.fetchone() is not None

            if not table_exists:
                # 表不存在，直接创建
                df.to_sql('cb_basic', conn, if_exists='replace', index=False)
            else:
                # 表存在，先删除所有数据，然后插入新数据
                cursor.execute("DELETE FROM cb_basic")

                # 逐行插入数据，忽略不存在的字段
                cursor.execute("PRAGMA table_info(cb_basic)")
                existing_columns = [col[1] for col in cursor.fetchall()]

                # 只保留表中存在的字段
                df_filtered = df[[col for col in df.columns if col in existing_columns]]

                # 插入数据
                df_filtered.to_sql('cb_basic', conn, if_exists='append', index=False)

            conn.commit()
            conn.close()
            
            print(f"获取可转债基本信息成功，共{len(df)}条记录")
            return df
        except Exception as e:
            print(f"获取可转债基本信息失败: {e}")
            return None
    
    def clean_zero_amount_data(self):
        """
        清理数据库中 amount=0 或 amount 为 null 的数据
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询需要删除的记录数量
            count_query = """
            SELECT COUNT(*) as count FROM cb_daily
            WHERE amount IS NULL OR amount = 0 OR amount <= 0
            """
            count_result = pd.read_sql(count_query, conn)
            delete_count = count_result.iloc[0]['count']

            if delete_count > 0:
                print(f"发现 {delete_count} 条 amount=0 或无效的记录，正在清理...")

                # 删除 amount=0 或无效的记录
                delete_query = """
                DELETE FROM cb_daily
                WHERE amount IS NULL OR amount = 0 OR amount <= 0
                """
                cursor.execute(delete_query)
                conn.commit()

                print(f"已清理 {delete_count} 条无效记录")
            else:
                print("数据库中没有发现 amount=0 的记录")

            conn.close()
            return delete_count

        except Exception as e:
            print(f"清理数据库失败: {e}")
            return 0

    def fetch_cb_daily(self, ts_code=None, start_date=None, end_date=None):
        """
        获取可转债日行情数据

        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
        """
        print(f"获取可转债日行情数据: {ts_code}, {start_date} - {end_date}...")

        # 首先清理数据库中现有的 amount=0 数据
        self.clean_zero_amount_data()
        
        try:
            # 如果未指定可转债代码，则获取所有可转债（包括已退市的）
            if ts_code is None:
                # 从数据库获取所有可转债代码（不过滤退市状态）
                conn = sqlite3.connect(self.db_path)
                df_basic = pd.read_sql("""
                    SELECT ts_code, delist_date FROM cb_basic
                """, conn)
                conn.close()

                if df_basic.empty:
                    print("未找到可转债基本信息")
                    return None

                ts_codes = df_basic['ts_code'].tolist()
                # 统计在交易和已退市的数量
                active_count = len(df_basic[
                    (df_basic['delist_date'].isna()) |
                    (df_basic['delist_date'] == '') |
                    (df_basic['delist_date'] > pd.Timestamp.now().strftime('%Y%m%d'))
                ])
                delisted_count = len(df_basic) - active_count
                print(f"找到 {len(ts_codes)} 只可转债（在交易: {active_count}只，已退市: {delisted_count}只）")
            else:
                ts_codes = [ts_code]
            
            # 获取日行情数据
            all_data = []
            total_codes = len(ts_codes)
            skipped_count = 0
            successful_count = 0
            no_data_count = 0
            error_count = 0
            
            for idx, code in enumerate(ts_codes):
                # 检查是否已有该代码在指定日期范围的完整数据
                conn = sqlite3.connect(self.db_path)
                
                # 计算期望的交易日数量（简单估算，工作日约为总日数的70%）
                if start_date and end_date:
                    from datetime import datetime, timedelta
                    start_dt = datetime.strptime(start_date, '%Y%m%d')
                    end_dt = datetime.strptime(end_date, '%Y%m%d')
                    total_days = (end_dt - start_dt).days + 1
                    expected_trading_days = max(1, int(total_days * 0.7))  # 简单估算交易日数量
                    
                    existing_query = "SELECT COUNT(*) as count FROM cb_daily WHERE ts_code = ?"
                    params = [code]
                    
                    if start_date:
                        existing_query += " AND trade_date >= ?"
                        params.append(start_date)
                    if end_date:
                        existing_query += " AND trade_date <= ?"
                        params.append(end_date)
                    
                    existing_count = pd.read_sql(existing_query, conn, params=params).iloc[0]['count']
                    
                    # 只有当已有数据接近期望交易日数量时才跳过（允许10%的误差）
                    if existing_count >= expected_trading_days * 0.9 and ts_code is None:
                        print(f"  {code} 已有 {existing_count} 条记录（期望约{expected_trading_days}条），跳过")
                        skipped_count += 1
                        conn.close()
                        continue
                else:
                    # 如果没有指定日期范围，检查是否有任何数据
                    existing_count = pd.read_sql("SELECT COUNT(*) as count FROM cb_daily WHERE ts_code = ?", conn, params=[code]).iloc[0]['count']
                    # 对于没有指定日期的情况，如果有一定数量的数据就跳过
                    if existing_count > 10 and ts_code is None:
                        print(f"  {code} 已有 {existing_count} 条历史记录，跳过")
                        skipped_count += 1
                        conn.close()
                        continue
                
                conn.close()
                
                try:
                    # 检查API频率限制
                    self._check_api_rate_limit("cb_daily")
                    print(f"下载{code}的日行情数据... ({idx + 1}/{total_codes})")

                    df = self.pro.cb_daily(ts_code=code, start_date=start_date, end_date=end_date)
                    if not df.empty:
                        # 过滤掉 amount=0 或 amount 为 null/NaN 的数据
                        initial_count = len(df)
                        df = df[
                            (df['amount'].notna()) &
                            (df['amount'] != 0) &
                            (df['amount'] > 0)
                        ]
                        filtered_count = len(df)
                        if initial_count > filtered_count:
                            print(f"  过滤掉 {initial_count - filtered_count} 条 amount=0 的记录")

                        if df.empty:
                            print(f"  过滤后无有效数据")
                            no_data_count += 1
                            continue



                        # 获取该可转债的基本信息以计算全价
                        conn_basic = sqlite3.connect(self.db_path)
                        basic_info = pd.read_sql(
                            "SELECT coupon_rate, pay_per_year, value_date, par FROM cb_basic WHERE ts_code = ?",
                            conn_basic, params=[code]
                        )
                        conn_basic.close()

                        if not basic_info.empty:
                            coupon_rate = basic_info.iloc[0]['coupon_rate'] or 0
                            pay_per_year = basic_info.iloc[0]['pay_per_year'] or 1
                            value_date = basic_info.iloc[0]['value_date']
                            par_value = basic_info.iloc[0]['par'] or 100

                            # 计算应计利息和全价
                            df['accrued_interest'] = df['trade_date'].apply(
                                lambda x: self.calculate_accrued_interest(
                                    x, value_date, coupon_rate, pay_per_year, par_value
                                )
                            )
                            # 计算full_price（净价+应计利息+除息调整）
                            df['full_price'] = df.apply(
                                lambda row: self.calculate_full_price(
                                    row['close'], 
                                    row['accrued_interest'], 
                                    row['ts_code'], 
                                    row['trade_date']
                                ), axis=1
                            )

                            # 处理应计利息字段的nan值：用前后两天的数据取平均值
                            if df['accrued_interest'].isna().any():
                                df['accrued_interest'] = df['accrued_interest'].interpolate(method='linear', limit_direction='both')
                                # 如果插值后仍有nan，使用前项填充
                                df['accrued_interest'] = df['accrued_interest'].fillna(method='ffill')
                        else:
                            # 如果没有基本信息，设置默认值
                            df['accrued_interest'] = 0.0

                        print(f"  获取到 {len(df)} 条新记录")
                        all_data.append(df)
                        successful_count += 1
                    else:
                        # 只在查询单个债券时显示"未获取到数据"
                        if ts_code is not None:
                            print(f"  未获取到数据")
                        no_data_count += 1

                except Exception as e:
                    print(f"获取{code}的日行情数据失败: {e}")
                    # 如果是频率限制错误，等待更长时间
                    if "每分钟最多访问" in str(e) or "频率限制" in str(e):
                        print("遇到频率限制，等待70秒...")
                        time.sleep(70)
                        # 重置该API的计数器
                        if "cb_daily" in self.api_call_history:
                            self.api_call_history["cb_daily"] = 0
                            self.api_start_times["cb_daily"] = time.time()
                    error_count += 1

            # 输出统计信息
            print(f"\n📊 数据获取统计:")
            print(f"  成功获取: {successful_count} 只转债")
            print(f"  无数据: {no_data_count} 只转债")
            print(f"  跳过: {skipped_count} 只转债")
            if error_count > 0:
                print(f"  错误: {error_count} 只转债")
            
            if not all_data:
                print("未获取到任何新的日行情数据")
                return None
            
            # 合并数据
            df_daily = pd.concat(all_data, ignore_index=True)
            
            # 使用INSERT OR IGNORE来避免重复数据
            conn = sqlite3.connect(self.db_path)
            batch_size = 1000  # 每批保存1000条记录
            total_records = len(df_daily)
            saved_count = 0
            
            for i in range(0, total_records, batch_size):
                batch_df = df_daily.iloc[i:i+batch_size]
                
                # 使用INSERT OR IGNORE避免主键冲突
                for _, row in batch_df.iterrows():
                    try:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT OR IGNORE INTO cb_daily
                            (ts_code, trade_date, pre_close, open, high, low, close, change, pct_chg, vol, amount, accrued_interest, full_price)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            row['ts_code'], row['trade_date'],
                            row.get('pre_close'), row.get('open'), row.get('high'),
                            row.get('low'), row.get('close'),
                            row.get('change'), row.get('pct_chg'), row.get('vol'), row.get('amount'),
                            row.get('accrued_interest', 0.0), row.get('full_price', row.get('close'))
                        ))
                        if cursor.rowcount > 0:
                            saved_count += 1
                    except Exception as e:
                        print(f"保存记录失败: {e}")
                
                print(f"已处理 {min(i+batch_size, total_records)}/{total_records} 条记录")
            
            conn.commit()
            conn.close()
            
            print(f"获取可转债日行情数据成功，实际保存 {saved_count} 条新记录（总获取 {len(df_daily)} 条）")
            return df_daily
        
        except Exception as e:
            print(f"获取可转债日行情数据失败: {e}")
            return None
    
    def fetch_stock_daily(self, ts_code=None, start_date=None, end_date=None):
        """
        获取正股日行情数据
        
        Args:
            ts_code: 股票代码，如果为None则获取所有可转债对应的正股
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
        """
        print(f"获取正股日行情数据: {ts_code}, {start_date} - {end_date}...")
        
        try:
            # 如果未指定股票代码，则获取所有可转债对应的正股
            if ts_code is None:
                # 从数据库获取所有可转债对应的正股代码
                conn = sqlite3.connect(self.db_path)
                df_basic = pd.read_sql("SELECT DISTINCT stk_code FROM cb_basic WHERE stk_code IS NOT NULL", conn)
                conn.close()
                
                if df_basic.empty:
                    print("未找到可转债基本信息，请先获取可转债基本信息")
                    return None
                
                # stk_code已经是tushare格式，直接使用
                ts_codes = []
                for code in df_basic['stk_code'].tolist():
                    if code and isinstance(code, str):
                        ts_codes.append(code)
            else:
                ts_codes = [ts_code]
            
            # 获取日行情数据
            all_data = []
            total_codes = len(ts_codes)
            skipped_count = 0
            
            for idx, code in enumerate(ts_codes):
                print(f"检查{code}的日行情数据... ({idx+1}/{total_codes})")
                
                # 检查是否已有该代码在指定日期范围的完整数据
                conn = sqlite3.connect(self.db_path)
                
                # 计算期望的交易日数量（简单估算，工作日约为总日数的70%）
                if start_date and end_date:
                    from datetime import datetime, timedelta
                    start_dt = datetime.strptime(start_date, '%Y%m%d')
                    end_dt = datetime.strptime(end_date, '%Y%m%d')
                    total_days = (end_dt - start_dt).days + 1
                    expected_trading_days = max(1, int(total_days * 0.7))  # 简单估算交易日数量
                    
                    existing_query = "SELECT COUNT(*) as count FROM stock_daily WHERE ts_code = ?"
                    params = [code]
                    
                    if start_date:
                        existing_query += " AND trade_date >= ?"
                        params.append(start_date)
                    if end_date:
                        existing_query += " AND trade_date <= ?"
                        params.append(end_date)
                    
                    existing_count = pd.read_sql(existing_query, conn, params=params).iloc[0]['count']
                    
                    # 只有当已有数据接近期望交易日数量时才跳过（允许10%的误差）
                    if existing_count >= expected_trading_days * 0.9 and ts_code is None:
                        print(f"  {code} 已有 {existing_count} 条记录（期望约{expected_trading_days}条），跳过")
                        skipped_count += 1
                        conn.close()
                        continue
                else:
                    # 如果没有指定日期范围，检查是否有任何数据
                    existing_count = pd.read_sql("SELECT COUNT(*) as count FROM stock_daily WHERE ts_code = ?", conn, params=[code]).iloc[0]['count']
                    # 对于没有指定日期的情况，如果有一定数量的数据就跳过
                    if existing_count > 10 and ts_code is None:
                        print(f"  {code} 已有 {existing_count} 条历史记录，跳过")
                        skipped_count += 1
                        conn.close()
                        continue
                
                conn.close()
                
                try:
                    # 检查API频率限制
                    self._check_api_rate_limit("daily")

                    df = self.pro.daily(ts_code=code, start_date=start_date, end_date=end_date)
                    if not df.empty:
                        print(f"  获取到 {len(df)} 条新记录")
                        all_data.append(df)
                    else:
                        # 只在查询单个股票时显示"未获取到数据"
                        if ts_code is not None:
                            print(f"  未获取到数据")

                except Exception as e:
                    print(f"获取{code}的日行情数据失败: {e}")
                    # 如果是频率限制错误，等待更长时间
                    if "每分钟最多访问" in str(e) or "频率限制" in str(e):
                        print("遇到频率限制，等待70秒...")
                        time.sleep(70)
                        # 重置该API的计数器
                        if "daily" in self.api_call_history:
                            self.api_call_history["daily"] = 0
                            self.api_start_times["daily"] = time.time()
            
            if skipped_count > 0:
                print(f"共跳过 {skipped_count} 个已有数据的正股")
            
            if not all_data:
                print("未获取到任何新的日行情数据")
                return None
            
            # 合并数据
            df_daily = pd.concat(all_data, ignore_index=True)

            # 使用pro_bar接口获取前复权价格数据并替换
            print("正在获取前复权价格数据...")
            df_daily = self._get_forward_adjusted_prices(df_daily, start_date, end_date)

            # 使用INSERT OR IGNORE来避免重复数据
            conn = sqlite3.connect(self.db_path)
            batch_size = 1000  # 每批保存1000条记录
            total_records = len(df_daily)
            saved_count = 0

            for i in range(0, total_records, batch_size):
                batch_df = df_daily.iloc[i:i+batch_size]

                # 使用INSERT OR IGNORE避免主键冲突
                for _, row in batch_df.iterrows():
                    try:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT OR IGNORE INTO stock_daily
                            (ts_code, trade_date, pre_close, open, high, low, close, org_close, change, pct_chg, vol, amount)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            row['ts_code'], row['trade_date'],
                            row.get('pre_close'), row.get('open'), row.get('high'),
                            row.get('low'), row.get('close'), row.get('org_close'),
                            row.get('change'), row.get('pct_chg'), row.get('vol'), row.get('amount')
                        ))
                        if cursor.rowcount > 0:
                            saved_count += 1
                    except Exception as e:
                        print(f"保存记录失败: {e}")

                print(f"已处理 {min(i+batch_size, total_records)}/{total_records} 条记录")
            
            conn.commit()
            conn.close()
            
            print(f"获取正股日行情数据成功，实际保存 {saved_count} 条新记录（总获取 {len(df_daily)} 条）")
            return df_daily
        
        except Exception as e:
            print(f"获取正股日行情数据失败: {e}")
            return None

    def _get_forward_adjusted_prices(self, df_daily, start_date, end_date):
        """
        使用pro_bar接口获取前复权价格数据并替换原始价格

        Args:
            df_daily: 原始日行情数据DataFrame
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD

        Returns:
            DataFrame: 替换为前复权价格的数据
        """
        if df_daily.empty:
            return df_daily

        # 为每只股票单独获取前复权数据
        adjusted_data = []

        for ts_code in df_daily['ts_code'].unique():
            stock_data = df_daily[df_daily['ts_code'] == ts_code].copy()
            
            # 保存原始收盘价（如果还没有org_close列）
            if 'org_close' not in stock_data.columns:
                stock_data['org_close'] = stock_data['close'].copy()

            try:
                print(f"  获取 {ts_code} 的前复权价格数据...")

                # 使用pro_bar接口获取前复权数据
                # 检查API频率限制
                self._check_api_rate_limit("pro_bar")

                df_qfq = ts.pro_bar(
                    ts_code=ts_code,
                    api=self.pro,        # 传入api对象
                    asset='E',           # E=股票
                    start_date=start_date,
                    end_date=end_date,
                    freq='D',            # 日线
                    adj='qfq'            # 前复权
                )

                if df_qfq is not None and not df_qfq.empty:
                    # 将前复权数据按日期索引，方便查找
                    df_qfq = df_qfq.set_index('trade_date')

                    # 替换原始数据中的价格字段
                    for idx, row in stock_data.iterrows():
                        trade_date = row['trade_date']

                        if trade_date in df_qfq.index:
                            qfq_row = df_qfq.loc[trade_date]

                            # 替换为前复权价格
                            stock_data.loc[idx, 'open'] = qfq_row.get('open', row['open'])
                            stock_data.loc[idx, 'high'] = qfq_row.get('high', row['high'])
                            stock_data.loc[idx, 'low'] = qfq_row.get('low', row['low'])
                            stock_data.loc[idx, 'close'] = qfq_row.get('close', row['close'])
                            # pre_close保持原值，因为它已经是前复权的

                    print(f"  成功替换 {ts_code} 的前复权价格数据")
                else:
                    print(f"  未获取到 {ts_code} 的前复权数据，保持原始价格")

            except Exception as e:
                print(f"  获取 {ts_code} 前复权数据失败: {e}，保持原始价格")
                # 如果是频率限制错误，等待更长时间
                if "每分钟最多访问" in str(e) or "频率限制" in str(e):
                    print("遇到频率限制，等待70秒...")
                    time.sleep(70)
                    # 重置该API的计数器
                    if "pro_bar" in self.api_call_history:
                        self.api_call_history["pro_bar"] = 0
                        self.api_start_times["pro_bar"] = time.time()

            adjusted_data.append(stock_data)

        # 合并所有股票的调整后数据
        result_df = pd.concat(adjusted_data, ignore_index=True)

        print(f"前复权价格处理完成，处理了 {len(df_daily['ts_code'].unique())} 只股票")
        return result_df
    
    def fetch_cb_price_chg(self, ts_code=None):
        """
        获取可转债转股价调整记录
        
        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
        """
        print(f"获取可转债转股价调整记录: {ts_code}...")
        
        try:
            # 如果未指定可转债代码，则获取所有可转债（包括已退市的）
            if ts_code is None:
                # 从数据库获取所有可转债代码（不过滤退市状态）
                conn = sqlite3.connect(self.db_path)
                df_basic = pd.read_sql("""
                    SELECT ts_code FROM cb_basic
                """, conn)
                
                # 检查已经获取过转股价调整记录的可转债
                try:
                    df_existing = pd.read_sql("SELECT DISTINCT ts_code FROM cb_price_chg", conn)
                    existing_codes = df_existing['ts_code'].tolist() if not df_existing.empty else []
                except:
                    existing_codes = []
                
                conn.close()
                
                if df_basic.empty:
                    print("未找到可转债基本信息，请先获取可转债基本信息")
                    return None
                
                # 过滤掉已经获取过的代码，实现断点续传
                all_codes = df_basic['ts_code'].tolist()
                ts_codes = [code for code in all_codes if code not in existing_codes]
                
                if not ts_codes:
                    print("所有可转债的转股价调整记录都已获取")
                    return None
                
                print(f"需要获取转股价调整记录的可转债数量: {len(ts_codes)} (总共: {len(all_codes)}, 已完成: {len(existing_codes)})")
            else:
                ts_codes = [ts_code]
            
            # 获取转股价调整记录，使用新的频率控制
            all_data = []

            for i, code in enumerate(ts_codes):
                print(f"获取{code}的转股价调整记录... ({i+1}/{len(ts_codes)})")

                try:
                    # 使用新的API频率控制
                    self._check_api_rate_limit("cb_price_chg")

                    df = self.pro.cb_price_chg(ts_code=code)
                    
                    if not df.empty:
                        all_data.append(df)
                        # 立即保存到数据库，避免数据丢失
                        conn = sqlite3.connect(self.db_path)
                        
                        # 检查表结构并确保兼容性
                        cursor = conn.cursor()
                        
                        # 获取当前表的列名
                        cursor.execute("PRAGMA table_info(cb_price_chg)")
                        existing_columns = [row[1] for row in cursor.fetchall()]
                        
                        # 检查表结构是否需要更新
                        need_rebuild = False
                        for col in df.columns:
                            if col not in existing_columns:
                                need_rebuild = True
                                break
                        
                        if need_rebuild:
                            print("检测到表结构需要更新，正在重建cb_price_chg表...")
                            
                            # 备份现有数据
                            try:
                                backup_df = pd.read_sql("SELECT * FROM cb_price_chg", conn)
                            except:
                                backup_df = pd.DataFrame()
                            
                            # 删除旧表
                            cursor.execute("DROP TABLE IF EXISTS cb_price_chg")
                            
                            # 重新创建表结构
                            cursor.execute('''
                            CREATE TABLE cb_price_chg (
                                ts_code TEXT,
                                bond_short_name TEXT,
                                publish_date TEXT,
                                change_date TEXT,
                                convert_price_initial REAL,
                                convertprice_bef REAL,
                                convertprice_aft REAL,
                                PRIMARY KEY (ts_code, change_date)
                            )
                            ''')
                            
                            # 恢复备份数据（如果有的话）
                            if not backup_df.empty:
                                # 添加缺失的列
                                for col in df.columns:
                                    if col not in backup_df.columns:
                                        backup_df[col] = None
                                backup_df.to_sql('cb_price_chg', conn, if_exists='append', index=False)
                        
                        # 保存新数据
                        df.to_sql('cb_price_chg', conn, if_exists='append', index=False)
                        conn.close()
                        print(f"  -> 获取到 {len(df)} 条记录并已保存")
                    else:
                        print(f"  -> 无转股价调整记录")
                    
                except Exception as e:
                    print(f"获取{code}的转股价调整记录失败: {e}")
                    # 如果是频率限制错误，等待更长时间
                    if "每分钟最多访问" in str(e) or "频率限制" in str(e):
                        print("遇到频率限制，等待70秒...")
                        time.sleep(70)
                        # 重置该API的计数器
                        if "cb_price_chg" in self.api_call_history:
                            self.api_call_history["cb_price_chg"] = 0
                            self.api_start_times["cb_price_chg"] = time.time()
            
            if not all_data:
                print("未获取到任何转股价调整记录")
                return None
            
            # 合并数据
            df_price_chg = pd.concat(all_data, ignore_index=True)
            
            print(f"获取可转债转股价调整记录成功，共{len(df_price_chg)}条记录")
            return df_price_chg
        
        except Exception as e:
            print(f"获取可转债转股价调整记录失败: {e}")
            return None

    def fetch_cb_dividend_adjustments(self, ts_code=None):
        """
        获取可转债除息日调整数据

        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
        """
        print("获取可转债除息日调整数据...")

        try:
            # 如果未指定可转债代码，则获取所有可转债
            if ts_code is None:
                # 从数据库获取所有可转债代码
                conn = sqlite3.connect(self.db_path)
                df_basic = pd.read_sql("""
                    SELECT ts_code FROM cb_basic
                """, conn)

                # 检查已经获取过除息日数据的可转债
                try:
                    df_existing = pd.read_sql("SELECT DISTINCT ts_code FROM cb_dividend_adjustments", conn)
                    existing_codes = df_existing['ts_code'].tolist() if not df_existing.empty else []
                except:
                    existing_codes = []

                conn.close()

                if df_basic.empty:
                    print("未找到可转债基本信息，请先获取可转债基本信息")
                    return None

                # 过滤掉已经获取过的代码，实现断点续传
                all_codes = df_basic['ts_code'].tolist()
                ts_codes = [code for code in all_codes if code not in existing_codes]

                if not ts_codes:
                    print("所有可转债的除息日数据都已获取")
                    return None

                print(f"需要获取除息日数据的可转债: {len(ts_codes)}只")
            else:
                ts_codes = [ts_code]

            # 批量获取除息日数据
            total_codes = len(ts_codes)
            success_count = 0

            for idx, code in enumerate(ts_codes):
                print(f"获取{code}的除息日数据... ({idx+1}/{total_codes})")

                try:
                    # 调用_get_dividend_dates_and_amounts方法，它会自动缓存数据
                    dividend_data = self._get_dividend_dates_and_amounts(code)
                    if dividend_data:
                        success_count += 1
                        print(f"  获取到 {len(dividend_data)} 个除息日")
                    else:
                        print(f"  未找到除息日数据")

                    # 添加延时避免频繁请求
                    time.sleep(0.1)

                except Exception as e:
                    print(f"获取{code}除息日数据失败: {e}")
                    continue

            print(f"除息日数据获取完成，成功: {success_count}/{total_codes}")
            return True

        except Exception as e:
            print(f"获取可转债除息日数据失败: {e}")
            return None

    def update_dividend_adjustment(self, ts_code, dividend_date, adjustment_amount):
        """
        手动更新或添加除息日调整数据

        Args:
            ts_code: 可转债代码
            dividend_date: 除息日，格式YYYYMMDD
            adjustment_amount: 调整金额
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            # 确保表存在
            self._create_dividend_adjustments_table(conn)

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            conn.execute("""
                INSERT OR REPLACE INTO cb_dividend_adjustments
                (ts_code, dividend_date, adjustment_amount, created_time)
                VALUES (?, ?, ?, ?)
            """, (ts_code, dividend_date, adjustment_amount, current_time))

            conn.commit()
            conn.close()

            print(f"已更新 {ts_code} 在 {dividend_date} 的除息调整: {adjustment_amount}")
            return True

        except Exception as e:
            print(f"更新除息调整数据失败: {e}")
            return False

    def get_dividend_adjustments(self, ts_code=None):
        """
        查询除息日调整数据

        Args:
            ts_code: 可转债代码，如果为None则查询所有

        Returns:
            DataFrame: 除息调整数据
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            if ts_code:
                df = pd.read_sql("""
                    SELECT * FROM cb_dividend_adjustments
                    WHERE ts_code = ? AND dividend_date != 'NO_DIVIDEND_DATA'
                    ORDER BY dividend_date
                """, conn, params=[ts_code])
            else:
                df = pd.read_sql("""
                    SELECT * FROM cb_dividend_adjustments
                    WHERE dividend_date != 'NO_DIVIDEND_DATA'
                    ORDER BY ts_code, dividend_date
                """, conn)

            conn.close()
            return df

        except Exception as e:
            print(f"查询除息调整数据失败: {e}")
            return pd.DataFrame()

    def clear_dividend_cache(self, ts_code=None):
        """
        清除除息日缓存数据（包括空结果标记）

        Args:
            ts_code: 可转债代码，如果为None则清除所有缓存

        Returns:
            bool: 是否成功
        """
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)

            if ts_code:
                conn.execute("""
                    DELETE FROM cb_dividend_adjustments
                    WHERE ts_code = ?
                """, (ts_code,))
                print(f"已清除 {ts_code} 的除息日缓存")
            else:
                conn.execute("DELETE FROM cb_dividend_adjustments")
                print("已清除所有除息日缓存")

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"清除除息日缓存失败: {e}")
            return False

    def fetch_all_data(self, start_date=None, end_date=None):
        """
        获取所有数据
        
        Args:
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
        """
        # 转换日期格式
        if start_date:
            start_date = start_date.replace('-', '')
        if end_date:
            end_date = end_date.replace('-', '')
        
        # 获取可转债基本信息
        self.fetch_cb_basic()
        
        # 获取可转债日行情数据
        self.fetch_cb_daily(start_date=start_date, end_date=end_date)
        
        # 获取正股日行情数据
        self.fetch_stock_daily(start_date=start_date, end_date=end_date)
        
        # 获取可转债转股价调整记录
        self.fetch_cb_price_chg()

        # 获取可转债除息日数据
        self.fetch_cb_dividend_adjustments()

        self.fetch_cb_call()
        print("所有数据获取完成")
    
    def update_data(self):
        """更新数据到最新日期"""
        # 获取数据库中最新的日期
        conn = sqlite3.connect(self.db_path)
        
        # 获取可转债日行情最新日期
        try:
            latest_cb_date = pd.read_sql("SELECT MAX(trade_date) as latest_date FROM cb_daily", conn).iloc[0]['latest_date']
        except:
            latest_cb_date = None
        
        # 获取正股日行情最新日期
        try:
            latest_stock_date = pd.read_sql("SELECT MAX(trade_date) as latest_date FROM stock_daily", conn).iloc[0]['latest_date']
        except:
            latest_stock_date = None
        
        conn.close()
        
        # 计算开始日期
        if latest_cb_date:
            # 将字符串日期转换为datetime对象
            latest_date = datetime.strptime(latest_cb_date, '%Y%m%d')
            # 开始日期为最新日期的下一天
            start_date = (latest_date + timedelta(days=1)).strftime('%Y%m%d')
        else:
            # 如果没有数据，则默认从30天前开始
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        # 结束日期为今天
        end_date = datetime.now().strftime('%Y%m%d')
        
        # 更新可转债基本信息
        self.fetch_cb_basic()
        
        # 更新可转债日行情数据
        self.fetch_cb_daily(start_date=start_date, end_date=end_date)
        
        # 更新正股日行情数据
        self.fetch_stock_daily(start_date=start_date, end_date=end_date)
        
        # 更新可转债转股价调整记录
        self.fetch_cb_price_chg()

        # 更新可转债除息日数据
        self.fetch_cb_dividend_adjustments()

        self.fetch_cb_call()
        print("数据更新完成")

    def update_close_for_existing_data(self, force_update=False):
        """
        为已有的cb_daily数据更新全价字段
        包含同时更新cb_dividend_adjustments表的逻辑
        
        Args:
            force_update: 是否强制更新所有记录（默认False，只更新空值）
        """
        print("开始更新已有数据的全价字段...")

        try:
            # 使用超时设置避免长时间锁定
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            # 设置WAL模式以减少锁定
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")

            # 首先确保cb_dividend_adjustments表存在
            self._create_dividend_adjustments_table(conn)

            # 获取所有需要更新的记录
            if force_update:
                query = """
                SELECT cd.ts_code, cd.trade_date, cd.close,
                       cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par
                FROM cb_daily cd
                JOIN cb_basic cb ON cd.ts_code = cb.ts_code
                ORDER BY cd.ts_code, cd.trade_date
                """
                print("强制更新模式：将更新所有记录的full_price")
            else:
                query = """
                SELECT cd.ts_code, cd.trade_date, cd.close,
                       cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par
                FROM cb_daily cd
                JOIN cb_basic cb ON cd.ts_code = cb.ts_code
                WHERE cd.full_price IS NULL OR cd.full_price = 0
                ORDER BY cd.ts_code, cd.trade_date
                """
                print("增量更新模式：只更新full_price为空的记录")

            df = pd.read_sql(query, conn)

            print(f"需要更新 {len(df)} 条记录的全价字段")

            if df.empty:
                print("没有需要更新的记录")
                conn.close()
                return

            # 获取所有需要更新的可转债代码
            unique_codes = df['ts_code'].unique()
            print(f"涉及 {len(unique_codes)} 只可转债")

            # 首先更新cb_dividend_adjustments表
            print("正在更新cb_dividend_adjustments表...")
            dividend_updated_count = 0
            
            for ts_code in unique_codes:
                try:
                    # 获取该可转债的除息调整数据（传递现有连接避免锁定）
                    dividend_data = self._get_dividend_dates_and_amounts(ts_code, conn)
                    
                    if dividend_data:
                        # 清除旧数据
                        conn.execute("DELETE FROM cb_dividend_adjustments WHERE ts_code = ?", (ts_code,))
                        
                        # 插入新数据
                        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        for dividend_date, adjustment_amount in dividend_data:
                            conn.execute("""
                                INSERT OR REPLACE INTO cb_dividend_adjustments
                                (ts_code, dividend_date, adjustment_amount, created_time)
                                VALUES (?, ?, ?, ?)
                            """, (ts_code, dividend_date, adjustment_amount, current_time))
                        
                        dividend_updated_count += 1
                        print(f"  {ts_code}: 更新了 {len(dividend_data)} 个付息日")
                    else:
                        # 插入空结果标记
                        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        conn.execute("""
                            INSERT OR REPLACE INTO cb_dividend_adjustments
                            (ts_code, dividend_date, adjustment_amount, created_time)
                            VALUES (?, ?, ?, ?)
                        """, (ts_code, 'NO_DIVIDEND_DATA', 0.0, current_time))
                        
                except Exception as e:
                    print(f"  {ts_code}: 更新除息调整数据失败: {e}")

            conn.commit()
            print(f"cb_dividend_adjustments表更新完成，共更新 {dividend_updated_count} 只转债")

            # 重新预加载除息调整数据
            dividend_cache = self._preload_dividend_adjustments(conn)

            # 批量更新full_price
            batch_size = 1000
            updated_count = 0

            for i in range(0, len(df), batch_size):
                batch_df = df.iloc[i:i+batch_size]

                for _, row in batch_df.iterrows():
                    # 计算应计利息
                    accrued_interest = self.calculate_accrued_interest(
                        row['trade_date'],
                        row['value_date'],
                        row['coupon_rate'] or 0,
                        row['pay_per_year'] or 1,
                        row['par'] or 100
                    )

                    # 计算全价（使用缓存的除息调整数据）
                    full_price = self._calculate_full_price_with_cache(
                        row['close'],
                        accrued_interest,
                        row['ts_code'],
                        row['trade_date'],
                        dividend_cache
                    )

                    # 更新数据库
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE cb_daily
                        SET accrued_interest = ?, full_price = ?
                        WHERE ts_code = ? AND trade_date = ?
                    ''', (accrued_interest, full_price, row['ts_code'], row['trade_date']))

                    if cursor.rowcount > 0:
                        updated_count += 1

                print(f"已更新 {min(i+batch_size, len(df))}/{len(df)} 条记录")

            conn.commit()

            # 对所有数据进行NaN值处理
            print("开始处理NaN值...")
            self._fix_nan_values_in_database(conn)

            conn.close()

            print(f"全价字段更新完成，共更新 {updated_count} 条记录")

        except Exception as e:
            print(f"更新全价字段失败: {e}")
            import traceback
            traceback.print_exc()
            if 'conn' in locals():
                conn.close()

    def _fix_nan_values_in_database(self, conn):
        """
        处理数据库中full_price和accrued_interest字段的NaN值
        使用线性插值和前向填充的方法

        Args:
            conn: 数据库连接
        """
        try:
            # 获取所有可转债代码
            ts_codes = pd.read_sql("SELECT DISTINCT ts_code FROM cb_daily ORDER BY ts_code", conn)

            for _, row in ts_codes.iterrows():
                ts_code = row['ts_code']

                # 获取该可转债的所有数据，按日期排序
                query = """
                SELECT ts_code, trade_date, full_price, accrued_interest
                FROM cb_daily
                WHERE ts_code = ?
                ORDER BY trade_date
                """
                df = pd.read_sql(query, conn, params=[ts_code])

                if df.empty:
                    continue

                # 处理full_price的NaN值
                if df['full_price'].isna().any():
                    df['full_price'] = df['full_price'].interpolate(method='linear', limit_direction='both')
                    df['full_price'] = df['full_price'].fillna(method='ffill')

                # 处理accrued_interest的NaN值
                if df['accrued_interest'].isna().any():
                    df['accrued_interest'] = df['accrued_interest'].interpolate(method='linear', limit_direction='both')
                    df['accrued_interest'] = df['accrued_interest'].fillna(method='ffill')

                # 更新数据库
                for _, data_row in df.iterrows():
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE cb_daily
                        SET full_price = ?, accrued_interest = ?
                        WHERE ts_code = ? AND trade_date = ?
                    """, (data_row['full_price'], data_row['accrued_interest'],
                          data_row['ts_code'], data_row['trade_date']))

            conn.commit()
            print("NaN值处理完成")

        except Exception as e:
            print(f"处理NaN值失败: {e}")

    def _preload_dividend_adjustments(self, conn):
        """
        预加载所有除息调整数据到内存中

        Args:
            conn: 数据库连接

        Returns:
            dict: {ts_code: [(dividend_date, adjustment_amount), ...]}
        """
        dividend_cache = {}

        try:
            # 确保除息调整表存在
            self._create_dividend_adjustments_table(conn)

            # 加载所有除息调整数据，排除空结果标记
            df = pd.read_sql("""
                SELECT ts_code, dividend_date, adjustment_amount
                FROM cb_dividend_adjustments
                WHERE dividend_date != 'NO_DIVIDEND_DATA'
                ORDER BY ts_code, dividend_date
            """, conn)

            # 按ts_code分组
            for ts_code, group in df.groupby('ts_code'):
                dividend_cache[ts_code] = [
                    (row['dividend_date'], row['adjustment_amount'])
                    for _, row in group.iterrows()
                ]

        except Exception as e:
            print(f"预加载除息调整数据失败: {e}")

        return dividend_cache

    def _calculate_full_price_with_cache(self, net_price, accrued_interest, ts_code, trade_date, dividend_cache):
        """
        使用缓存的除息调整数据计算全价

        Args:
            net_price: 净价
            accrued_interest: 应计利息（不使用，保留参数以兼容）
            ts_code: 可转债代码
            trade_date: 交易日期
            dividend_cache: 预加载的除息调整数据缓存

        Returns:
            float: 连续全价
        """
        if net_price is None:
            return net_price

        # 基础全价 = 净价（不加应计利息）
        basic_full_price = net_price

        # 计算累积除息调整
        cumulative_adjustment = 0.0

        if ts_code in dividend_cache:
            for dividend_date, adjustment_amount in dividend_cache[ts_code]:
                if dividend_date <= trade_date:
                    cumulative_adjustment += adjustment_amount

        return basic_full_price + cumulative_adjustment




class DataCache:
    """数据缓存类，负责数据缓存和读取"""
    
    def __init__(self):
        """初始化数据缓存器"""
        # 设置数据库路径
        self.db_path = config_manager.get_db_path()
        
        # 设置缓存目录
        self.cache_dir = config_manager.get_cache_dir()
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def get_cb_basic(self):
        """获取可转债基本信息"""
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql("SELECT * FROM cb_basic", conn)
        conn.close()
        return df
    
    def get_cb_daily(self, ts_code=None, start_date=None, end_date=None):
        """
        获取可转债日行情数据
        
        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
        """
        # 转换日期格式
        if start_date:
            start_date = start_date.replace('-', '')
        if end_date:
            end_date = end_date.replace('-', '')
        
        # 构建SQL查询
        sql = "SELECT * FROM cb_daily WHERE 1=1"
        params = []
        
        if ts_code:
            sql += " AND ts_code = ?"
            params.append(ts_code)
        
        if start_date:
            sql += " AND trade_date >= ?"
            params.append(start_date)
        
        if end_date:
            sql += " AND trade_date <= ?"
            params.append(end_date)
        
        # 执行查询
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql(sql, conn, params=params)
        conn.close()

        # 直接使用收盘价，不再使用全价字段
        df['price'] = df['close']
        
        # 如果有org_close字段，也提供原始价格
        if 'org_close' in df.columns:
            df['org_price'] = df['org_close']

        return df
    
    def get_stock_daily(self, ts_code=None, start_date=None, end_date=None):
        """
        获取正股日行情数据
        
        Args:
            ts_code: 股票代码，如果为None则获取所有正股
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
        """
        # 转换日期格式
        if start_date:
            start_date = start_date.replace('-', '')
        if end_date:
            end_date = end_date.replace('-', '')
        
        # 构建SQL查询
        sql = "SELECT * FROM stock_daily WHERE 1=1"
        params = []
        
        if ts_code:
            sql += " AND ts_code = ?"
            params.append(ts_code)
        
        if start_date:
            sql += " AND trade_date >= ?"
            params.append(start_date)
        
        if end_date:
            sql += " AND trade_date <= ?"
            params.append(end_date)
        
        # 执行查询
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql(sql, conn, params=params)
        conn.close()
        
        return df
    
    def get_cb_price_chg(self, ts_code=None):
        """
        获取可转债转股价调整记录

        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
        """
        # 构建SQL查询
        sql = "SELECT * FROM cb_price_chg WHERE 1=1"
        params = []

        if ts_code:
            sql += " AND ts_code = ?"
            params.append(ts_code)

        # 执行查询
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql(sql, conn, params=params)
        conn.close()

        return df
    
    def get_cb_factor(self, ts_code=None, trade_date=None):
        """
        获取可转债因子数据
        
        Args:
            ts_code: 可转债代码，如果为None则获取所有可转债
            trade_date: 交易日期，格式YYYY-MM-DD，如果为None则获取最新日期
        """
        # 转换日期格式
        if trade_date:
            trade_date = trade_date.replace('-', '')
        
        # 构建SQL查询
        sql = "SELECT * FROM cb_factor WHERE 1=1"
        params = []
        
        if ts_code:
            sql += " AND ts_code = ?"
            params.append(ts_code)
        
        if trade_date:
            sql += " AND trade_date = ?"
            params.append(trade_date)
        else:
            # 获取最新日期
            sql += " AND trade_date = (SELECT MAX(trade_date) FROM cb_factor)"
        
        # 执行查询
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql(sql, conn, params=params)
        conn.close()
        
        return df
    
    def save_factor(self, df):
        """
        保存因子数据（使用INSERT OR REPLACE避免重复）

        Args:
            df: 因子数据DataFrame
        """
        # 保存到数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 使用INSERT OR REPLACE来处理重复数据
        saved_count = 0
        for _, row in df.iterrows():
            try:
                # 构建动态SQL语句
                columns = list(row.index)
                placeholders = ', '.join(['?' for _ in columns])
                column_names = ', '.join(columns)

                cursor.execute(f'''
                    INSERT OR REPLACE INTO cb_factor ({column_names})
                    VALUES ({placeholders})
                ''', tuple(row.values))
                saved_count += 1
            except Exception as e:
                print(f"保存因子记录失败: {e}")

        conn.commit()
        conn.close()

        print(f"因子数据保存成功，共{saved_count}条记录")


class DataProcessor:
    """数据处理类，负责数据清洗和预处理"""
    
    def __init__(self):
        """初始化数据处理器"""
        # 初始化数据缓存器
        self.data_cache = DataCache()
    
    def get_cb_with_stock(self, trade_date=None):
        """
        获取可转债和对应正股的数据
        
        Args:
            trade_date: 交易日期，格式YYYY-MM-DD，如果为None则获取最新日期
        """
        # 转换日期格式
        if trade_date:
            date_str = trade_date.replace('-', '')
        else:
            # 获取最新交易日期
            conn = sqlite3.connect(config_manager.get_db_path())
            latest_date = pd.read_sql("SELECT MAX(trade_date) as latest_date FROM cb_daily", conn).iloc[0]['latest_date']
            conn.close()
            date_str = latest_date
        
        # 获取可转债基本信息
        df_basic = self.data_cache.get_cb_basic()
        
        # 获取可转债日行情数据
        df_cb = self.data_cache.get_cb_daily(start_date=date_str, end_date=date_str)
        
        # 获取正股日行情数据
        df_stock = self.data_cache.get_stock_daily(start_date=date_str, end_date=date_str)
        
        # 合并数据
        # 1. 先合并可转债基本信息和日行情
        df = pd.merge(df_cb, df_basic, on='ts_code', how='left')
        
        # 2. 再合并正股日行情
        # 将正股代码转换为tushare格式
        df['stk_ts_code'] = df['stk_code'].apply(lambda x: f"{x}.SH" if x.startswith('6') else f"{x}.SZ")
        
        # 合并正股日行情
        df = pd.merge(df, df_stock, left_on=['stk_ts_code', 'trade_date'], right_on=['ts_code', 'trade_date'], how='left', suffixes=('', '_stock'))
        
        return df
    
    def get_historical_data(self, ts_code, start_date, end_date):
        """
        获取历史数据
        
        Args:
            ts_code: 可转债代码
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
        """
        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')
        
        # 获取可转债基本信息
        df_basic = self.data_cache.get_cb_basic()
        df_basic = df_basic[df_basic['ts_code'] == ts_code]
        
        # 获取可转债日行情数据
        df_cb = self.data_cache.get_cb_daily(ts_code=ts_code, start_date=start_date_str, end_date=end_date_str)
        
        # 获取正股代码
        stk_code = df_basic['stk_code'].iloc[0]
        stk_ts_code = f"{stk_code}.SH" if stk_code.startswith('6') else f"{stk_code}.SZ"
        
        # 获取正股日行情数据
        df_stock = self.data_cache.get_stock_daily(ts_code=stk_ts_code, start_date=start_date_str, end_date=end_date_str)
        
        # 获取可转债转股价调整记录
        df_price_chg = self.data_cache.get_cb_price_chg(ts_code=ts_code)
        
        # 合并数据
        # 1. 先合并可转债基本信息和日行情
        df = pd.merge(df_cb, df_basic, on='ts_code', how='left')
        
        # 2. 再合并正股日行情
        df['stk_ts_code'] = stk_ts_code
        df = pd.merge(df, df_stock, left_on=['stk_ts_code', 'trade_date'], right_on=['ts_code', 'trade_date'], how='left', suffixes=('', '_stock'))
        
        return df, df_price_chg
    

    
    def get_cb_premium_data(self, trade_date=None, min_premium_ratio=None):
        """
        获取转股溢价率数据

        Args:
            trade_date: 交易日期，格式YYYY-MM-DD
            min_premium_ratio: 最小转股溢价率过滤条件

        Returns:
            包含转股溢价率数据的DataFrame
        """
        # 转换日期格式
        if trade_date:
            date_str = trade_date.replace('-', '')
        else:
            # 获取最新交易日期
            conn = sqlite3.connect(config_manager.get_db_path())
            latest_date = pd.read_sql("SELECT MAX(trade_date) as latest_date FROM cb_factor", conn).iloc[0]['latest_date']
            conn.close()
            date_str = latest_date

        # 构建SQL查询，通过JOIN获取价格数据而不是从cb_factor表
        sql = """
        SELECT
            f.ts_code,
            f.trade_date,
            COALESCE(cd.org_close, cd.close) as cb_price,
            sd.close as stock_price,
            cb.conv_price,
            f.conv_value,
            f.premium_ratio
        FROM cb_factor f
        JOIN cb_daily cd ON f.ts_code = cd.ts_code AND f.trade_date = cd.trade_date
        JOIN cb_basic cb ON f.ts_code = cb.ts_code
        LEFT JOIN stock_daily sd ON (
            CASE
                WHEN cb.stk_code LIKE '%.%' THEN cb.stk_code
                WHEN cb.stk_code LIKE '6%' THEN cb.stk_code || '.SH'
                ELSE cb.stk_code || '.SZ'
            END
        ) = sd.ts_code AND f.trade_date = sd.trade_date
        WHERE f.trade_date = ? AND f.premium_ratio IS NOT NULL
        """
        params = [date_str]

        if min_premium_ratio is not None:
            sql += " AND f.premium_ratio >= ?"
            params.append(min_premium_ratio)

        sql += " ORDER BY f.premium_ratio DESC"

        # 执行查询
        conn = sqlite3.connect(config_manager.get_db_path())
        df = pd.read_sql(sql, conn, params=params)
        conn.close()

        return df
