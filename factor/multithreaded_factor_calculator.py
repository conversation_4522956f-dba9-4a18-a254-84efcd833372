"""\n多线程因子计算器\n提供多线程加速的因子计算功能，同时支持因子类型过滤\n"""

import pandas as pd
import numpy as np
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue
import time

from .base_factor_calculator import FactorCalculator, CompositeFactorCalculator
# 避免循环导入，在方法内部导入
# from .factor_calculator_factory import FactorCalculatorFactory
from config.config import config_manager


class MultithreadedFactorCalculator:
    """\n    多线程因子计算器\n    \n    提供以下功能：\n    1. 多线程并行计算因子，大幅提升计算速度\n    2. 支持因子类型过滤，只计算指定类型的因子\n    3. 智能任务分配和负载均衡\n    4. 线程安全的数据合并\n    """
    
    def __init__(self, calculator_type: str = 'enhanced', max_workers: int = None, **kwargs):
        """
        初始化多线程因子计算器
        
        Args:
            calculator_type: 基础计算器类型
            max_workers: 最大线程数，默认为CPU核心数
            **kwargs: 传递给基础计算器的参数
        """
        self.calculator_type = calculator_type
        self.calculator_kwargs = kwargs
        self.max_workers = max_workers or min(8, (os.cpu_count() or 1) + 4)
        self.db_path = config_manager.get_db_path()
        self.logger = logging.getLogger(__name__)
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 支持的因子类型过滤
        self.factor_type_mapping = {
            'enhanced_only': self._get_enhanced_factors,
            'basic_only': self._get_basic_factors,
            'technical_only': self._get_technical_factors,
            'momentum_only': self._get_momentum_factors,
            'zl_only': self._get_zl_factors,
            'double_low_only': self._get_double_low_factors
        }
        
    def calculate_factors_for_period(self, start_date: str, end_date: str, 
                                   factor_filter: Optional[str] = None,
                                   chunk_size: int = 30, **kwargs) -> pd.DataFrame:
        """
        多线程计算指定时间段的因子
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            factor_filter: 因子类型过滤器
            chunk_size: 每个线程处理的天数
            **kwargs: 其他参数
            
        Returns:
            计算结果DataFrame
        """
        print(f"🚀 启动多线程因子计算 - 时间段: {start_date} 到 {end_date}")
        print(f"📊 线程数: {self.max_workers}, 分块大小: {chunk_size}天")
        
        if factor_filter:
            print(f"🎯 因子过滤: {factor_filter}")
        
        # 获取交易日列表
        trading_dates = self._get_trading_dates(start_date, end_date)
        if not trading_dates:
            print("❌ 未找到交易日数据")
            return pd.DataFrame()
        
        print(f"📅 总交易日数: {len(trading_dates)}")
        
        # 将交易日分块
        date_chunks = self._split_dates_into_chunks(trading_dates, chunk_size)
        print(f"📦 分为 {len(date_chunks)} 个数据块")
        
        # 多线程计算
        all_results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_chunk = {
                executor.submit(
                    self._calculate_chunk_factors, 
                    chunk, 
                    factor_filter, 
                    i+1, 
                    len(date_chunks),
                    **kwargs
                ): chunk for i, chunk in enumerate(date_chunks)
            }
            
            # 收集结果
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    result = future.result()
                    if not result.empty:
                        all_results.append(result)
                except Exception as e:
                    self.logger.error(f"计算数据块失败: {e}")
                    print(f"❌ 数据块计算失败: {e}")
        
        # 合并结果
        if all_results:
            final_result = pd.concat(all_results, ignore_index=True)
            final_result = final_result.sort_values(['trade_date', 'ts_code'])
            
            elapsed_time = time.time() - start_time
            print(f"✅ 多线程计算完成!")
            print(f"⏱️  总耗时: {elapsed_time:.2f}秒")
            print(f"📈 计算记录数: {len(final_result)}")
            print(f"🚄 平均速度: {len(final_result)/elapsed_time:.1f} 记录/秒")
            
            return final_result
        else:
            print("❌ 没有计算出任何因子数据")
            return pd.DataFrame()
    
    def _calculate_chunk_factors(self, date_chunk: List[str], factor_filter: Optional[str],
                               chunk_id: int, total_chunks: int, **kwargs) -> pd.DataFrame:
        """
        计算单个数据块的因子
        
        Args:
            date_chunk: 日期列表
            factor_filter: 因子过滤器
            chunk_id: 块ID
            total_chunks: 总块数
            **kwargs: 其他参数
            
        Returns:
            该数据块的因子DataFrame
        """
        thread_id = threading.current_thread().ident
        print(f"  🧵 线程{thread_id} - 块[{chunk_id}/{total_chunks}]: {date_chunk[0]} 到 {date_chunk[-1]} ({len(date_chunk)}天)")
        
        try:
            # 为每个线程创建独立的计算器实例
            calculator = self._create_calculator_instance()
            
            # 计算该数据块的因子
            start_date = date_chunk[0]
            end_date = date_chunk[-1]
            
            # 转换日期格式
            start_date_formatted = self._format_date_for_calculator(start_date)
            end_date_formatted = self._format_date_for_calculator(end_date)
            
            result = calculator.calculate_factors_for_period(
                start_date_formatted, end_date_formatted, **kwargs
            )
            
            # 应用因子过滤
            if factor_filter and not result.empty:
                result = self._apply_factor_filter(result, factor_filter)
            
            print(f"  ✅ 线程{thread_id} - 块[{chunk_id}/{total_chunks}]: 完成 {len(result)} 条记录")
            return result
            
        except Exception as e:
            print(f"  ❌ 线程{thread_id} - 块[{chunk_id}/{total_chunks}]: 失败 - {e}")
            self.logger.error(f"线程{thread_id}计算块{chunk_id}失败: {e}")
            return pd.DataFrame()
    
    def _create_calculator_instance(self) -> FactorCalculator:
        """
        为线程创建独立的计算器实例
        
        Returns:
            因子计算器实例
        """
        # 在方法内部导入以避免循环导入
        from .factor_calculator_factory import FactorCalculatorFactory
        
        if self.calculator_type == 'enhanced':
            from .enhanced_factor_calculator import EnhancedFactorCalculator
            return EnhancedFactorCalculator()
        elif self.calculator_type == 'double_low':
            from .double_low_factor_calculator import DoubleLowFactorCalculator
            return DoubleLowFactorCalculator(**self.calculator_kwargs)
        else:
            return FactorCalculatorFactory.create_calculator(
                self.calculator_type, **self.calculator_kwargs
            )
    
    def _get_trading_dates(self, start_date: str, end_date: str) -> List[str]:
        """
        获取交易日列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日列表
        """
        conn = sqlite3.connect(self.db_path)
        try:
            start_date_db = start_date.replace('-', '')
            end_date_db = end_date.replace('-', '')
            
            query = """
            SELECT DISTINCT trade_date
            FROM cb_daily
            WHERE trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """
            
            df = pd.read_sql(query, conn, params=[start_date_db, end_date_db])
            return df['trade_date'].tolist()
            
        except Exception as e:
            self.logger.error(f"获取交易日失败: {e}")
            return []
        finally:
            conn.close()
    
    def _split_dates_into_chunks(self, dates: List[str], chunk_size: int) -> List[List[str]]:
        """
        将日期列表分割成块
        
        Args:
            dates: 日期列表
            chunk_size: 每块大小
            
        Returns:
            日期块列表
        """
        chunks = []
        for i in range(0, len(dates), chunk_size):
            chunk = dates[i:i + chunk_size]
            chunks.append(chunk)
        return chunks
    
    def _format_date_for_calculator(self, date_str: str) -> str:
        """
        格式化日期供计算器使用
        
        Args:
            date_str: 日期字符串
            
        Returns:
            格式化后的日期
        """
        if len(date_str) == 8:  # YYYYMMDD
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return date_str
    
    def _apply_factor_filter(self, df: pd.DataFrame, factor_filter: str) -> pd.DataFrame:
        """
        应用因子过滤器
        
        Args:
            df: 原始因子DataFrame
            factor_filter: 过滤器类型
            
        Returns:
            过滤后的DataFrame
        """
        if factor_filter not in self.factor_type_mapping:
            print(f"⚠️  未知的因子过滤器: {factor_filter}")
            return df
        
        filter_func = self.factor_type_mapping[factor_filter]
        return filter_func(df)
    
    def _get_enhanced_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留增强因子"""
        enhanced_factors = [
            'time_decay_factor', 'forced_call_pressure', 'put_protection_value',
            'conversion_tendency', 'conversion_arbitrage_efficiency', 'stock_limit_impact',
            'net_inflow_factor', 'relative_strength_vs_market', 'liquidity_shock',
            'momentum_vol_cross', 'momentum_consistency', 'value_quality_score',
            'arbitrage_efficiency', 'premium_ratio_squared', 'momentum_rank_score',
            'volatility_regime', 'ma5_ma20_cross', 'ma_slope_5d', 'support_strength',
            'resistance_pressure', 'gap_factor', 'price_volume_divergence'
        ]
        
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in enhanced_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]
    
    def _get_basic_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留基础因子"""
        basic_factors = ['conv_value', 'premium_ratio', 'combined_factor']
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in basic_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]
    
    def _get_technical_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留技术因子"""
        technical_factors = [
            'bb_position', 'bb_width', 'bb_deviation', 'volume_percentile', 'amount_percentile',
            'volume_change_5d', 'volume_change_10d', 'volume_change_20d', 'volume_trend',
            'volatility_5d', 'volatility_10d', 'volatility_20d', 'atr_14d', 'relative_volatility',
            'price_percentile', 'price_position', 'drawdown_from_high', 'gain_from_low',
            'avg_turnover_20d', 'turnover_cv', 'amihud_illiquidity',
            'trend_slope_5d', 'trend_slope_10d', 'trend_slope_20d', 'trend_consistency',
            'rsi_14d', 'williams_r', 'obv_trend', 'macd'
        ]
        
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in technical_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]
    
    def _get_momentum_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留动量因子"""
        momentum_factors = ['momentum_5d', 'momentum_10d', 'momentum_20d']
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in momentum_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]
    
    def _get_zl_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留ZL模型因子"""
        zl_factors = [
            'zl_price', 'theoretical_price', 'arbitrage_space', 'model_delta', 'corr_coef', 
            'reg_slope', 'bond_floor', 'option_value', 'cb_return', 'stock_return', 
            'predicted_return', 'expected_return'
        ]
        
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in zl_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]
    
    def _get_double_low_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """只保留双低因子"""
        double_low_factors = [
            'price_factor', 'premium_factor', 'premium_ratio', 'double_low_score',
            'double_low_rank', 'double_low_percentile'
        ]
        
        base_columns = ['ts_code', 'trade_date']
        keep_columns = base_columns + [col for col in double_low_factors if col in df.columns]
        return df[keep_columns] if keep_columns else df[base_columns]


# 导入os模块
import os


if __name__ == "__main__":
    # 测试多线程因子计算器
    calculator = MultithreadedFactorCalculator(
        calculator_type='enhanced',
        max_workers=4
    )
    
    # 测试计算
    result = calculator.calculate_factors_for_period(
        start_date='2024-01-01',
        end_date='2024-01-31',
        factor_filter='enhanced_only',
        chunk_size=10
    )
    
    if not result.empty:
        print(f"测试成功，计算了 {len(result)} 条记录")
        print(f"因子列: {result.columns.tolist()}")
    else:
        print("测试失败，未计算出数据")