"""
ZL模型因子计算器
基于增强版ZL模型计算套利空间等因子
"""

import pandas as pd
import numpy as np
import sqlite3
import time
import logging
from typing import Dict, Any
from scipy import stats
from sklearn.preprocessing import QuantileTransformer
from tqdm import tqdm

from .base_factor_calculator import FactorCalculator
from .enhanced_zl_model import EnhancedZLModel


class ZLFactorCalculator(FactorCalculator):
    """
    ZL模型因子计算器
    
    专门负责计算ZL模型相关的因子：
    - 理论价格
    - 套利空间
    - 动态套利空间
    - 敏感度分析指标
    """
    
    def __init__(self, precision_level='medium', use_dynamic_params=True):
        """
        初始化ZL因子计算器
        
        Args:
            precision_level: 精度等级
            use_dynamic_params: 是否使用动态参数
        """
        super().__init__("ZL模型因子计算器")
        self.zl_model = EnhancedZLModel(
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params
        )
        self.precision_level = precision_level
        print(f"ZL模型因子计算器初始化完成 - 精度: {precision_level}")
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
    

    
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的ZL模型因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含ZL模型因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🔄 计算ZL模型因子 - 日期: {date_str}")
        start_time = time.time()
        
        # 获取可转债数据
        df = self._get_cb_data_for_date(date_str)
        
        if df.empty:
            print(f"日期 {date_str} 没有可用的可转债数据")
            return pd.DataFrame()
        
        print(f"找到 {len(df)} 只可转债")
        
        # 计算ZL模型因子
        df = self._calculate_zl_factors(df, date_str)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ ZL模型因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的ZL模型因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含ZL模型因子的DataFrame
        """
        print(f"\n🔄 计算ZL模型因子 - 时间段: {start_date} 到 {end_date}")
        
        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')
        
        # 获取交易日
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)
        
        if not trading_dates:
            print("指定时间段内没有交易日数据")
            return pd.DataFrame()
        
        print(f"找到 {len(trading_dates)} 个交易日")
        
        all_factors = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")
            
            try:
                daily_factors = self.calculate_factors_for_date(trade_date, **kwargs)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                self.logger.error(f"处理日期 {trade_date} 时出错: {e}")
                continue
        
        if not all_factors:
            print("没有成功计算出任何因子数据")
            return pd.DataFrame()
        
        # 合并所有日期的数据
        result_df = pd.concat(all_factors, ignore_index=True)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        print(f"✅ ZL模型因子计算完成，共 {len(result_df)} 条记录")
        
        return result_df
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的ZL模型因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数
            
        Returns:
            包含单个债券ZL模型因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🎯 计算单债券ZL模型因子 - 日期: {date_str}, 债券: {bond_code}")
        start_time = time.time()
        
        # 获取指定可转债的数据
        df = self._get_single_cb_data(date_str, bond_code)
        
        if df.empty:
            raise ValueError(f"债券 {bond_code} 在日期 {date_str} 没有找到数据")
        
        print(f"找到债券: {bond_code}")
        
        # 计算ZL模型因子
        df = self._calculate_zl_factors(df, date_str)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ 单债券ZL模型因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def _get_cb_data_for_date(self, date_str: str) -> pd.DataFrame:
        """
        获取指定日期的可转债数据（ZL模型专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD

        Returns:
            包含可转债数据的DataFrame
        """
        # 使用基类的通用方法，指定ZL模型所需字段和过滤条件
        additional_filters = "cb.conv_price > 0"
        return super()._get_cb_data_for_date(
            date_str=date_str,
            fields="zl_model",
            additional_filters=additional_filters,
            strict_validation=False
        )
    
    def _get_single_cb_data(self, date_str: str, bond_code: str) -> pd.DataFrame:
        """
        获取指定日期单个可转债的数据（ZL模型专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            bond_code: 可转债代码

        Returns:
            包含单个可转债数据的DataFrame
        """
        # 使用基类的通用方法，指定ZL模型所需字段和严格验证
        additional_filters = "cb.conv_price > 0"
        return super()._get_single_cb_data(
            date_str=date_str,
            bond_code=bond_code,
            fields="zl_model",
            additional_filters=additional_filters,
            strict_validation=True
        )
    
    def _calculate_zl_factors(self, df: pd.DataFrame, date_str: str) -> pd.DataFrame:
        """
        计算ZL模型因子
        
        Args:
            df: 包含可转债数据的DataFrame
            date_str: 日期字符串
            
        Returns:
            添加了ZL模型因子的DataFrame
        """
        if df.empty:
            return df
        
        result = df.copy()
        
        # 计算理论价格和敏感度分析
        print(f"🔄 计算理论价格和敏感度分析...")
        theoretical_prices = []
        bond_floors = []
        option_values = []
        model_deltas = []
        corr_coefs = []
        reg_slopes = []
        cb_returns = []
        stock_returns = []
        predicted_returns = []
        expected_returns = []
        
        for idx, cb_row in tqdm(result.iterrows(), total=len(result), desc="计算理论价格"):
            try:
                # 获取正股数据
                stock_data = self.zl_model._get_stock_data(cb_row['ts_code'], cb_row['trade_date'])
                
                if stock_data is None:
                    # 填充NaN值
                    theoretical_prices.append(np.nan)
                    bond_floors.append(np.nan)
                    option_values.append(np.nan)
                    model_deltas.append(np.nan)
                    corr_coefs.append(np.nan)
                    reg_slopes.append(np.nan)
                    cb_returns.append(np.nan)
                    stock_returns.append(np.nan)
                    predicted_returns.append(np.nan)
                    expected_returns.append(np.nan)
                    continue
                
                # 计算增强版理论价格和敏感度分析
                zl_result = self.zl_model.calculate_theoretical_price_with_sensitivity(cb_row, stock_data)
                
                theoretical_prices.append(zl_result['theoretical_price'])
                bond_floors.append(zl_result['bond_floor'])
                option_values.append(zl_result['option_value'])
                model_deltas.append(zl_result['model_delta'])
                corr_coefs.append(zl_result['corr_coef'])
                reg_slopes.append(zl_result['reg_slope'])
                cb_returns.append(zl_result['cb_return'])
                stock_returns.append(zl_result['stock_return'])
                predicted_returns.append(zl_result['predicted_return'])
                expected_returns.append(zl_result['expected_return'])
                
            except Exception as e:
                self.logger.warning(f"计算 {cb_row.get('ts_code', 'unknown')} 失败: {e}")
                # 填充NaN值
                theoretical_prices.append(np.nan)
                bond_floors.append(np.nan)
                option_values.append(np.nan)
                model_deltas.append(np.nan)
                corr_coefs.append(np.nan)
                reg_slopes.append(np.nan)
                cb_returns.append(np.nan)
                stock_returns.append(np.nan)
                predicted_returns.append(np.nan)
                expected_returns.append(np.nan)
        
        # 添加计算结果到DataFrame
        result['theoretical_price'] = theoretical_prices
        result['bond_floor'] = bond_floors
        result['option_value'] = option_values
        result['model_delta'] = model_deltas
        result['corr_coef'] = corr_coefs
        result['reg_slope'] = reg_slopes
        result['cb_return'] = cb_returns
        result['stock_return'] = stock_returns
        result['predicted_return'] = predicted_returns
        result['expected_return'] = expected_returns
        
        # 计算套利空间
        print(f"计算套利空间...")
        result['arbitrage_space_raw'] = result['theoretical_price'] - result['full_price']
        result['arbitrage_space'] = self._calculate_dynamic_arbitrage_space(result, date_str)

        # 只返回ZL模型特有的因子，避免与基础价格因子计算器重复
        zl_specific_columns = [
            'ts_code', 'trade_date',  # 必需的标识字段
            'theoretical_price', 'bond_floor', 'option_value', 'model_delta',  # ZL模型核心因子
            'corr_coef', 'reg_slope', 'cb_return', 'stock_return',  # 敏感度分析因子
            'predicted_return', 'expected_return',  # 预测因子
            'arbitrage_space_raw', 'arbitrage_space'  # 套利空间因子
        ]

        # 只保留存在的列
        available_columns = [col for col in zl_specific_columns if col in result.columns]
        result = result[available_columns]

        return result
    
    def _calculate_dynamic_arbitrage_space(self, df: pd.DataFrame, date_str: str) -> pd.Series:
        """
        计算动态套利空间
        
        Args:
            df: 包含套利空间数据的DataFrame
            date_str: 日期字符串
            
        Returns:
            动态套利空间Series
        """
        try:
            # 获取历史套利空间数据用于计算动态调整
            conn = sqlite3.connect(self.db_path)
            
            # 查询过去20天的套利空间数据
            query = f"""
            SELECT ts_code, arbitrage_space
            FROM cb_factor
            WHERE trade_date < '{date_str}'
            AND trade_date >= '{int(date_str) - 30}'
            AND arbitrage_space IS NOT NULL
            ORDER BY trade_date DESC
            LIMIT 1000
            """
            
            hist_data = pd.read_sql(query, conn)
            conn.close()
            
            if len(hist_data) < 10:
                # 历史数据不足，直接返回原始套利空间
                return df['arbitrage_space_raw']
            
            # 计算每只债券的历史套利空间均值
            hist_mean = hist_data.groupby('ts_code')['arbitrage_space'].mean()
            
            # 计算动态套利空间
            dynamic_arbitrage_space = []
            for _, row in df.iterrows():
                ts_code = row['ts_code']
                raw_space = row['arbitrage_space_raw']
                
                if pd.isna(raw_space):
                    dynamic_arbitrage_space.append(np.nan)
                    continue
                
                # 获取该债券的历史均值
                if ts_code in hist_mean.index:
                    hist_mean_value = hist_mean[ts_code]
                    # 动态套利空间 = 原始套利空间 - 历史均值
                    dynamic_space = raw_space - hist_mean_value
                else:
                    # 没有历史数据，使用原始值
                    dynamic_space = raw_space
                
                dynamic_arbitrage_space.append(dynamic_space)
            
            return pd.Series(dynamic_arbitrage_space, index=df.index)
            
        except Exception as e:
            self.logger.warning(f"计算动态套利空间失败: {e}")
            return df['arbitrage_space_raw']
