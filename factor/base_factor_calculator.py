"""
基础因子计算器抽象类
定义因子计算器的通用接口和基础功能
"""

from abc import ABC, abstractmethod
import pandas as pd
import sqlite3
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from config.config import config_manager


class FactorCalculator(ABC):
    """
    因子计算器抽象基类
    
    定义所有因子计算器必须实现的接口方法
    """
    
    def __init__(self, name: str):
        """
        初始化因子计算器
        
        Args:
            name: 因子计算器名称
        """
        self.name = name
        self.db_path = config_manager.get_db_path()
        self.logger = logging.getLogger(__name__)
        
    @abstractmethod
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含因子数据的DataFrame
        """
        pass
    
    @abstractmethod
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含因子数据的DataFrame
        """
        pass
    
    @abstractmethod
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数
            
        Returns:
            包含单个债券因子的DataFrame
        """
        pass
    
    def get_name(self) -> str:
        """获取因子计算器名称"""
        return self.name
    
    def _standardize_date(self, date: str) -> str:
        """
        标准化日期格式为YYYYMMDD
        
        Args:
            date: 日期字符串
            
        Returns:
            标准化后的日期字符串
        """
        if isinstance(date, str):
            if '-' in date:
                # 格式如 2025-06-25
                return date.replace('-', '')
            else:
                # 格式如 20250625
                return date
        else:
            return date.strftime('%Y%m%d')
    
    def _get_trading_dates(self, start_date_str: str, end_date_str: str) -> List[str]:
        """
        获取指定时间段内的交易日

        Args:
            start_date_str: 开始日期，格式YYYYMMDD
            end_date_str: 结束日期，格式YYYYMMDD

        Returns:
            交易日列表
        """
        conn = sqlite3.connect(self.db_path)

        try:
            query = """
            SELECT DISTINCT trade_date
            FROM cb_daily
            WHERE trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """

            df = pd.read_sql(query, conn, params=[start_date_str, end_date_str])
            return df['trade_date'].tolist()

        except Exception as e:
            self.logger.error(f"获取交易日时出错: {e}")
            return []
        finally:
            conn.close()

    def _get_cb_data_for_date(self, date_str: str, fields: str = "basic",
                             additional_filters: str = "",
                             strict_validation: bool = False) -> pd.DataFrame:
        """
        通用的可转债数据获取方法

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            fields: 字段类型，可选 "basic", "full", "zl_model"
            additional_filters: 额外的过滤条件
            strict_validation: 是否进行严格验证

        Returns:
            包含可转债数据的DataFrame
        """
        conn = sqlite3.connect(self.db_path)

        try:
            # 根据字段类型选择查询字段
            if fields == "basic":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price
                """
            elif fields == "zl_model":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cb.conv_price, cb.maturity_date, cd.vol as volume, cd.amount, cb.delist_date,
                cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par
                """
            elif fields == "double_low":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cd.vol, cd.amount, cb.conv_price, cb.stk_code, cb.bond_short_name,
                cb.delist_date, cb.list_date, cb.remain_size
                """
            else:  # full
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cb.conv_price, cb.maturity_date, cd.vol as volume, cd.amount, cb.delist_date,
                cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par, cb.stk_code,
                cb.bond_short_name, cb.list_date, cb.remain_size
                """

            # 基础过滤条件
            base_filters = """
            cd.trade_date = ?
            AND cd.close > 0
            AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > ?)
            """

            # 添加额外过滤条件
            if additional_filters:
                base_filters += f" AND {additional_filters}"

            query = f"""
            SELECT {select_fields}
            FROM cb_daily cd
            JOIN cb_basic cb ON cd.ts_code = cb.ts_code
            WHERE {base_filters}
            ORDER BY cd.ts_code
            """

            df = pd.read_sql(query, conn, params=[date_str, date_str])

            # 处理价格数据
            if not df.empty:
                df = self._process_price_data(df, strict_validation)

            return df

        except Exception as e:
            self.logger.error(f"获取可转债数据时出错: {e}")
            return pd.DataFrame()
        finally:
            conn.close()

    def _get_single_cb_data(self, date_str: str, bond_code: str, fields: str = "basic",
                           additional_filters: str = "",
                           strict_validation: bool = False) -> pd.DataFrame:
        """
        通用的单个可转债数据获取方法

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            bond_code: 可转债代码
            fields: 字段类型，可选 "basic", "full", "zl_model"
            additional_filters: 额外的过滤条件
            strict_validation: 是否进行严格验证

        Returns:
            包含单个可转债数据的DataFrame
        """
        conn = sqlite3.connect(self.db_path)

        try:
            # 根据字段类型选择查询字段
            if fields == "basic":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price
                """
            elif fields == "zl_model":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cb.conv_price, cb.maturity_date, cd.vol as volume, cd.amount, cb.delist_date,
                cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par
                """
            elif fields == "double_low":
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cd.vol, cd.amount, cb.conv_price, cb.stk_code, cb.bond_short_name,
                cb.delist_date, cb.list_date, cb.remain_size
                """
            else:  # full
                select_fields = """
                cd.ts_code, cd.trade_date, cd.close, cd.full_price, cd.accrued_interest,
                cb.conv_price, cb.maturity_date, cd.vol as volume, cd.amount, cb.delist_date,
                cb.coupon_rate, cb.pay_per_year, cb.value_date, cb.par, cb.stk_code,
                cb.bond_short_name, cb.list_date, cb.remain_size
                """

            # 基础过滤条件
            base_filters = """
            cd.trade_date = ? AND cd.ts_code = ?
            AND cd.close > 0
            AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > ?)
            """

            # 添加额外过滤条件
            if additional_filters:
                base_filters += f" AND {additional_filters}"

            query = f"""
            SELECT {select_fields}
            FROM cb_daily cd
            JOIN cb_basic cb ON cd.ts_code = cb.ts_code
            WHERE {base_filters}
            """

            df = pd.read_sql(query, conn, params=[date_str, bond_code, date_str])

            # 处理全价数据
            if not df.empty:
                df = self._process_price_data(df, strict_validation, bond_code, date_str)

            return df

        except Exception as e:
            self.logger.error(f"获取单债券数据时出错: {e}")
            return pd.DataFrame()
        finally:
            conn.close()

    def _process_price_data(self, df: pd.DataFrame, strict_validation: bool = False,
                           bond_code: str = None, date_str: str = None) -> pd.DataFrame:
        """
        处理价格数据

        Args:
            df: 数据DataFrame
            strict_validation: 是否进行严格验证
            bond_code: 债券代码（用于错误信息）
            date_str: 日期（用于错误信息）

        Returns:
            处理后的DataFrame
        """
        if df.empty:
            return df

        # 检查价格数据
        if 'close' not in df.columns:
            if strict_validation:
                raise ValueError(f"查询结果中缺少close列")
            else:
                print("警告：缺少close价格数据")
                return df

        # 处理价格缺失值
        if df['close'].isna().all():
            if strict_validation:
                error_msg = f"所有close数据都为空"
                if bond_code and date_str:
                    error_msg = f"债券 {bond_code} 在日期 {date_str} " + error_msg
                raise ValueError(error_msg)
            else:
                print("警告：所有close数据都为空")
                return df

        # 处理应计利息
        if 'accrued_interest' in df.columns:
            if df['accrued_interest'].isna().all() and strict_validation:
                error_msg = f"缺少应计利息数据"
                if bond_code and date_str:
                    error_msg = f"债券 {bond_code} 在日期 {date_str} " + error_msg
                raise ValueError(error_msg)
            else:
                df['accrued_interest'] = df['accrued_interest'].fillna(0.0)

        # 严格验证模式下的额外检查
        if strict_validation and bond_code and date_str:
            if df['close'].isna().any():
                print(f"警告：债券 {bond_code} 在日期 {date_str} 仍有close NaN值")

            if 'accrued_interest' in df.columns and df['accrued_interest'].isna().any():
                print(f"警告：债券 {bond_code} 在日期 {date_str} 仍有accrued_interest NaN值")

        return df


class CompositeFactorCalculator(FactorCalculator):
    """
    组合因子计算器
    
    维护多个子因子计算器，遍历调用计算因子
    """
    
    def __init__(self, name: str = "组合因子计算器"):
        """
        初始化组合因子计算器
        
        Args:
            name: 计算器名称
        """
        super().__init__(name)
        self.calculators: List[FactorCalculator] = []
        
    def add_calculator(self, calculator: FactorCalculator):
        """
        添加子因子计算器
        
        Args:
            calculator: 因子计算器实例
        """
        self.calculators.append(calculator)
        print(f"添加因子计算器: {calculator.get_name()}")
    
    def remove_calculator(self, calculator_name: str):
        """
        移除子因子计算器
        
        Args:
            calculator_name: 计算器名称
        """
        self.calculators = [calc for calc in self.calculators if calc.get_name() != calculator_name]
        print(f"移除因子计算器: {calculator_name}")
    
    def get_calculator_names(self) -> List[str]:
        """获取所有子计算器名称"""
        return [calc.get_name() for calc in self.calculators]
    
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的所有因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含所有因子数据的DataFrame
        """
        if not self.calculators:
            print("警告：没有添加任何子因子计算器")
            return pd.DataFrame()
        
        date_str = self._standardize_date(date)
        print(f"\n🔄 组合因子计算 - 日期: {date_str}")
        print(f"子计算器数量: {len(self.calculators)}")
        
        # 存储所有因子结果
        all_factors = []
        
        for i, calculator in enumerate(self.calculators):
            try:
                print(f"  [{i+1}/{len(self.calculators)}] 计算 {calculator.get_name()}")
                factors = calculator.calculate_factors_for_date(date, **kwargs)
                
                if not factors.empty:
                    all_factors.append(factors)
                    print(f"    ✅ 成功计算 {len(factors)} 条记录")
                else:
                    print(f"    ⚠️  无数据")
                    
            except Exception as e:
                self.logger.error(f"计算器 {calculator.get_name()} 执行失败: {e}")
                print(f"    ❌ 计算失败: {e}")
                continue
        
        if not all_factors:
            print("所有子计算器都没有返回数据")
            return pd.DataFrame()
        
        # 合并所有因子数据
        result = self._merge_factor_data(all_factors)
        print(f"✅ 组合因子计算完成，共 {len(result)} 条记录")
        
        return result
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的所有因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含所有因子数据的DataFrame
        """
        if not self.calculators:
            print("警告：没有添加任何子因子计算器")
            return pd.DataFrame()
        
        print(f"\n🔄 组合因子计算 - 时间段: {start_date} 到 {end_date}")
        print(f"子计算器数量: {len(self.calculators)}")
        
        # 存储所有因子结果
        all_factors = []
        
        for i, calculator in enumerate(self.calculators):
            try:
                print(f"  [{i+1}/{len(self.calculators)}] 计算 {calculator.get_name()}")
                factors = calculator.calculate_factors_for_period(start_date, end_date, **kwargs)
                
                if not factors.empty:
                    all_factors.append(factors)
                    print(f"    ✅ 成功计算 {len(factors)} 条记录")
                else:
                    print(f"    ⚠️  无数据")
                    
            except Exception as e:
                self.logger.error(f"计算器 {calculator.get_name()} 执行失败: {e}")
                print(f"    ❌ 计算失败: {e}")
                continue
        
        if not all_factors:
            print("所有子计算器都没有返回数据")
            return pd.DataFrame()
        
        # 合并所有因子数据
        result = self._merge_factor_data(all_factors)
        print(f"✅ 组合因子计算完成，共 {len(result)} 条记录")
        
        return result
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的所有因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数
            
        Returns:
            包含单个债券所有因子的DataFrame
        """
        if not self.calculators:
            print("警告：没有添加任何子因子计算器")
            return pd.DataFrame()
        
        date_str = self._standardize_date(date)
        print(f"\n🎯 单债券组合因子计算 - 日期: {date_str}, 债券: {bond_code}")
        print(f"子计算器数量: {len(self.calculators)}")
        
        # 存储所有因子结果
        all_factors = []
        
        for i, calculator in enumerate(self.calculators):
            try:
                print(f"  [{i+1}/{len(self.calculators)}] 计算 {calculator.get_name()}")
                factors = calculator.calculate_factors_for_single_bond(date, bond_code, **kwargs)
                
                if not factors.empty:
                    all_factors.append(factors)
                    print(f"    ✅ 成功计算")
                else:
                    print(f"    ⚠️  无数据")
                    
            except Exception as e:
                self.logger.error(f"计算器 {calculator.get_name()} 执行失败: {e}")
                print(f"    ❌ 计算失败: {e}")
                continue
        
        if not all_factors:
            print("所有子计算器都没有返回数据")
            return pd.DataFrame()
        
        # 合并所有因子数据
        result = self._merge_factor_data(all_factors)
        print(f"✅ 单债券组合因子计算完成")
        
        return result
    
    def _merge_factor_data(self, factor_list: List[pd.DataFrame]) -> pd.DataFrame:
        """
        合并多个因子数据

        Args:
            factor_list: 因子DataFrame列表

        Returns:
            合并后的DataFrame
        """
        if not factor_list:
            return pd.DataFrame()

        if len(factor_list) == 1:
            return factor_list[0]

        # 以第一个DataFrame为基础，逐个合并其他DataFrame
        result = factor_list[0].copy()

        # 定义基础数据列（不是因子列）
        base_data_columns = {
            'ts_code', 'trade_date', 'close', 'full_price', 'accrued_interest',
            'volume', 'amount', 'maturity_date', 'delist_date', 'coupon_rate',
            'pay_per_year', 'value_date', 'par', 'stk_code', 'bond_short_name',
            'list_date', 'remain_size', 'stk_ts_code', 'stock_close', 'org_close'
        }

        for i, df in enumerate(factor_list[1:], 1):
            try:
                # 基于ts_code和trade_date合并
                merge_keys = ['ts_code', 'trade_date']

                # 检查合并键是否存在
                if all(key in result.columns for key in merge_keys) and all(key in df.columns for key in merge_keys):
                    # 只保留因子列和合并键，避免重复的基础数据列
                    df_to_merge = df.copy()

                    # 检查并处理重复列名
                    unique_columns = []
                    seen_columns = set()
                    for col in df_to_merge.columns:
                        if col not in seen_columns:
                            unique_columns.append(col)
                            seen_columns.add(col)

                    df_to_merge = df_to_merge[unique_columns]

                    # 移除重复的基础数据列（除了合并键）
                    columns_to_keep = merge_keys.copy()
                    for col in df_to_merge.columns:
                        if col not in base_data_columns or col in merge_keys:
                            columns_to_keep.append(col)

                    # 去重列名
                    columns_to_keep = list(dict.fromkeys(columns_to_keep))
                    df_to_merge = df_to_merge[columns_to_keep]

                    # 检查是否有重复的因子列，如果有则先处理
                    overlapping_factor_columns = []
                    for col in df_to_merge.columns:
                        if col in result.columns and col not in merge_keys:
                            overlapping_factor_columns.append(col)

                    if overlapping_factor_columns:
                        print(f"    发现重复因子列: {overlapping_factor_columns}")
                        # 对于重复的因子列，优先使用后面计算器的结果（通常更准确）
                        # 先从result中删除这些列
                        result = result.drop(columns=overlapping_factor_columns)

                    # 合并数据
                    result = pd.merge(result, df_to_merge, on=merge_keys, how='outer')
                else:
                    self.logger.warning(f"第{i+1}个DataFrame缺少合并键，跳过合并")

            except Exception as e:
                self.logger.error(f"合并第{i+1}个DataFrame时出错: {e}")
                continue

        return result

    def calculate_incremental_factors(self, start_date: str, end_date: str, 
                                    new_factors_only: bool = True, **kwargs) -> pd.DataFrame:
        """
        增量计算因子（只计算新因子，不影响已有因子）
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            new_factors_only: 是否只计算新因子列（True）还是更新所有因子（False）
            **kwargs: 其他参数
            
        Returns:
            增量计算的因子DataFrame
        """
        if not self.calculators:
            print("警告：没有添加任何子因子计算器")
            return pd.DataFrame()
        
        print(f"\n🔄 增量因子计算 - 时间段: {start_date} 到 {end_date}")
        print(f"模式: {'只计算新因子' if new_factors_only else '更新所有因子'}")
        
        # 获取现有因子列
        existing_columns = self._get_existing_factor_columns()
        print(f"现有因子列数: {len(existing_columns)}")
        
        # 存储所有因子结果
        all_factors = []
        
        for i, calculator in enumerate(self.calculators):
            try:
                print(f"  [{i+1}/{len(self.calculators)}] 计算 {calculator.get_name()}")
                
                # 检查这个计算器是否会产生新因子
                if new_factors_only:
                    calculator_columns = self._get_calculator_columns(calculator)
                    new_columns = [col for col in calculator_columns if col not in existing_columns]
                    
                    if not new_columns:
                        print(f"    ⏭️  跳过 - 没有新因子")
                        continue
                    else:
                        print(f"    🆕 发现新因子: {new_columns}")
                
                factors = calculator.calculate_factors_for_period(start_date, end_date, **kwargs)
                
                if not factors.empty:
                    # 如果只计算新因子，过滤出新列
                    if new_factors_only:
                        base_columns = ['ts_code', 'trade_date']
                        keep_columns = base_columns + [col for col in factors.columns 
                                                     if col not in base_columns and col not in existing_columns]
                        if len(keep_columns) > 2:  # 有新因子
                            factors = factors[keep_columns]
                            all_factors.append(factors)
                            print(f"    ✅ 成功计算 {len(factors)} 条记录，{len(keep_columns)-2} 个新因子")
                        else:
                            print(f"    ⏭️  过滤后无新因子")
                    else:
                        all_factors.append(factors)
                        print(f"    ✅ 成功计算 {len(factors)} 条记录")
                else:
                    print(f"    ⚠️  无数据")
                    
            except Exception as e:
                self.logger.error(f"计算器 {calculator.get_name()} 执行失败: {e}")
                print(f"    ❌ 计算失败: {e}")
                continue
        
        if not all_factors:
            print("没有新因子需要计算")
            return pd.DataFrame()
        
        # 合并所有因子数据
        result = self._merge_factor_data(all_factors)
        print(f"✅ 增量因子计算完成，共 {len(result)} 条记录")
        
        return result
    
    def _get_existing_factor_columns(self) -> List[str]:
        """
        获取数据库中已存在的因子列
        
        Returns:
            已存在的因子列列表
        """
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取cb_factor表的所有列
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(cb_factor)")
            columns_info = cursor.fetchall()
            
            # 提取列名，排除基础列
            base_columns = {'ts_code', 'trade_date', 'id'}
            existing_columns = [col[1] for col in columns_info if col[1] not in base_columns]
            
            return existing_columns
            
        except Exception as e:
            self.logger.error(f"获取现有因子列失败: {e}")
            return []
        finally:
            conn.close()
    
    def _get_calculator_columns(self, calculator: FactorCalculator) -> List[str]:
        """
        获取特定计算器会产生的因子列（通过分析计算器类型推断）
        
        Args:
            calculator: 因子计算器
            
        Returns:
            该计算器会产生的因子列列表
        """
        calculator_name = calculator.get_name()
        
        # 根据计算器名称或类型推断因子列
        if 'ZL' in calculator_name or 'zl' in calculator_name.lower():
            return ['zl_price', 'arbitrage_space', 'model_delta', 'corr_coef', 'reg_slope',
                   'bond_floor', 'option_value', 'cb_return', 'stock_return', 'predicted_return',
                   'expected_return']
        elif 'momentum' in calculator_name.lower() or '动量' in calculator_name:
            return ['momentum_5d', 'momentum_10d', 'momentum_20d']
        elif 'double_low' in calculator_name.lower() or '双低' in calculator_name:
            return ['price_factor', 'premium_factor', 'double_low_score',
                   'double_low_rank', 'double_low_percentile']
        elif 'technical' in calculator_name.lower() or '技术' in calculator_name:
            return ['bb_position', 'bb_width', 'bb_deviation', 'volume_percentile', 'amount_percentile',
                   'volume_change_5d', 'volume_change_10d', 'volume_change_20d', 'volume_trend',
                   'volatility_5d', 'volatility_10d', 'volatility_20d', 'atr_14d', 'relative_volatility',
                   'price_percentile', 'price_position', 'drawdown_from_high', 'gain_from_low',
                   'avg_turnover_20d', 'turnover_cv', 'amihud_illiquidity',
                   'trend_slope_5d', 'trend_slope_10d', 'trend_slope_20d', 'trend_consistency',
                   'rsi_14d', 'williams_r', 'obv_trend', 'macd']
        elif 'basic_price' in calculator_name.lower() or '基础价格' in calculator_name:
            return ['stock_close', 'conv_value', 'premium_ratio', 'combined_factor']
        elif 'composite' in calculator_name.lower() or '组合' in calculator_name:
            return ['combined_factor']
        else:
            # 未知计算器，返回空列表
            return []
