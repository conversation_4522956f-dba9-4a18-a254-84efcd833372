#!/usr/bin/env python3
"""
预测器工厂类
统一管理AutoGluon、线性回归和FT-Transformer三种预测器
"""

from typing import Optional, Dict, List, Union, Any
from pathlib import Path
import warnings

# 导入三种预测器
try:
    from .autogluon_predictor import AutoGluonFactorPredictor, train_autogluon_predictor, load_and_predict_autogluon
    AUTOGLUON_AVAILABLE = True
except ImportError:
    print("AutoGluon预测器不可用")
    AUTOGLUON_AVAILABLE = False

try:
    from .linear_regression_optimizer import FactorLinearRegression, train_factor_regression, load_and_predict
    LINEAR_AVAILABLE = True
except ImportError:
    print("线性回归预测器不可用")
    LINEAR_AVAILABLE = False

try:
    from .ft_transformer_predictor import FTTransformerPredictor, train_ft_transformer_predictor, load_and_predict_ft_transformer
    FT_TRANSFORMER_AVAILABLE = True
except ImportError:
    print("FT-Transformer预测器不可用")
    FT_TRANSFORMER_AVAILABLE = False

from .autogluon_predictor import get_factor_config_by_name


class PredictorFactory:
    """
    预测器工厂类
    提供统一的接口来创建、训练和使用不同类型的预测器
    """
    
    SUPPORTED_PREDICTORS = {
        'autogluon': 'AutoGluon TabularPredictor',
        'linear': '线性回归预测器',
        'ft_transformer': 'FT-Transformer预测器'
    }
    
    def __init__(self):
        """初始化预测器工厂"""
        self.available_predictors = {}
        
        if AUTOGLUON_AVAILABLE:
            self.available_predictors['autogluon'] = True
        if LINEAR_AVAILABLE:
            self.available_predictors['linear'] = True
        if FT_TRANSFORMER_AVAILABLE:
            self.available_predictors['ft_transformer'] = True
        
        print(f"预测器工厂初始化完成")
        print(f"可用预测器: {list(self.available_predictors.keys())}")
    
    def list_available_predictors(self):
        """列出所有可用的预测器"""
        print("\n=== 可用预测器列表 ===")
        for predictor_type, description in self.SUPPORTED_PREDICTORS.items():
            status = "✅ 可用" if predictor_type in self.available_predictors else "❌ 不可用"
            print(f"{predictor_type:<15} | {description:<25} | {status}")
    
    def create_predictor(self, predictor_type: str, model_name: str = 'default', 
                        target_days: int = 1, **kwargs):
        """
        创建预测器实例
        
        Args:
            predictor_type: 预测器类型 ('autogluon', 'linear', 'ft_transformer')
            model_name: 模型名称
            target_days: 预测天数
            **kwargs: 其他参数
            
        Returns:
            预测器实例
        """
        if predictor_type not in self.available_predictors:
            raise ValueError(f"预测器类型 '{predictor_type}' 不可用。可用类型: {list(self.available_predictors.keys())}")
        
        print(f"创建 {predictor_type} 预测器: {model_name}")
        
        if predictor_type == 'autogluon':
            return AutoGluonFactorPredictor(
                model_name=model_name,
                target_days=target_days,
                **kwargs
            )
        elif predictor_type == 'linear':
            return FactorLinearRegression(
                model_name=model_name,
                return_days=target_days,
                **kwargs
            )
        elif predictor_type == 'ft_transformer':
            return FTTransformerPredictor(
                model_name=model_name,
                target_days=target_days,
                **kwargs
            )
        else:
            raise ValueError(f"未知的预测器类型: {predictor_type}")
    
    def train_predictor(self, predictor_type: str, train_start: str, train_end: str,
                       model_name: str = 'default', target_days: int = 1,
                       custom_features: Optional[List[str]] = None,
                       factor_config: Optional[str] = None,
                       preprocessing_config: str = 'conservative',
                       **kwargs):
        """
        训练预测器
        
        Args:
            predictor_type: 预测器类型
            train_start: 训练开始日期
            train_end: 训练结束日期
            model_name: 模型名称
            target_days: 预测天数
            custom_features: 自定义特征列表
            factor_config: 因子配置名称 ('best_ten', 'best_five', etc.)
            preprocessing_config: 预处理配置
            **kwargs: 其他参数
            
        Returns:
            训练好的预测器
        """
        if predictor_type not in self.available_predictors:
            raise ValueError(f"预测器类型 '{predictor_type}' 不可用")
        
        # 处理因子配置
        if factor_config and not custom_features:
            custom_features = get_factor_config_by_name(factor_config)
            if custom_features:
                print(f"使用因子配置 '{factor_config}': {len(custom_features)} 个因子")
        
        print(f"\n=== 训练 {predictor_type} 预测器 ===")
        print(f"训练期间: {train_start} - {train_end}")
        print(f"模型名称: {model_name}")
        print(f"预测天数: {target_days}")
        print(f"预处理配置: {preprocessing_config}")
        if custom_features:
            print(f"使用特征: {len(custom_features)} 个")
        
        if predictor_type == 'autogluon':
            return train_autogluon_predictor(
                train_start=train_start,
                train_end=train_end,
                model_name=model_name,
                target_days=target_days,
                custom_features=custom_features,
                preprocessing_config=preprocessing_config,
                **kwargs
            )
        elif predictor_type == 'linear':
            return train_factor_regression(
                train_start=train_start,
                train_end=train_end,
                model_name=model_name,
                custom_factors=custom_features,
                target_factor=f'return_{target_days}d',
                return_days=target_days,
                factor_config_name=factor_config or 'default',
                **kwargs
            )
        elif predictor_type == 'ft_transformer':
            return train_ft_transformer_predictor(
                train_start=train_start,
                train_end=train_end,
                model_name=model_name,
                target_days=target_days,
                custom_features=custom_features,
                preprocessing_config=preprocessing_config,
                **kwargs
            )
        else:
            raise ValueError(f"未知的预测器类型: {predictor_type}")
    
    def load_and_predict(self, predictor_type: str, model_name: str, factor_data,
                        factor_name: Optional[str] = None, **kwargs):
        """
        加载模型并进行预测
        
        Args:
            predictor_type: 预测器类型
            model_name: 模型名称
            factor_data: 因子数据DataFrame
            factor_name: 预测因子名称
            **kwargs: 其他参数
            
        Returns:
            包含预测结果的DataFrame
        """
        if predictor_type not in self.available_predictors:
            raise ValueError(f"预测器类型 '{predictor_type}' 不可用")
        
        # 设置默认因子名称
        if factor_name is None:
            factor_name = f'{predictor_type}_predicted_return'
        
        print(f"加载 {predictor_type} 模型: {model_name}")
        
        if predictor_type == 'autogluon':
            return load_and_predict_autogluon(model_name, factor_data, factor_name, **kwargs)
        elif predictor_type == 'linear':
            return load_and_predict(model_name, factor_data, factor_name, **kwargs)
        elif predictor_type == 'ft_transformer':
            return load_and_predict_ft_transformer(model_name, factor_data, factor_name, **kwargs)
        else:
            raise ValueError(f"未知的预测器类型: {predictor_type}")
    
    def compare_predictors(self, train_start: str, train_end: str, 
                          test_start: str, test_end: str, 
                          predictor_configs: List[Dict[str, Any]],
                          factor_config: str = 'best_ten',
                          preprocessing_config: str = 'conservative'):
        """
        比较不同预测器的性能
        
        Args:
            train_start: 训练开始日期
            train_end: 训练结束日期
            test_start: 测试开始日期
            test_end: 测试结束日期
            predictor_configs: 预测器配置列表
            factor_config: 因子配置
            preprocessing_config: 预处理配置
            
        Returns:
            比较结果字典
        """
        print(f"\n=== 预测器性能比较 ===")
        print(f"训练期间: {train_start} - {train_end}")
        print(f"测试期间: {test_start} - {test_end}")
        print(f"因子配置: {factor_config}")
        print(f"预处理配置: {preprocessing_config}")
        
        results = {}
        custom_features = get_factor_config_by_name(factor_config)
        
        for i, config in enumerate(predictor_configs):
            predictor_type = config['type']
            model_name = config.get('model_name', f'compare_{predictor_type}')
            
            print(f"\n{'-'*50}")
            print(f"训练预测器 {i+1}/{len(predictor_configs)}: {predictor_type}")
            print(f"{'-'*50}")
            
            try:
                # 训练模型
                predictor = self.train_predictor(
                    predictor_type=predictor_type,
                    train_start=train_start,
                    train_end=train_end,
                    model_name=model_name,
                    custom_features=custom_features,
                    factor_config=factor_config,
                    preprocessing_config=preprocessing_config,
                    **config.get('train_params', {})
                )
                
                if predictor is None:
                    print(f"❌ {predictor_type} 训练失败")
                    continue
                
                # 记录训练结果
                training_results = getattr(predictor, 'training_results', {})
                results[predictor_type] = {
                    'predictor': predictor,
                    'training_results': training_results,
                    'model_name': model_name,
                    'status': 'success'
                }
                
                print(f"✅ {predictor_type} 训练成功")
                if 'train_r2' in training_results:
                    print(f"   训练R²: {training_results['train_r2']:.4f}")
                if 'tuning_r2' in training_results:
                    print(f"   验证R²: {training_results['tuning_r2']:.4f}")
                
            except Exception as e:
                print(f"❌ {predictor_type} 训练失败: {e}")
                results[predictor_type] = {
                    'status': 'failed',
                    'error': str(e)
                }
                continue
        
        # 打印比较结果
        print(f"\n{'='*80}")
        print("预测器性能比较结果")
        print(f"{'='*80}")
        print(f"{'预测器类型':<15} {'状态':<10} {'训练R²':<12} {'验证R²':<12} {'训练样本':<12}")
        print(f"{'-'*80}")
        
        for predictor_type, result in results.items():
            if result['status'] == 'success':
                training_results = result['training_results']
                train_r2 = training_results.get('train_r2', 0)
                val_r2 = training_results.get('tuning_r2', training_results.get('test_r2', 0))
                train_samples = training_results.get('train_samples', training_results.get('n_samples', 0))
                
                print(f"{predictor_type:<15} {'成功':<10} {train_r2:<12.4f} {val_r2:<12.4f} {train_samples:<12}")
            else:
                print(f"{predictor_type:<15} {'失败':<10} {'N/A':<12} {'N/A':<12} {'N/A':<12}")
        
        return results
    
    def get_model_paths(self, predictor_type: str) -> Dict[str, List[Path]]:
        """
        获取指定预测器类型的所有模型路径
        
        Args:
            predictor_type: 预测器类型
            
        Returns:
            模型路径字典
        """
        if predictor_type not in self.available_predictors:
            raise ValueError(f"预测器类型 '{predictor_type}' 不可用")
        
        paths = {}
        
        if predictor_type == 'autogluon':
            models_dir = Path('models/autogluon')
            if models_dir.exists():
                paths['models'] = list(models_dir.glob("*_autogluon"))
                paths['metadata'] = list(models_dir.glob("*_metadata.json"))
                paths['preprocessors'] = list(models_dir.glob("*_preprocessor.pkl"))
        elif predictor_type == 'linear':
            models_dir = Path('models/linear_regression')
            if models_dir.exists():
                paths['models'] = list(models_dir.glob("*.pkl"))
                paths['configs'] = list(models_dir.glob("*.json"))
        elif predictor_type == 'ft_transformer':
            models_dir = Path('models/ft_transformer')
            if models_dir.exists():
                paths['models'] = list(models_dir.glob("*_ft_transformer.pth"))
                paths['metadata'] = list(models_dir.glob("*_metadata.json"))
                paths['preprocessors'] = list(models_dir.glob("*_preprocessor.pkl"))
        
        return paths


# 创建全局工厂实例
predictor_factory = PredictorFactory()


def create_predictor(predictor_type: str, **kwargs):
    """创建预测器的便捷函数"""
    return predictor_factory.create_predictor(predictor_type, **kwargs)


def train_predictor(predictor_type: str, **kwargs):
    """训练预测器的便捷函数"""
    return predictor_factory.train_predictor(predictor_type, **kwargs)


def predict_with_model(predictor_type: str, model_name: str, factor_data, **kwargs):
    """使用模型进行预测的便捷函数"""
    return predictor_factory.load_and_predict(predictor_type, model_name, factor_data, **kwargs)


def compare_all_predictors(train_start: str, train_end: str, test_start: str = None, test_end: str = None,
                          factor_config: str = 'best_ten', preprocessing_config: str = 'conservative',
                          time_limit: int = 300):
    """
    比较所有可用预测器的便捷函数
    
    Args:
        train_start: 训练开始日期
        train_end: 训练结束日期  
        test_start: 测试开始日期（可选）
        test_end: 测试结束日期（可选）
        factor_config: 因子配置
        preprocessing_config: 预处理配置
        time_limit: 训练时间限制
        
    Returns:
        比较结果
    """
    # 如果没有提供测试日期，使用训练日期
    if test_start is None:
        test_start = train_start
    if test_end is None:
        test_end = train_end
    
    # 定义预测器配置
    configs = []
    
    if 'autogluon' in predictor_factory.available_predictors:
        configs.append({
            'type': 'autogluon',
            'model_name': f'compare_autogluon_{factor_config}',
            'train_params': {
                'time_limit': time_limit,
                'presets': 'medium_quality',
                'tuning_ratio': 0.1
            }
        })
    
    if 'linear' in predictor_factory.available_predictors:
        configs.append({
            'type': 'linear',
            'model_name': f'compare_linear_{factor_config}',
            'train_params': {
                'regularization': 'ridge',
                'alpha': 1.0
            }
        })
    
    if 'ft_transformer' in predictor_factory.available_predictors:
        configs.append({
            'type': 'ft_transformer',
            'model_name': f'compare_ft_{factor_config}',
            'train_params': {
                'time_limit': time_limit,
                'tuning_ratio': 0.1
            }
        })
    
    return predictor_factory.compare_predictors(
        train_start=train_start,
        train_end=train_end,
        test_start=test_start,
        test_end=test_end,
        predictor_configs=configs,
        factor_config=factor_config,
        preprocessing_config=preprocessing_config
    )


if __name__ == "__main__":
    # 测试工厂功能
    factory = PredictorFactory()
    factory.list_available_predictors()
    
    # 比较所有预测器
    print("\n开始比较所有预测器...")
    results = compare_all_predictors(
        train_start='20240101',
        train_end='20240630',
        factor_config='best_ten',
        preprocessing_config='conservative',
        time_limit=120  # 2分钟测试
    )
    
    print(f"\n比较完成，结果: {list(results.keys())}")