"""
因子计算器工厂
用于创建和管理不同类型的因子计算器
"""

from typing import Dict, Any, Optional, List
import logging

from .base_factor_calculator import FactorCalculator, CompositeFactorCalculator
from .zl_factor_calculator import ZLFactorCalculator
from .momentum_factor_calculator import MomentumFactorCalculator
from .double_low_factor_calculator import DoubleLowFactorCalculator
from .advanced_technical_factor_calculator import AdvancedTechnicalFactorCalculator
from .basic_price_factor_calculator import BasicPriceFactorCalculator
from .enhanced_factor_calculator import EnhancedFactorCalculator
from .multithreaded_factor_calculator import MultithreadedFactorCalculator


class FactorCalculatorFactory:
    """
    因子计算器工厂类
    
    负责创建和管理不同类型的因子计算器实例
    """
    
    @staticmethod
    def create_calculator(calculator_type: str, **kwargs) -> FactorCalculator:
        """
        创建因子计算器实例
        
        Args:
            calculator_type: 计算器类型
                - 'zl': ZL模型因子计算器
                - 'momentum': 动量因子计算器
                - 'double_low': 双低因子计算器
                - 'advanced_technical': 高级技术因子计算器
                - 'enhanced': 增强因子计算器
                - 'composite': 组合因子计算器
                - 'optimized_*': 对应的优化版计算器
            **kwargs: 计算器特定参数
        
        Returns:
            因子计算器实例
        
        Raises:
            ValueError: 不支持的计算器类型
        """
        calculator_type = calculator_type.lower()
        
        calculator_map = {
            'zl': lambda: ZLFactorCalculator(
                precision_level=kwargs.get('precision_level', 'medium'),
                use_dynamic_params=kwargs.get('use_dynamic_params', True)
            ),
            'momentum': lambda: MomentumFactorCalculator(),
            'double_low': lambda: DoubleLowFactorCalculator(
                alpha=kwargs.get('alpha', 0.5)
            ),
            'advanced_technical': lambda: AdvancedTechnicalFactorCalculator(),
            'basic_price': lambda: BasicPriceFactorCalculator(),
            'enhanced': lambda: EnhancedFactorCalculator(),
            'composite': lambda: CompositeFactorCalculator(name=kwargs.get('name', '组合因子计算器')),
            'multithreaded': lambda: MultithreadedFactorCalculator(
                calculator_type=kwargs.get('base_calculator_type', 'enhanced'),
                max_workers=kwargs.get('max_workers', None),
                precision_level=kwargs.get('precision_level', 'medium'),
                use_dynamic_params=kwargs.get('use_dynamic_params', True),
                alpha=kwargs.get('alpha', 0.5)
            )
        }
        
        if calculator_type not in calculator_map:
            raise ValueError(f"不支持的因子计算器类型: {calculator_type}")
        
        return calculator_map[calculator_type]()
    
    @staticmethod
    def create_default_composite_calculator(precision_level: str = 'medium',
                                          use_dynamic_params: bool = True,
                                          alpha: float = 0.5) -> CompositeFactorCalculator:
        """
        创建默认的组合因子计算器

        包含基础价格因子计算器、ZL模型因子计算器和动量因子计算器

        Args:
            precision_level: ZL模型精度等级
            use_dynamic_params: 是否使用动态参数
            alpha: 双低因子的alpha参数

        Returns:
            配置好的组合因子计算器
        """
        composite = CompositeFactorCalculator("默认组合因子计算器")

        # 添加基础价格因子计算器（优先计算基础字段）
        basic_price_calculator = BasicPriceFactorCalculator()
        composite.add_calculator(basic_price_calculator)

        # 添加ZL模型因子计算器
        zl_calculator = ZLFactorCalculator(
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params
        )
        composite.add_calculator(zl_calculator)

        # 添加动量因子计算器
        momentum_calculator = MomentumFactorCalculator()
        composite.add_calculator(momentum_calculator)

        return composite
    
    @staticmethod
    def create_enhanced_composite_calculator(precision_level: str = 'medium',
                                           use_dynamic_params: bool = True,
                                           alpha: float = 0.5) -> CompositeFactorCalculator:
        """
        创建包含增强因子的完整组合因子计算器
        
        包含所有基础因子计算器 + 增强因子计算器，提供最全面的因子覆盖
        
        Args:
            precision_level: ZL模型精度等级
            use_dynamic_params: 是否使用动态参数
            alpha: 双低因子的alpha参数
            
        Returns:
            配置好的增强组合因子计算器
        """
        composite = CompositeFactorCalculator("增强组合因子计算器")
        
        # 添加基础价格因子计算器（优先计算基础字段）
        basic_price_calculator = BasicPriceFactorCalculator()
        composite.add_calculator(basic_price_calculator)
        
        # 添加ZL模型因子计算器
        zl_calculator = ZLFactorCalculator(
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params
        )
        composite.add_calculator(zl_calculator)
        
        # 添加动量因子计算器
        momentum_calculator = MomentumFactorCalculator()
        composite.add_calculator(momentum_calculator)
        
        # 添加双低因子计算器
        double_low_calculator = DoubleLowFactorCalculator(
            alpha=alpha
        )
        composite.add_calculator(double_low_calculator)
        
        # 添加高级技术因子计算器
        advanced_technical_calculator = AdvancedTechnicalFactorCalculator()
        composite.add_calculator(advanced_technical_calculator)
        
        # 添加增强因子计算器（新增）
        enhanced_calculator = EnhancedFactorCalculator()
        composite.add_calculator(enhanced_calculator)
        
        return composite
    
    @staticmethod
    def create_double_low_composite_calculator(precision_level: str = 'medium',
                                             use_dynamic_params: bool = True,
                                             alpha: float = 0.5) -> CompositeFactorCalculator:
        """
        创建包含双低因子的组合因子计算器

        包含基础价格因子计算器、ZL模型因子计算器、动量因子计算器和双低因子计算器

        Args:
            precision_level: ZL模型精度等级
            use_dynamic_params: 是否使用动态参数
            alpha: 双低因子的alpha参数

        Returns:
            配置好的组合因子计算器
        """
        composite = CompositeFactorCalculator("双低组合因子计算器")

        # 添加基础价格因子计算器（优先计算基础字段）
        basic_price_calculator = BasicPriceFactorCalculator()
        composite.add_calculator(basic_price_calculator)

        # 添加ZL模型因子计算器
        zl_calculator = ZLFactorCalculator(
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params
        )
        composite.add_calculator(zl_calculator)

        # 添加动量因子计算器
        momentum_calculator = MomentumFactorCalculator()
        composite.add_calculator(momentum_calculator)

        # 添加双低因子计算器
        double_low_calculator = DoubleLowFactorCalculator(
            alpha=alpha
        )
        composite.add_calculator(double_low_calculator)

        return composite
    
    @staticmethod
    def create_advanced_composite_calculator(precision_level: str = 'medium',
                                           use_dynamic_params: bool = True,
                                           alpha: float = 0.5) -> CompositeFactorCalculator:
        """
        创建包含高级技术因子的组合因子计算器

        包含ZL模型因子计算器、动量因子计算器、双低因子计算器、高级技术因子计算器和基础价格因子计算器

        Args:
            precision_level: ZL模型精度等级
            use_dynamic_params: 是否使用动态参数
            alpha: 双低因子的alpha参数

        Returns:
            配置好的组合因子计算器
        """
        composite = CompositeFactorCalculator("高级组合因子计算器")

        # 添加基础价格因子计算器（优先计算基础字段）
        basic_price_calculator = BasicPriceFactorCalculator()
        composite.add_calculator(basic_price_calculator)

        # 添加ZL模型因子计算器
        zl_calculator = ZLFactorCalculator(
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params
        )
        composite.add_calculator(zl_calculator)

        # 添加动量因子计算器
        momentum_calculator = MomentumFactorCalculator()
        composite.add_calculator(momentum_calculator)

        # 添加双低因子计算器
        double_low_calculator = DoubleLowFactorCalculator(
            alpha=alpha
        )
        composite.add_calculator(double_low_calculator)

        # 添加高级技术因子计算器
        technical_calculator = AdvancedTechnicalFactorCalculator()
        composite.add_calculator(technical_calculator)

        return composite
    
    @staticmethod
    def create_multithreaded_calculator(calculator_type: str = 'enhanced',
                                       max_workers: Optional[int] = None,
                                       precision_level: str = 'medium',
                                       use_dynamic_params: bool = True,
                                       alpha: float = 0.5) -> MultithreadedFactorCalculator:
        """
        创建多线程因子计算器
        
        Args:
            calculator_type: 基础计算器类型
            max_workers: 最大工作线程数
            precision_level: ZL模型精度等级
            use_dynamic_params: 是否使用动态参数
            alpha: 双低因子的alpha参数
            
        Returns:
            配置好的多线程因子计算器
        """
        return MultithreadedFactorCalculator(
            calculator_type=calculator_type,
            max_workers=max_workers,
            precision_level=precision_level,
            use_dynamic_params=use_dynamic_params,
            alpha=alpha
        )
    
    @staticmethod
    def get_available_calculators() -> List[str]:
        """
        获取所有可用的因子计算器类型
        
        Returns:
            可用计算器类型列表
        """
        return [
            'basic_price',
            'zl',
            'momentum',
            'double_low',
            'advanced_technical',
            'enhanced',
            'composite',
            'multithreaded'
        ]
    
    @staticmethod
    def get_calculator_description(calculator_type: str) -> str:
        """
        获取因子计算器描述
        
        Args:
            calculator_type: 计算器类型
        
        Returns:
            计算器描述
        """
        descriptions = {
            'basic_price': '基础价格因子计算器 - 计算股价、转股价值等基础价格相关因子（支持优化功能）',
            'zl': 'ZL模型因子计算器 - 基于ZL模型的可转债定价因子（支持优化功能）',
            'momentum': '动量因子计算器 - 计算价格动量、成交量动量等因子（支持优化功能）',
            'double_low': '双低因子计算器 - 计算双低策略相关因子（支持优化功能）',
            'advanced_technical': '高级技术因子计算器 - 计算技术分析相关的高级因子（支持优化功能）',
            'enhanced': '增强因子计算器 - 计算可转债特有的高级因子和市场结构因子',
            'composite': '组合因子计算器 - 组合多个子计算器的复合计算器（支持优化功能）',
            'multithreaded': '多线程因子计算器 - 通过多线程并行计算大幅提升因子计算效率'
        }
        
        return descriptions.get(calculator_type.lower(), '未知计算器类型')


def create_calculator_from_args(calculator_type: str, **kwargs) -> FactorCalculator:
    """
    便捷函数：从参数创建因子计算器
    
    Args:
        calculator_type: 计算器类型
        **kwargs: 计算器参数
    
    Returns:
        因子计算器实例
    """
    return FactorCalculatorFactory.create_calculator(calculator_type, **kwargs)


def create_default_calculator(precision_level: str = 'medium', 
                            use_dynamic_params: bool = True) -> CompositeFactorCalculator:
    """
    便捷函数：创建默认的组合因子计算器
    
    Args:
        precision_level: ZL模型精度等级
        use_dynamic_params: 是否使用动态参数
    
    Returns:
        配置好的组合因子计算器
    """
    return FactorCalculatorFactory.create_default_composite_calculator(
        precision_level=precision_level,
        use_dynamic_params=use_dynamic_params
    )


def create_double_low_calculator(precision_level: str = 'medium',
                               use_dynamic_params: bool = True,
                               alpha: float = 0.5) -> CompositeFactorCalculator:
    """
    便捷函数：创建包含双低因子的组合因子计算器
    
    Args:
        precision_level: ZL模型精度等级
        use_dynamic_params: 是否使用动态参数
        alpha: 双低因子的alpha参数
    
    Returns:
        配置好的组合因子计算器
    """
    return FactorCalculatorFactory.create_double_low_composite_calculator(
        precision_level=precision_level,
        use_dynamic_params=use_dynamic_params,
        alpha=alpha
    )


def create_advanced_calculator(precision_level: str = 'medium',
                             use_dynamic_params: bool = True,
                             alpha: float = 0.5) -> CompositeFactorCalculator:
    """
    便捷函数：创建包含高级技术因子的组合因子计算器
    
    Args:
        precision_level: ZL模型精度等级
        use_dynamic_params: 是否使用动态参数
        alpha: 双低因子的alpha参数
    
    Returns:
        配置好的组合因子计算器
    """
    return FactorCalculatorFactory.create_advanced_composite_calculator(
        precision_level=precision_level,
        use_dynamic_params=use_dynamic_params,
        alpha=alpha
    )


def create_enhanced_calculator(precision_level: str = 'medium',
                             use_dynamic_params: bool = True,
                             alpha: float = 0.5) -> CompositeFactorCalculator:
    """
    便捷函数：创建增强组合因子计算器（包含所有因子+新增增强因子）
    
    Args:
        precision_level: ZL模型精度等级
        use_dynamic_params: 是否使用动态参数
        alpha: 双低因子的alpha参数
    
    Returns:
        配置好的增强组合因子计算器
    """
    return FactorCalculatorFactory.create_enhanced_composite_calculator(
        precision_level=precision_level,
        use_dynamic_params=use_dynamic_params,
        alpha=alpha
    )



