"""
高级技术因子计算器
实现更多适合可转债投资的技术因子
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import warnings
import logging
from typing import Dict, Any
from scipy import stats
from sklearn.preprocessing import QuantileTransformer
warnings.filterwarnings('ignore')

from .base_factor_calculator import FactorCalculator


class AdvancedTechnicalFactorCalculator(FactorCalculator):
    """
    高级技术因子计算器
    
    实现适合可转债投资的高级技术因子，包括：
    - 布林带相关因子
    - 成交量因子
    - 波动率因子  
    - 相对价格因子
    - 流动性因子
    - 趋势因子
    """
    
    def __init__(self):
        """初始化高级技术因子计算器"""
        super().__init__("高级技术因子计算器")
        print("高级技术因子计算器初始化完成")
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的高级技术因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含高级技术因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"🔄 计算高级技术因子 - 日期: {date_str}")
        
        # 获取可转债数据（需要历史数据用于计算技术指标）
        cb_data = self._get_historical_data_for_date(date_str)
        
        if cb_data.empty:
            print(f"没有找到日期 {date_str} 的数据")
            return pd.DataFrame()
        
        print(f"找到 {len(cb_data['ts_code'].unique())} 只可转债")
        
        # 计算各类技术因子
        result_list = []
        
        for ts_code in cb_data['ts_code'].unique():
            bond_data = cb_data[cb_data['ts_code'] == ts_code].sort_values('trade_date')

            # 降低历史数据要求，至少需要5天数据
            if len(bond_data) < 5:
                continue

            # 获取当日数据
            current_data = bond_data[bond_data['trade_date'] == date_str]
            if current_data.empty:
                continue

            factors = self._calculate_bond_technical_factors(bond_data, date_str)
            if factors is not None:
                result_list.append(pd.DataFrame([factors]))
        
        if result_list:
            result_df = pd.concat(result_list, ignore_index=True)
            
            # 注意：因子计算器只负责计算原始因子数据，不进行预处理
            # 预处理在ML模型训练/预测时进行
            
            print(f"✅ 高级技术因子计算完成，计算了 {len(result_df)} 只债券")
            return result_df
        else:
            print("❌ 没有计算出任何因子")
            return pd.DataFrame()
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的高级技术因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含高级技术因子的DataFrame
        """
        start_date_str = self._standardize_date(start_date)
        end_date_str = self._standardize_date(end_date)
        
        print(f"🔄 计算高级技术因子 - 时间段: {start_date_str} 到 {end_date_str}")
        
        # 获取交易日列表
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)
        
        if not trading_dates:
            print("没有找到交易日")
            return pd.DataFrame()
        
        print(f"需要处理 {len(trading_dates)} 个交易日")
        
        all_factors = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")
            
            daily_factors = self.calculate_factors_for_date(trade_date)
            if not daily_factors.empty:
                all_factors.append(daily_factors)
        
        if all_factors:
            result_df = pd.concat(all_factors, ignore_index=True)
            
            # 注意：因子计算器只负责计算原始因子数据，不进行预处理
            # 预处理在ML模型训练/预测时进行
            
            print(f"✅ 时间段高级技术因子计算完成，共 {len(result_df)} 条记录")
            return result_df
        else:
            print("❌ 没有计算出任何因子")
            return pd.DataFrame()
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的高级技术因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码
            **kwargs: 其他参数
            
        Returns:
            包含单个债券高级技术因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"🎯 计算单债券高级技术因子 - 日期: {date_str}, 债券: {bond_code}")
        
        # 获取单个债券的历史数据
        bond_data = self._get_single_bond_historical_data(date_str, bond_code)
        
        if bond_data.empty or len(bond_data) < 20:
            print(f"债券 {bond_code} 历史数据不足")
            return pd.DataFrame()
        
        # 计算技术因子
        factors = self._calculate_bond_technical_factors(bond_data, date_str)
        
        if factors is not None:
            result_df = pd.DataFrame([factors])
            
            # 注意：因子计算器只负责计算原始因子数据，不进行预处理
            # 预处理在ML模型训练/预测时进行
            
            print(f"✅ 单债券高级技术因子计算完成")
            return result_df
        else:
            print("❌ 计算失败")
            return pd.DataFrame()
    
    def _get_historical_data_for_date(self, date_str: str, lookback_days: int = 60) -> pd.DataFrame:
        """
        获取指定日期的历史数据

        Args:
            date_str: 目标日期
            lookback_days: 回看天数（如果数据不足，会获取所有可用数据）

        Returns:
            历史数据DataFrame
        """
        conn = sqlite3.connect(self.db_path)

        try:
            # 先尝试获取指定回看天数的数据
            target_date = datetime.strptime(date_str, '%Y%m%d')
            start_date = target_date - timedelta(days=lookback_days)
            start_date_str = start_date.strftime('%Y%m%d')

            query = """
            SELECT cd.ts_code, cd.trade_date, cd.close, cd.high, cd.low, cd.open,
                   cd.vol, cd.amount, cd.full_price,
                   cb.conv_price, cb.stk_code, cb.bond_short_name
            FROM cb_daily cd
            JOIN cb_basic cb ON cd.ts_code = cb.ts_code
            WHERE cd.trade_date >= ? AND cd.trade_date <= ?
            AND cd.close > 0
            AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > ?)
            ORDER BY cd.ts_code, cd.trade_date
            """

            df = pd.read_sql(query, conn, params=[start_date_str, date_str, date_str])
            
            # 处理全价数据
            if not df.empty:
                df['full_price'] = df['full_price'].fillna(method='ffill')

            return df
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def _get_single_bond_historical_data(self, date_str: str, bond_code: str, lookback_days: int = 60) -> pd.DataFrame:
        """
        获取单个债券的历史数据
        
        Args:
            date_str: 目标日期
            bond_code: 债券代码
            lookback_days: 回看天数
            
        Returns:
            单个债券的历史数据DataFrame
        """
        target_date = datetime.strptime(date_str, '%Y%m%d')
        start_date = target_date - timedelta(days=lookback_days)
        start_date_str = start_date.strftime('%Y%m%d')
        
        conn = sqlite3.connect(self.db_path)
        
        try:
            query = """
            SELECT cd.ts_code, cd.trade_date, cd.close, cd.high, cd.low, cd.open,
                   cd.vol, cd.amount, cd.full_price,
                   cb.conv_price, cb.stk_code, cb.bond_short_name
            FROM cb_daily cd
            JOIN cb_basic cb ON cd.ts_code = cb.ts_code
            WHERE cd.trade_date >= ? AND cd.trade_date <= ?
            AND cd.ts_code = ?
            AND cd.close > 0
            ORDER BY cd.trade_date
            """
            
            df = pd.read_sql(query, conn, params=[start_date_str, date_str, bond_code])
            
            # 处理全价数据
            if not df.empty:
                df['full_price'] = df['full_price'].fillna(method='ffill')
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取单债券历史数据失败: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def _calculate_bond_technical_factors(self, bond_data: pd.DataFrame, target_date: str) -> dict:
        """
        计算单个债券的技术因子
        
        Args:
            bond_data: 债券历史数据
            target_date: 目标日期
            
        Returns:
            技术因子字典
        """
        try:
            # 确保数据按日期排序
            bond_data = bond_data.sort_values('trade_date')
            
            # 获取当日数据
            current_data = bond_data[bond_data['trade_date'] == target_date]
            if current_data.empty:
                return None
            
            current_row = current_data.iloc[0]
            ts_code = current_row['ts_code']
            
            # 基础价格和成交量数据
            prices = bond_data['full_price'].values
            volumes = bond_data['vol'].values  # 使用正确的字段名
            amounts = bond_data['amount'].values
            highs = bond_data['high'].values
            lows = bond_data['low'].values

            # 当前价格
            current_price = current_row['full_price']
            current_volume = current_row['vol']  # 使用正确的字段名
            current_amount = current_row['amount']
            
            factors = {
                'ts_code': ts_code,
                'trade_date': target_date,
            }
            
            # 1. 布林带因子
            factors.update(self._calculate_bollinger_factors(prices, current_price))
            
            # 2. 成交量因子
            factors.update(self._calculate_volume_factors(volumes, amounts, current_volume, current_amount))
            
            # 3. 波动率因子
            factors.update(self._calculate_volatility_factors(prices, highs, lows))
            
            # 4. 相对价格因子
            factors.update(self._calculate_relative_price_factors(prices, current_price))
            
            # 5. 流动性因子
            factors.update(self._calculate_liquidity_factors(volumes, amounts, prices))
            
            # 6. 趋势因子
            factors.update(self._calculate_trend_factors(prices))
            
            # 7. 强度因子
            factors.update(self._calculate_strength_factors(prices, volumes, highs, lows))

            # 填充缺失的因子字段为默认值
            factors = self._fill_missing_factors(factors)

            return factors
            
        except Exception as e:
            self.logger.error(f"计算技术因子失败: {e}")
            return None
    
    
    def _apply_single_bond_optimization(self, raw_factors: pd.DataFrame, 
                                      date: str, bond_code: str) -> pd.DataFrame:
        """应用单债券优化策略"""
        if raw_factors.empty:
            return raw_factors
        
        optimized_factors = raw_factors.copy()
        
        try:
            # 对于单债券，主要进行数据质量检查
            optimized_factors = self._fix_single_bond_data_quality(optimized_factors, bond_code)
            optimized_factors = self._check_single_bond_outliers(optimized_factors, bond_code)
        except Exception as e:
            self.logger.error(f"单债券优化过程中出错: {e}")
            optimized_factors = raw_factors
        
        return optimized_factors
    
    
    
    
    
    def _fix_single_bond_data_quality(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """修复单债券数据质量问题"""
        df_fixed = df.copy()
        
        # 对于单债券，主要检查数据完整性
        for col in df_fixed.select_dtypes(include=[np.number]).columns:
            if df_fixed[col].isnull().any():
                # 用前值填充
                df_fixed[col] = df_fixed[col].fillna(method='ffill')
                # 如果还有缺失值，用后值填充
                df_fixed[col] = df_fixed[col].fillna(method='bfill')
                # 如果还有缺失值，用0填充
                df_fixed[col] = df_fixed[col].fillna(0)
        
        return df_fixed
    
    def _check_single_bond_outliers(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """检查单债券异常值"""
        df_checked = df.copy()
        
        # 对于单债券，进行基本的异常值检查
        for col in df_checked.select_dtypes(include=[np.number]).columns:
            # 检查极端值（例如负的价格）
            if 'price' in col.lower() or 'value' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=0)
            
            # 检查百分比字段
            if 'ratio' in col.lower() or 'rate' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=-1, upper=10)  # 限制在合理范围内
        
        return df_checked
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        return self.optimization_stats.copy()
    
    def reset_optimization_stats(self):
        """重置优化统计信息"""
        self.optimization_stats = {}
    
    def update_optimization_config(self, config_updates: Dict[str, Any]):
        """更新优化配置"""
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.optimization_config, config_updates)
        self.logger.info(f"优化配置已更新: {config_updates}")
    
    def set_optimization_enabled(self, enabled: bool):
        """设置优化开关 - 注意：因子计算器不再支持优化功能"""
        pass
    
    def is_optimization_enabled(self) -> bool:
        """检查优化是否启用 - 注意：因子计算器不再支持优化功能"""
        return False
    
    def _calculate_bollinger_factors(self, prices: np.ndarray, current_price: float) -> dict:
        """计算布林带相关因子"""
        factors = {}

        # 根据数据量选择合适的周期
        min_period = min(20, len(prices))
        if min_period >= 5:  # 至少需要5天数据
            ma = np.mean(prices[-min_period:])
            std = np.std(prices[-min_period:])

            upper_band = ma + 2 * std
            lower_band = ma - 2 * std

            # 布林带位置 (-1到1之间，-1为下轨，0为中轨，1为上轨)
            if upper_band != lower_band:
                factors['bb_position'] = (current_price - lower_band) / (upper_band - lower_band) * 2 - 1
            else:
                factors['bb_position'] = 0

            # 布林带宽度（标准化）
            factors['bb_width'] = (upper_band - lower_band) / ma if ma > 0 else 0

            # 价格相对于中轨的偏离度
            factors['bb_deviation'] = (current_price - ma) / std if std > 0 else 0

        return factors
    
    def _calculate_volume_factors(self, volumes: np.ndarray, amounts: np.ndarray,
                                current_volume: float, current_amount: float) -> dict:
        """计算成交量相关因子"""
        factors = {}

        if len(volumes) >= 5:  # 降低数据要求
            # 成交量相对位置 (当前成交量相对于历史分位数)
            factors['volume_percentile'] = self._calculate_percentile(volumes, current_volume)
            factors['amount_percentile'] = self._calculate_percentile(amounts, current_amount)

            # 成交量变化率 (根据数据量动态调整周期)
            for period in [5, 10, 20]:
                if len(volumes) >= period:
                    avg_volume = np.mean(volumes[-period:])
                    factors[f'volume_change_{period}d'] = (current_volume / avg_volume - 1) if avg_volume > 0 else 0
                else:
                    factors[f'volume_change_{period}d'] = 0.0  # 数据不足时设为默认值

            # 成交量趋势 (线性回归斜率)
            trend_period = min(20, len(volumes))
            factors['volume_trend'] = self._calculate_trend_slope(volumes[-trend_period:])

        return factors
    
    def _calculate_volatility_factors(self, prices: np.ndarray, highs: np.ndarray, lows: np.ndarray) -> dict:
        """计算波动率相关因子"""
        factors = {}

        if len(prices) >= 5:  # 降低数据要求
            # 价格波动率 (不同周期，根据数据量动态调整)
            for period in [5, 10, 20]:
                if len(prices) >= period + 1:  # 需要period+1个数据点来计算period天的收益率
                    returns = np.diff(prices[-period-1:]) / prices[-period-1:-1]
                    factors[f'volatility_{period}d'] = np.std(returns) * np.sqrt(252)  # 年化波动率
                else:
                    factors[f'volatility_{period}d'] = 0.3  # 数据不足时使用默认值

            # ATR (真实波动幅度)
            atr_period = min(14, len(prices))
            if atr_period >= 5 and len(highs) >= atr_period and len(lows) >= atr_period:
                factors['atr_14d'] = self._calculate_atr(highs[-atr_period:], lows[-atr_period:], prices[-atr_period:])
            else:
                factors['atr_14d'] = 0.0

            # 相对波动率 (当前波动率相对于历史均值)
            if len(prices) >= 30:  # 降低要求从60天到30天
                recent_period = min(20, len(prices) // 2)
                historical_period = len(prices)

                if recent_period >= 5 and historical_period >= 10:
                    recent_vol = np.std(np.diff(prices[-recent_period-1:]) / prices[-recent_period-1:-1]) * np.sqrt(252)
                    historical_vol = np.std(np.diff(prices[-historical_period:]) / prices[-historical_period:-1]) * np.sqrt(252)
                    factors['relative_volatility'] = recent_vol / historical_vol if historical_vol > 0 else 1
                else:
                    factors['relative_volatility'] = 1.0
            else:
                factors['relative_volatility'] = 1.0

        return factors
    
    def _calculate_relative_price_factors(self, prices: np.ndarray, current_price: float) -> dict:
        """计算相对价格因子"""
        factors = {}

        if len(prices) >= 5:  # 降低数据要求
            # 价格相对位置 (当前价格在历史价格中的分位数)
            factors['price_percentile'] = self._calculate_percentile(prices, current_price)

            # 距离历史高点和低点的位置
            max_price = np.max(prices)
            min_price = np.min(prices)

            if max_price != min_price:
                factors['price_position'] = (current_price - min_price) / (max_price - min_price)
            else:
                factors['price_position'] = 0.5

            # 回撤程度 (从最高点的回撤)
            factors['drawdown_from_high'] = (current_price - max_price) / max_price if max_price > 0 else 0

            # 从最低点的涨幅
            factors['gain_from_low'] = (current_price - min_price) / min_price if min_price > 0 else 0

        return factors
    
    def _calculate_liquidity_factors(self, volumes: np.ndarray, amounts: np.ndarray, prices: np.ndarray) -> dict:
        """计算流动性相关因子"""
        factors = {}

        if len(volumes) >= 5 and len(amounts) >= 5:  # 降低数据要求
            # 平均日成交额
            period = min(20, len(amounts))
            avg_amount = np.mean(amounts[-period:])
            factors['avg_turnover_20d'] = avg_amount

            # 成交额变异系数 (成交额稳定性)
            factors['turnover_cv'] = np.std(amounts[-period:]) / avg_amount if avg_amount > 0 else 0

            # Amihud非流动性指标 (价格影响成本)
            if len(prices) >= period + 1 and len(amounts) >= period:
                returns = np.abs(np.diff(prices[-period-1:]) / prices[-period-1:-1])
                amounts_slice = amounts[-period:]
                if len(returns) == len(amounts_slice):
                    amihud_values = returns / amounts_slice
                    valid_values = amihud_values[np.isfinite(amihud_values) & (amihud_values > 0)]
                    factors['amihud_illiquidity'] = np.mean(valid_values) if len(valid_values) > 0 else 0
                else:
                    factors['amihud_illiquidity'] = 0
            else:
                factors['amihud_illiquidity'] = 0

        return factors
    
    def _calculate_trend_factors(self, prices: np.ndarray) -> dict:
        """计算趋势相关因子"""
        factors = {}

        if len(prices) >= 5:  # 降低数据要求
            # 不同周期的趋势强度 (线性回归斜率)
            for period in [5, 10, 20]:
                if len(prices) >= period:
                    factors[f'trend_slope_{period}d'] = self._calculate_trend_slope(prices[-period:])
                else:
                    factors[f'trend_slope_{period}d'] = 0.0  # 数据不足时设为默认值

            # 趋势一致性 (不同周期趋势方向是否一致)
            slopes = []
            for period in [5, 10, 20]:
                if len(prices) >= period:
                    slopes.append(self._calculate_trend_slope(prices[-period:]))

            if len(slopes) >= 2:  # 至少需要两个周期的数据
                # 计算趋势一致性得分 (-1到1之间)
                signs = [np.sign(slope) for slope in slopes]
                factors['trend_consistency'] = np.mean(signs)
            else:
                factors['trend_consistency'] = 0.0

        return factors
    
    def _calculate_strength_factors(self, prices: np.ndarray, volumes: np.ndarray,
                                  highs: np.ndarray, lows: np.ndarray) -> dict:
        """计算强度相关因子"""
        factors = {}

        # RSI 指标（根据数据量动态调整周期）
        rsi_period = min(14, len(prices))
        if rsi_period >= 5:
            factors['rsi_14d'] = self._calculate_rsi(prices[-rsi_period:])
        else:
            factors['rsi_14d'] = 50.0  # 默认中性值

        # 威廉指标
        williams_period = min(14, len(prices))
        if williams_period >= 5 and len(highs) >= williams_period and len(lows) >= williams_period:
            factors['williams_r'] = self._calculate_williams_r(highs[-williams_period:], lows[-williams_period:], prices[-williams_period:])
        else:
            factors['williams_r'] = -50.0  # 默认中性值

        # MACD指标（降低数据要求）
        if len(prices) >= 12:  # 从26降低到12
            factors['macd'] = self._calculate_macd(prices)
        else:
            factors['macd'] = 0.0

        # 量价配合度 (OBV变化率)
        obv_period = min(20, len(prices))
        if len(volumes) >= obv_period and len(prices) >= obv_period and obv_period >= 5:
            factors['obv_trend'] = self._calculate_obv_trend(prices[-obv_period:], volumes[-obv_period:])
        else:
            factors['obv_trend'] = 0.0

        return factors
    
    def _calculate_percentile(self, data: np.ndarray, value: float) -> float:
        """计算值在数据中的分位数，移除随机噪声保持数据真实性"""
        if len(data) == 0:
            return 0.5
        
        # 使用scipy.stats的percentileofscore方法，提供更稳定的分位数计算
        from scipy import stats
        try:
            # kind='rank'使用排序方法，更适合技术分析
            percentile = stats.percentileofscore(data, value, kind='rank') / 100.0
            # 移除随机扰动，保持数据的真实性和可重复性
            return percentile
        except:
            # 降级为原有方法
            return np.sum(data <= value) / len(data)
    
    def _calculate_trend_slope(self, prices: np.ndarray) -> float:
        """计算价格趋势斜率"""
        if len(prices) < 2:
            return 0
        
        x = np.arange(len(prices))
        try:
            slope = np.polyfit(x, prices, 1)[0]
            # 标准化斜率 (相对于价格水平)
            return slope / np.mean(prices) if np.mean(prices) > 0 else 0
        except:
            return 0
    
    def _calculate_atr(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> float:
        """计算ATR (真实波动幅度)"""
        if len(highs) < 2 or len(lows) < 2 or len(closes) < 2:
            return 0

        # 确保所有数组长度一致
        min_len = min(len(highs), len(lows), len(closes))
        if min_len < 2:
            return 0

        highs = highs[:min_len]
        lows = lows[:min_len]
        closes = closes[:min_len]

        # 真实范围
        tr1 = highs[1:] - lows[1:]  # 当日高低点差
        tr2 = np.abs(highs[1:] - closes[:-1])  # 当日高点与前日收盘价差
        tr3 = np.abs(lows[1:] - closes[:-1])   # 当日低点与前日收盘价差

        tr = np.maximum(tr1, np.maximum(tr2, tr3))

        return np.mean(tr)
    
    def _calculate_rsi(self, prices: np.ndarray) -> float:
        """计算RSI指标"""
        if len(prices) < 2:
            return 50
        
        changes = np.diff(prices)
        gains = np.where(changes > 0, changes, 0)
        losses = np.where(changes < 0, -changes, 0)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_williams_r(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> float:
        """计算威廉指标"""
        if len(highs) == 0 or len(lows) == 0 or len(closes) == 0:
            return -50
        
        highest_high = np.max(highs)
        lowest_low = np.min(lows)
        current_close = closes[-1]
        
        if highest_high == lowest_low:
            return -50
        
        williams_r = ((highest_high - current_close) / (highest_high - lowest_low)) * (-100)
        
        return williams_r
    
    def _calculate_obv_trend(self, prices: np.ndarray, volumes: np.ndarray) -> float:
        """计算OBV趋势"""
        if len(prices) < 2 or len(volumes) < 2:
            return 0
        
        # 计算OBV
        obv = np.zeros(len(prices))
        for i in range(1, len(prices)):
            if prices[i] > prices[i-1]:
                obv[i] = obv[i-1] + volumes[i]
            elif prices[i] < prices[i-1]:
                obv[i] = obv[i-1] - volumes[i]
            else:
                obv[i] = obv[i-1]
        
        # 计算OBV趋势 (最近10期的斜率)
        if len(obv) >= 10:
            return self._calculate_trend_slope(obv[-10:])
        else:
            return self._calculate_trend_slope(obv)

    def _calculate_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> float:
        """计算MACD指标"""
        if len(prices) < slow:
            return 0

        # 计算EMA
        def ema(data, period):
            alpha = 2 / (period + 1)
            ema_values = np.zeros(len(data))
            ema_values[0] = data[0]
            for i in range(1, len(data)):
                ema_values[i] = alpha * data[i] + (1 - alpha) * ema_values[i-1]
            return ema_values

        # 计算快线和慢线EMA
        ema_fast = ema(prices, fast)
        ema_slow = ema(prices, slow)

        # MACD线 = 快线EMA - 慢线EMA
        macd_line = ema_fast - ema_slow

        # 信号线 = MACD线的EMA
        if len(macd_line) >= signal:
            signal_line = ema(macd_line, signal)
            # 返回MACD柱状图 = MACD线 - 信号线
            return macd_line[-1] - signal_line[-1]
        else:
            return macd_line[-1]



    def _get_default_technical_factors(self) -> dict:
        """
        获取技术因子的默认值（用于数据不足的情况）

        Returns:
            包含默认值的技术因子字典
        """
        return {
            # 布林带因子
            'bb_position': 0.5,
            'bb_width': 0.0,
            'bb_deviation': 0.0,

            # 成交量因子
            'volume_percentile': 0.5,
            'amount_percentile': 0.5,
            'volume_change_5d': 0.0,
            'volume_change_10d': 0.0,
            'volume_change_20d': 0.0,
            'volume_trend': 0.0,

            # 波动率因子
            'volatility_5d': 0.3,
            'volatility_10d': 0.3,
            'volatility_20d': 0.3,
            'atr_14d': 0.0,
            'relative_volatility': 1.0,

            # 相对价格因子
            'price_percentile': 0.5,
            'price_position': 0.5,
            'drawdown_from_high': 0.0,
            'gain_from_low': 0.0,

            # 流动性因子
            'avg_turnover_20d': 0.0,
            'turnover_cv': 0.0,
            'amihud_illiquidity': 0.0,

            # 趋势因子
            'trend_slope_5d': 0.0,
            'trend_slope_10d': 0.0,
            'trend_slope_20d': 0.0,
            'trend_consistency': 0.0,

            # 强度因子（已移除rsi_6d和rsi_12d）
            'rsi_14d': 50.0,
            'williams_r': -50.0,
            'obv_trend': 0.0,
            'macd': 0.0
        }

    def _fill_missing_factors(self, factors: dict) -> dict:
        """
        填充缺失的因子字段为默认值

        Args:
            factors: 已计算的因子字典

        Returns:
            填充后的因子字典
        """
        default_factors = self._get_default_technical_factors()

        # 填充缺失的字段
        for key, default_value in default_factors.items():
            if key not in factors:
                factors[key] = default_value

        return factors