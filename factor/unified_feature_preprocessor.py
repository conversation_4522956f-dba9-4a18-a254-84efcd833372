#!/usr/bin/env python3
"""
统一特征预处理器
整合OptimizationMixin和EnhancedFeaturePreprocessor的功能，提供统一的数据预处理接口

主要功能：
1. 数据质量检查和修复
2. 智能异常值检测和处理（支持多种方法）
3. 缺失值处理（支持多种策略）
4. 特征工程和变换
5. 多种特征标准化/归一化方法
6. 分布变换和稳定性增强
7. 时间序列特征的特殊处理
8. AutoGluon友好的预处理选项
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import (
    StandardScaler, MinMaxScaler, RobustScaler, 
    PowerTransformer, QuantileTransformer
)
from sklearn.impute import SimpleImputer, KNNImputer
from scipy import stats as scipy_stats
from typing import Dict, List, Tuple, Optional, Union, Any
import warnings
import logging
warnings.filterwarnings('ignore')


class UnifiedFeaturePreprocessor:
    """
    统一特征预处理器
    
    整合了OptimizationMixin和EnhancedFeaturePreprocessor的所有功能，
    提供灵活的配置系统，支持因子计算和机器学习训练的不同预处理模式。
    
    提供OptimizationMixin的兼容接口，以便无缝替换。
    """
    
    def __init__(self, 
                 # 异常值处理配置
                 outlier_method='iqr',
                 outlier_config=None,
                 
                 # 缺失值处理配置
                 missing_strategy='knn',
                 missing_config=None,
                 
                 # 特征缩放配置
                 scaling_method='robust',
                 scaling_config=None,
                 
                 # 数据质量配置
                 data_quality_config=None,
                 
                 # 分布变换配置
                 distribution_transform_config=None,
                 
                 # 稳定性增强配置
                 stability_config=None,
                 
                 # 特征工程配置
                 feature_engineering=True,
                 feature_engineering_config=None,
                 
                 # 时间序列配置
                 time_series_aware=True,
                 
                 # 模式配置
                 mode='ml_training',  # 'ml_training', 'factor_calculation', 'autogluon_friendly'
                 
                 # 优化级别
                 optimization_level='medium'  # 'light', 'medium', 'aggressive'
                 ):
        """
        初始化统一预处理器
        
        Args:
            outlier_method: 异常值检测方法 ('iqr', 'z_score', 'isolation_forest', 'winsorize', 'none')
            outlier_config: 异常值处理详细配置
            missing_strategy: 缺失值处理策略 ('mean', 'median', 'knn', 'forward_fill', 'interpolation', 'none')
            missing_config: 缺失值处理详细配置
            scaling_method: 缩放方法 ('standard', 'minmax', 'robust', 'quantile', 'power', 'none')
            scaling_config: 缩放详细配置
            data_quality_config: 数据质量检查配置
            distribution_transform_config: 分布变换配置
            stability_config: 稳定性增强配置
            feature_engineering: 是否进行特征工程
            feature_engineering_config: 特征工程详细配置
            time_series_aware: 是否考虑时间序列特性
            mode: 预处理模式
            optimization_level: 优化级别
        """
        self.outlier_method = outlier_method
        self.missing_strategy = missing_strategy
        self.scaling_method = scaling_method
        self.feature_engineering = feature_engineering
        self.time_series_aware = time_series_aware
        self.mode = mode
        self.optimization_level = optimization_level
        
        # 初始化配置
        self.outlier_config = self._init_outlier_config(outlier_config)
        self.missing_config = self._init_missing_config(missing_config)
        self.scaling_config = self._init_scaling_config(scaling_config)
        self.data_quality_config = self._init_data_quality_config(data_quality_config)
        self.distribution_config = self._init_distribution_config(distribution_transform_config)
        self.stability_config = self._init_stability_config(stability_config)
        self.feature_engineering_config = self._init_feature_engineering_config(feature_engineering_config)
        
        # 初始化处理器
        self.scaler = self._init_scaler()
        self.imputer = self._init_imputer()
        
        # 记录处理信息
        self.preprocessing_stats = {}
        self.feature_stability = {}
        self.optimization_stats = {}
        
        # 记录训练时生成的特征（用于保证推理一致性）
        self.trained_feature_names = None
        self.trained_engineered_features = []
        
        # 记录实际使用的策略
        self.actual_missing_strategy = missing_strategy
        self.actual_outlier_method = outlier_method
        
        # 初始化日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 为OptimizationMixin兼容性添加属性
        self.enable_optimization = True
        
        print(f"统一特征预处理器初始化完成 (模式: {mode})")
        print(f"  异常值检测: {outlier_method}")
        print(f"  缺失值策略: {missing_strategy}")
        print(f"  缩放方法: {scaling_method}")
        print(f"  特征工程: {feature_engineering}")
        print(f"  时间序列感知: {time_series_aware}")
        print(f"  优化级别: {optimization_level}")
    
    def _init_outlier_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化异常值处理配置"""
        default_config = {
            'iqr_factor': 1.5,
            'zscore_threshold': 3.0,
            'winsorize_limits': [0.01, 0.01],
            'isolation_forest_contamination': 0.1,
            'clip_extreme': True,
            'use_mad_preprocessing': True,  # 使用中位数绝对偏差预处理
            'mad_threshold': 3.0
        }
        
        # 根据优化级别调整
        if self.optimization_level == 'light':
            default_config['iqr_factor'] = 2.0
            default_config['zscore_threshold'] = 3.5
        elif self.optimization_level == 'aggressive':
            default_config['iqr_factor'] = 1.0
            default_config['zscore_threshold'] = 2.5
            default_config['winsorize_limits'] = [0.02, 0.02]
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_missing_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化缺失值处理配置"""
        default_config = {
            'knn_neighbors': 5,
            'interpolation_method': 'linear',
            'forward_fill_limit': None,
            'missing_threshold': 0.3,  # 缺失值比例阈值
            'auto_switch_threshold': 100000,  # 自动切换阈值（数据点数）
            'auto_switch_method': 'median'  # 大数据量时的备选方法
        }
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_scaling_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化缩放配置"""
        default_config = {
            'quantile_output_distribution': 'normal',
            'power_method': 'yeo-johnson',
            'robust_with_centering': True,
            'robust_with_scaling': True,
            'minmax_feature_range': (0, 1)
        }
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_data_quality_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化数据质量配置"""
        default_config = {
            'missing_value_threshold': 0.3,
            'zero_value_threshold': 0.5,
            'constant_value_threshold': 0.95,  # 常数值比例阈值
            'enable_interpolation': True,
            'interpolation_method': 'linear',
            'check_infinite_values': True,
            'check_data_types': True
        }
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_distribution_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化分布变换配置"""
        default_config = {
            'enable_transforms': True,
            'enable_normalization': True,
            'normalization_method': 'standard',  # 'standard', 'robust', 'quantile'
            'enable_skewness_correction': True,
            'skewness_threshold': 1.0,
            'enable_box_cox': False,  # 是否启用Box-Cox变换
            'box_cox_lambda': None,
            'log_transform': True,
            'sqrt_transform': True,
            'square_transform': True,
            'standardization': True
        }
        
        # 根据模式调整
        if self.mode == 'autogluon_friendly':
            default_config['enable_skewness_correction'] = False  # AutoGluon自己处理
            default_config['enable_normalization'] = False
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_stability_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化稳定性增强配置"""
        default_config = {
            'enable_rolling_standardization': False,  # 默认关闭，避免数据泄露
            'rolling_window': 20,
            'enable_smoothing': False,
            'smoothing_alpha': 0.1,
            'enable_outlier_smoothing': True
        }
        
        # 根据模式调整
        if self.mode == 'factor_calculation':
            default_config['enable_rolling_standardization'] = True
            default_config['enable_smoothing'] = True
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_feature_engineering_config(self, config: Optional[Dict]) -> Dict[str, Any]:
        """初始化特征工程配置"""
        default_config = {
            'create_interactions': True,
            'max_interaction_features': 10,
            'max_features_for_interaction': 10,
            'create_polynomials': False,  # 默认关闭多项式特征
            'polynomial_degree': 2,
            'max_polynomial_features': 5,
            'create_time_series_features': True,
            'time_series_windows': [5, 10, 20],
            'max_time_series_features': 5,
            'create_ratios': True,
            'important_feature_keywords': [
                'arbitrage_space', 'momentum', 'volatility', 
                'double_low_score', 'premium_factor', 'price_factor'
            ]
        }
        
        # 根据模式调整
        if self.mode == 'autogluon_friendly':
            default_config['create_interactions'] = False  # AutoGluon自己处理
            default_config['create_polynomials'] = False
        
        if config:
            default_config.update(config)
        return default_config
    
    def _init_scaler(self):
        """初始化缩放器"""
        if self.scaling_method == 'none':
            return None
        
        scalers = {
            'standard': StandardScaler(),
            'minmax': MinMaxScaler(feature_range=self.scaling_config['minmax_feature_range']),
            'robust': RobustScaler(
                with_centering=self.scaling_config['robust_with_centering'],
                with_scaling=self.scaling_config['robust_with_scaling']
            ),
            'quantile': QuantileTransformer(
                output_distribution=self.scaling_config['quantile_output_distribution']
            ),
            'power': PowerTransformer(method=self.scaling_config['power_method'])
        }
        return scalers.get(self.scaling_method, RobustScaler())
    
    def _init_imputer(self):
        """初始化缺失值填充器"""
        if self.missing_strategy == 'none':
            return None
        
        imputers = {
            'mean': SimpleImputer(strategy='mean'),
            'median': SimpleImputer(strategy='median'),
            'knn': KNNImputer(n_neighbors=self.missing_config['knn_neighbors']),
            'forward_fill': None,  # 特殊处理
            'interpolation': None  # 特殊处理
        }
        return imputers.get(self.missing_strategy, KNNImputer(n_neighbors=5))
    
    def preprocess_features(self, df: pd.DataFrame, feature_names: List[str], 
                          target_name: str = None, is_training: bool = True,
                          context: str = 'default') -> pd.DataFrame:
        """
        完整的特征预处理流程
        
        Args:
            df: 输入数据
            feature_names: 特征列名列表
            target_name: 目标变量名（可选）
            is_training: 是否为训练阶段
            context: 处理上下文（用于统计记录）
            
        Returns:
            预处理后的数据
        """
        print(f"\n🔄 开始统一特征预处理 (模式: {self.mode})...")
        print(f"原始数据: {len(df)} 条记录, {len(feature_names)} 个特征")
        
        # 复制数据
        processed_df = df.copy()
        
        # 初始化统计信息
        step_stats = {
            'context': context,
            'mode': self.mode,
            'original_shape': processed_df.shape,
            'steps_applied': []
        }
        
        try:
            # 1. 数据质量检查和修复
            processed_df = self._check_and_fix_data_quality(processed_df, feature_names)
            step_stats['steps_applied'].append('data_quality_check')
            
            # 2. 处理缺失值
            processed_df = self._handle_missing_values(processed_df, feature_names, is_training)
            step_stats['steps_applied'].append('missing_values')
            
            # 3. 异常值检测和处理
            processed_df = self._handle_outliers(processed_df, feature_names, is_training)
            step_stats['steps_applied'].append('outlier_handling')
            
            # 4. 分布变换（在特征工程之前）
            if self.distribution_config['enable_normalization'] or self.distribution_config['enable_skewness_correction']:
                processed_df = self._apply_distribution_transforms(processed_df, feature_names, is_training)
                step_stats['steps_applied'].append('distribution_transform')
            
            # 5. 特征工程
            if self.feature_engineering:
                processed_df, feature_names = self._feature_engineering(processed_df, feature_names, is_training)
                step_stats['steps_applied'].append('feature_engineering')
            
            # 6. 特征缩放
            if self.scaling_method != 'none':
                processed_df = self._scale_features(processed_df, feature_names, is_training)
                step_stats['steps_applied'].append('feature_scaling')
            
            # 7. 稳定性增强
            if any(self.stability_config.values()):
                processed_df = self._enhance_stability(processed_df, feature_names, is_training)
                step_stats['steps_applied'].append('stability_enhancement')
            
            # 8. 特征稳定性检查
            if is_training:
                self._check_feature_stability(processed_df, feature_names)
                step_stats['steps_applied'].append('stability_check')
            
            # 9. 最终数据验证
            processed_df = self._final_validation(processed_df, feature_names, target_name)
            step_stats['steps_applied'].append('final_validation')
            
            step_stats['final_shape'] = processed_df.shape
            step_stats['success'] = True
            
        except Exception as e:
            self.logger.error(f"预处理过程中出错: {e}")
            step_stats['success'] = False
            step_stats['error'] = str(e)
            processed_df = df  # 返回原始数据
        
        # 记录统计信息
        self.preprocessing_stats[context] = step_stats
        
        print(f"✅ 预处理完成: {len(processed_df)} 条记录")
        self._print_preprocessing_summary()
        
        return processed_df
    
    def _check_feature_stability(self, df: pd.DataFrame, feature_names: List[str]):
        """检查特征稳定性"""
        print("🔍 特征稳定性检查...")
        
        if not hasattr(self, 'feature_stability'):
            self.feature_stability = {}
        
        for feature in feature_names:
            if feature not in df.columns:
                continue
                
            feature_data = df[feature]
            
            # 计算稳定性指标
            stability_stats = {
                'variance': feature_data.var(),
                'coefficient_of_variation': feature_data.std() / abs(feature_data.mean()) if feature_data.mean() != 0 else np.inf,
                'zero_variance': feature_data.var() == 0,
                'constant_values': feature_data.nunique() == 1
            }
            
            self.feature_stability[feature] = stability_stats
        
        # 识别问题特征
        problem_features = []
        for feature, stats in self.feature_stability.items():
            if stats['zero_variance'] or stats['constant_values']:
                problem_features.append(feature)
        
        if problem_features:
            print(f"⚠️  发现 {len(problem_features)} 个稳定性问题特征:")
            for feature in problem_features[:5]:
                print(f"    {feature}")
    
    def _print_preprocessing_summary(self):
        """打印预处理总结"""
        print(f"\n📋 预处理总结:")
        
        if 'quality_check' in self.preprocessing_stats:
            quality_stats = self.preprocessing_stats['quality_check']
            print(f"  数据质量: 检查了 {len(quality_stats)} 个特征")
        
        if 'missing_values' in self.preprocessing_stats:
            missing_stats = self.preprocessing_stats['missing_values']
            method_info = missing_stats['method']
            if 'original_method' in missing_stats and missing_stats['method'] != missing_stats['original_method']:
                method_info = f"{missing_stats['method']} (原始: {missing_stats['original_method']})"
            print(f"  缺失值: {missing_stats['before']} -> {missing_stats['after']} ({method_info})")
        
        if 'outliers' in self.preprocessing_stats:
            outlier_stats = self.preprocessing_stats['outliers']
            print(f"  异常值: 处理了 {outlier_stats['total_count']} 个 ({outlier_stats['method']})")
        
        if 'feature_engineering' in self.preprocessing_stats:
            fe_stats = self.preprocessing_stats['feature_engineering']
            print(f"  特征工程: {fe_stats['original_count']} -> {fe_stats['original_count'] + fe_stats['new_count']} (+{fe_stats['new_count']})")
        
        if 'scaling' in self.preprocessing_stats:
            scaling_stats = self.preprocessing_stats['scaling']
            print(f"  特征缩放: {scaling_stats['method']}")
    
    def _final_validation(self, df: pd.DataFrame, feature_names: List[str], 
                         target_name: str = None) -> pd.DataFrame:
        """最终数据验证"""
        print("✅ 最终数据验证...")
        
        # 检查无穷值和NaN
        infinite_count = np.isinf(df[feature_names]).sum().sum()
        nan_count = df[feature_names].isnull().sum().sum()

        if infinite_count > 0:
            print(f"⚠️  发现 {infinite_count} 个无穷值，将替换为0")
            df[feature_names] = df[feature_names].replace([np.inf, -np.inf], 0)

        if nan_count > 0:
            print(f"⚠️  发现 {nan_count} 个NaN值")
            # 打印包含NaN值的列名
            nan_columns = df[feature_names].isnull().sum()
            nan_columns = nan_columns[nan_columns > 0]
            if len(nan_columns) > 0:
                print(f"包含NaN值的列名及数量:")
                for col, count in nan_columns.items():
                    print(f"    {col}: {count} 个NaN值")
        
        # 移除目标变量有问题的行
        if target_name and target_name in df.columns:
            before_count = len(df)
            df = df[df[target_name].notna()]
            df = df[np.isfinite(df[target_name])]
            after_count = len(df)
            
            if before_count != after_count:
                print(f"移除目标变量异常的记录: {before_count} -> {after_count}")
        
        return df
    
    def _handle_outliers(self, df: pd.DataFrame, feature_names: List[str], 
                        is_training: bool) -> pd.DataFrame:
        """处理异常值（整合EnhancedFeaturePreprocessor的功能）"""
        if self.outlier_method == 'none':
            return df
            
        print(f"🔍 异常值检测和处理 ({self.outlier_method})...")
        
        outlier_stats = {}
        total_outliers = 0
        
        for feature in feature_names:
            if feature not in df.columns:
                continue
                
            feature_data = df[feature]
            outlier_mask = np.zeros(len(feature_data), dtype=bool)
            
            if self.outlier_method == 'iqr':
                outlier_mask = self._detect_outliers_iqr(feature_data)
            elif self.outlier_method == 'z_score':
                outlier_mask = self._detect_outliers_zscore(feature_data)
            elif self.outlier_method == 'isolation_forest':
                outlier_mask = self._detect_outliers_isolation_forest(feature_data)
            
            outlier_count = outlier_mask.sum()
            if outlier_count > 0:
                # 使用clipping方法处理异常值
                if self.outlier_method == 'iqr':
                    q1, q3 = np.percentile(feature_data.dropna(), [25, 75])
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    df[feature] = np.clip(feature_data, lower_bound, upper_bound)
                else:
                    # 使用5-95分位数进行clipping
                    lower_bound, upper_bound = np.percentile(feature_data.dropna(), [5, 95])
                    df[feature] = np.clip(feature_data, lower_bound, upper_bound)
                
                outlier_stats[feature] = outlier_count
                total_outliers += outlier_count
        
        print(f"  异常值处理: 总共处理了 {total_outliers} 个异常值")
        
        self.preprocessing_stats['outliers'] = {
            'method': self.outlier_method,
            'total_count': total_outliers,
            'feature_stats': outlier_stats
        }
        
        return df
    
    def _detect_outliers_iqr(self, data: pd.Series) -> np.ndarray:
        """IQR方法检测异常值"""
        q1, q3 = np.percentile(data.dropna(), [25, 75])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        return (data < lower_bound) | (data > upper_bound)
    
    def _detect_outliers_zscore(self, data: pd.Series, threshold: float = 3.0) -> np.ndarray:
        """Z-score方法检测异常值"""
        from scipy import stats as scipy_stats
        z_scores = np.abs(scipy_stats.zscore(data.dropna()))
        return z_scores > threshold
    
    def _detect_outliers_isolation_forest(self, data: pd.Series) -> np.ndarray:
        """Isolation Forest方法检测异常值"""
        try:
            from sklearn.ensemble import IsolationForest
            
            data_clean = data.dropna().values.reshape(-1, 1)
            if len(data_clean) < 10:  # 数据太少，跳过
                return np.zeros(len(data), dtype=bool)
                
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            outlier_pred = iso_forest.fit_predict(data_clean)
            
            # 将结果映射回原始数据
            outlier_mask = np.zeros(len(data), dtype=bool)
            outlier_mask[~data.isnull()] = outlier_pred == -1
            return outlier_mask
            
        except ImportError:
            print("  ⚠️  scikit-learn版本不支持IsolationForest，改用IQR方法")
            return self._detect_outliers_iqr(data)
    
    def _check_and_fix_data_quality(self, df: pd.DataFrame, feature_names: List[str]) -> pd.DataFrame:
        """数据质量检查和修复（整合OptimizationMixin的功能）"""
        print("📊 数据质量检查和修复...")
        
        quality_stats = {}
        base_columns = {'ts_code', 'trade_date', 'close', 'full_price', 'volume', 'amount'}
        
        for feature in feature_names:
            if feature not in df.columns:
                continue
            
            feature_data = df[feature]
            
            # 数据类型检查和转换
            if self.data_quality_config['check_data_types']:
                if not pd.api.types.is_numeric_dtype(feature_data):
                    print(f"  ⚠️  特征 {feature} 不是数值型 ({feature_data.dtype})，尝试转换...")
                    try:
                        df[feature] = pd.to_numeric(feature_data, errors='coerce')
                        feature_data = df[feature]
                        print(f"  ✅ 特征 {feature} 已转换为数值型")
                    except Exception as e:
                        print(f"  ❌ 特征 {feature} 转换失败: {e}，跳过此特征")
                        continue
            
            # 统计信息
            stats_dict = {
                'missing_count': feature_data.isnull().sum(),
                'missing_rate': feature_data.isnull().mean(),
                'zero_count': (feature_data == 0).sum(),
                'zero_rate': (feature_data == 0).mean(),
                'unique_count': feature_data.nunique(),
                'dtype': str(feature_data.dtype)
            }
            
            # 无穷值检查
            if self.data_quality_config['check_infinite_values']:
                try:
                    infinite_count = np.isinf(feature_data).sum()
                    stats_dict['infinite_count'] = infinite_count
                    if infinite_count > 0:
                        print(f"  发现 {infinite_count} 个无穷值在 {feature}，转换为NaN")
                        df[feature] = df[feature].replace([np.inf, -np.inf], np.nan)
                except Exception as e:
                    print(f"  ⚠️  检查无穷值失败 {feature}: {e}")
                    stats_dict['infinite_count'] = 0
            
            # 数值统计
            if feature_data.dtype in ['int64', 'float64', 'float32', 'int32']:
                try:
                    stats_dict.update({
                        'mean': feature_data.mean(),
                        'std': feature_data.std(),
                        'min': feature_data.min(),
                        'max': feature_data.max(),
                        'skewness': scipy_stats.skew(feature_data.dropna()),
                        'kurtosis': scipy_stats.kurtosis(feature_data.dropna())
                    })
                except Exception as e:
                    print(f"  ⚠️  计算统计量失败 {feature}: {e}")
            
            # 数据质量问题检查
            issues = []
            
            # 缺失值比例过高
            if stats_dict['missing_rate'] > self.data_quality_config['missing_value_threshold']:
                issues.append(f"缺失率{stats_dict['missing_rate']:.1%}")
                if self.data_quality_config['enable_interpolation']:
                    df[feature] = df[feature].interpolate(
                        method=self.data_quality_config['interpolation_method']
                    )
            
            # 零值比例过高
            if stats_dict['zero_rate'] > self.data_quality_config['zero_value_threshold']:
                issues.append(f"零值率{stats_dict['zero_rate']:.1%}")
            
            # 常数值检查
            if feature_data.nunique() == 1:
                issues.append("常数值")
            elif (feature_data.nunique() / len(feature_data)) < (1 - self.data_quality_config['constant_value_threshold']):
                issues.append("准常数值")
            
            if issues:
                print(f"  ⚠️  {feature}: {', '.join(issues)}")
            
            quality_stats[feature] = stats_dict
        
        self.preprocessing_stats['quality_check'] = quality_stats
        return df
    
    def _handle_missing_values(self, df: pd.DataFrame, feature_names: List[str], 
                             is_training: bool) -> pd.DataFrame:
        """处理缺失值（整合两个类的功能）"""
        print("🔧 处理缺失值...")
        
        missing_before = df[feature_names].isnull().sum().sum()
        
        if self.missing_strategy == 'none':
            print(f"  缺失值策略: none - 不进行填充，保留 {missing_before} 个缺失值")
            missing_after = missing_before
        else:
            # 检查数据规模，决定是否自动切换策略
            feature_data = df[feature_names]
            data_size = len(feature_data) * len(feature_names)
            
            print(f"  数据规模: {len(feature_data)} 行 x {len(feature_names)} 列 = {data_size:,} 个数据点")
            
            # 自动切换逻辑
            if (self.missing_strategy == 'knn' and 
                data_size > self.missing_config['auto_switch_threshold']):
                if is_training:
                    print(f"  ⚠️  数据量过大({data_size:,}个点)，KNN可能很慢，切换到{self.missing_config['auto_switch_method']}填充")
                    temp_imputer = SimpleImputer(strategy=self.missing_config['auto_switch_method'])
                    filled_data = temp_imputer.fit_transform(feature_data)
                    self.imputer = temp_imputer
                    self.actual_missing_strategy = self.missing_config['auto_switch_method']
                    print(f"  💾 实际使用策略已保存: {self.actual_missing_strategy} (原始配置: {self.missing_strategy})")
                else:
                    # 推理时使用训练时的实际策略
                    if hasattr(self, 'actual_missing_strategy'):
                        print(f"  ✅ 使用训练时的实际策略: {self.actual_missing_strategy}")
                        filled_data = self.imputer.transform(feature_data)
                    else:
                        print(f"  ⚠️  训练时使用了不同的填充策略，回退到median")
                        temp_imputer = SimpleImputer(strategy='median')
                        filled_data = temp_imputer.fit_transform(feature_data)
            
            elif self.missing_strategy == 'forward_fill' and self.time_series_aware:
                # 时间序列前向填充
                if 'trade_date' in df.columns and 'ts_code' in df.columns:
                    df = df.sort_values(['ts_code', 'trade_date'])
                    for feature in feature_names:
                        if feature in df.columns:
                            df[feature] = df.groupby('ts_code')[feature].fillna(method='ffill')
                else:
                    df[feature_names] = df[feature_names].fillna(method='ffill')
                filled_data = df[feature_names].values
            
            elif self.missing_strategy == 'interpolation':
                # 插值填充
                for feature in feature_names:
                    if feature in df.columns:
                        df[feature] = df[feature].interpolate(
                            method=self.missing_config['interpolation_method']
                        )
                filled_data = df[feature_names].values
            
            else:
                # 使用标准填充器
                if is_training:
                    print(f"  开始训练{self.missing_strategy}填充器...")
                    filled_data = self.imputer.fit_transform(feature_data)
                    print(f"  ✅ {self.missing_strategy}填充器训练完成")
                else:
                    filled_data = self.imputer.transform(feature_data)
            
            # 更新数据
            if self.missing_strategy not in ['forward_fill', 'interpolation']:
                df[feature_names] = filled_data
        
        missing_after = df[feature_names].isnull().sum().sum()
        print(f"  缺失值: {missing_before} -> {missing_after} (减少{missing_before - missing_after})")
        
        # 记录统计信息
        actual_strategy = getattr(self, 'actual_missing_strategy', self.missing_strategy)
        self.preprocessing_stats['missing_values'] = {
            'before': missing_before,
            'after': missing_after,
            'method': actual_strategy,
            'original_method': self.missing_strategy
        }
        
        return df
    
    def _apply_distribution_transforms(self, df: pd.DataFrame, feature_names: List[str], 
                                     is_training: bool) -> pd.DataFrame:
        """应用分布变换（来自OptimizationMixin）"""
        if not self.distribution_config['enable_transforms']:
            return df
        
        print("📈 应用分布变换...")
        
        processed_df = df.copy()
        transform_stats = {}
        
        # 获取因子列（排除基础数据列）
        base_columns = {'ts_code', 'trade_date', 'close', 'full_price', 'volume', 'amount'}
        factor_columns = [col for col in feature_names if col not in base_columns]
        
        for col in factor_columns:
            try:
                if col not in processed_df.columns:
                    continue
                
                if processed_df[col].dtype not in ['float64', 'int64']:
                    continue
                
                current_data = processed_df[col].dropna()
                if len(current_data) < 10:
                    continue
                
                # 偏度校正
                if self.distribution_config['enable_skewness_correction']:
                    skewness = scipy_stats.skew(current_data)
                    skew_threshold = self.distribution_config['skewness_threshold']
                    
                    if abs(skewness) > skew_threshold:
                        print(f"  {col}: 偏度 {skewness:.3f} > {skew_threshold}，应用变换")
                        
                        # 选择变换方法
                        if skewness > 0:  # 右偏
                            if self.distribution_config['log_transform'] and (current_data > 0).all():
                                # 对数变换
                                processed_df[col] = np.log1p(current_data)
                                transform_stats[col] = 'log_transform'
                            elif self.distribution_config['sqrt_transform'] and (current_data >= 0).all():
                                # 平方根变换
                                processed_df[col] = np.sqrt(current_data)
                                transform_stats[col] = 'sqrt_transform'
                        else:  # 左偏
                            if self.distribution_config['square_transform']:
                                # 平方变换
                                processed_df[col] = np.square(current_data)
                                transform_stats[col] = 'square_transform'
                
                # 标准化
                if self.distribution_config['standardization']:
                    mean_val = current_data.mean()
                    std_val = current_data.std()
                    
                    if std_val > 0:
                        processed_df[col] = (processed_df[col] - mean_val) / std_val
                        if col not in transform_stats:
                            transform_stats[col] = 'standardization'
                        else:
                            transform_stats[col] += '+standardization'
                
            except Exception as e:
                self.logger.warning(f"应用分布变换到 {col} 时出错: {e}")
                continue
        
        if transform_stats:
            print(f"  应用了分布变换的特征: {len(transform_stats)} 个")
        
        self.preprocessing_stats['distribution_transforms'] = transform_stats
        return processed_df

    def _feature_engineering(self, df: pd.DataFrame, feature_names: List[str], 
                           is_training: bool) -> pd.DataFrame:
        """特征工程（整合EnhancedFeaturePreprocessor的功能）"""
        if not self.feature_engineering:
            return df
        
        print("🔧 特征工程...")
        
        processed_df = df.copy()
        new_features = []
        
        # 获取数值型特征
        numeric_features = [col for col in feature_names 
                          if col in processed_df.columns and 
                          processed_df[col].dtype in ['float64', 'int64', 'float32', 'int32']]
        
        # 排除基础数据列
        base_columns = {'ts_code', 'trade_date', 'close', 'full_price', 'volume', 'amount'}
        factor_features = [col for col in numeric_features if col not in base_columns]
        
        if len(factor_features) < 2:
            print("  可用于特征工程的因子数量不足，跳过")
            return processed_df
        
        # 限制特征数量以避免组合爆炸
        max_features = min(len(factor_features), self.feature_engineering_config['max_features_for_interaction'])
        selected_features = factor_features[:max_features]
        
        if is_training:
            # 训练时生成新特征
            
            # 1. 特征交互（两两相乘）
            if self.feature_engineering_config['create_interactions']:
                interaction_count = 0
                max_interactions = self.feature_engineering_config['max_interaction_features']
                
                for i, feat1 in enumerate(selected_features):
                    for feat2 in selected_features[i+1:]:
                        if interaction_count >= max_interactions:
                            break
                        
                        interaction_name = f"{feat1}_x_{feat2}"
                        try:
                            processed_df[interaction_name] = processed_df[feat1] * processed_df[feat2]
                            new_features.append(interaction_name)
                            interaction_count += 1
                        except Exception as e:
                            self.logger.warning(f"创建交互特征 {interaction_name} 失败: {e}")
                    
                    if interaction_count >= max_interactions:
                        break
                
                print(f"  创建了 {interaction_count} 个交互特征")
            
            # 2. 多项式特征（平方项）
            if self.feature_engineering_config['create_polynomials']:
                poly_count = 0
                max_poly = min(len(selected_features), self.feature_engineering_config['max_polynomial_features'])
                
                for feat in selected_features[:max_poly]:
                    poly_name = f"{feat}_squared"
                    try:
                        processed_df[poly_name] = processed_df[feat] ** 2
                        new_features.append(poly_name)
                        poly_count += 1
                    except Exception as e:
                        self.logger.warning(f"创建多项式特征 {poly_name} 失败: {e}")
                
                print(f"  创建了 {poly_count} 个多项式特征")
            
            # 3. 时间序列特征
            if (self.feature_engineering_config['create_time_series_features'] and 
                self.time_series_aware and 'trade_date' in processed_df.columns):
                
                ts_count = 0
                max_ts = min(len(selected_features), self.feature_engineering_config['max_time_series_features'])
                
                # 确保数据按时间排序
                if 'ts_code' in processed_df.columns:
                    processed_df = processed_df.sort_values(['ts_code', 'trade_date'])
                else:
                    processed_df = processed_df.sort_values('trade_date')
                
                for feat in selected_features[:max_ts]:
                    try:
                        # 滞后特征
                        lag_name = f"{feat}_lag1"
                        if 'ts_code' in processed_df.columns:
                            processed_df[lag_name] = processed_df.groupby('ts_code')[feat].shift(1)
                        else:
                            processed_df[lag_name] = processed_df[feat].shift(1)
                        new_features.append(lag_name)
                        
                        # 移动平均
                        ma_name = f"{feat}_ma3"
                        if 'ts_code' in processed_df.columns:
                            processed_df[ma_name] = processed_df.groupby('ts_code')[feat].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
                        else:
                            processed_df[ma_name] = processed_df[feat].rolling(3, min_periods=1).mean()
                        new_features.append(ma_name)
                        
                        ts_count += 2
                    except Exception as e:
                        self.logger.warning(f"创建时间序列特征 {feat} 失败: {e}")
                
                print(f"  创建了 {ts_count} 个时间序列特征")
            
            # 保存生成的特征名称
            self.generated_features = new_features
            print(f"  总共生成了 {len(new_features)} 个新特征")
        
        else:
            # 推理时确保生成相同的特征
            if hasattr(self, 'generated_features'):
                print(f"  推理模式: 重新生成 {len(self.generated_features)} 个特征")
                
                for feature_name in self.generated_features:
                    try:
                        if '_x_' in feature_name:  # 交互特征
                            feat1, feat2 = feature_name.replace('_x_', ' ').split(' ', 1)
                            feat2 = feat2.replace('_x_', '_')
                            if feat1 in processed_df.columns and feat2 in processed_df.columns:
                                processed_df[feature_name] = processed_df[feat1] * processed_df[feat2]
                        
                        elif feature_name.endswith('_squared'):  # 多项式特征
                            base_feat = feature_name.replace('_squared', '')
                            if base_feat in processed_df.columns:
                                processed_df[feature_name] = processed_df[base_feat] ** 2
                        
                        elif feature_name.endswith('_lag1'):  # 滞后特征
                            base_feat = feature_name.replace('_lag1', '')
                            if base_feat in processed_df.columns:
                                if 'ts_code' in processed_df.columns:
                                    processed_df = processed_df.sort_values(['ts_code', 'trade_date'])
                                    processed_df[feature_name] = processed_df.groupby('ts_code')[base_feat].shift(1)
                                else:
                                    processed_df = processed_df.sort_values('trade_date')
                                    processed_df[feature_name] = processed_df[base_feat].shift(1)
                        
                        elif feature_name.endswith('_ma3'):  # 移动平均
                            base_feat = feature_name.replace('_ma3', '')
                            if base_feat in processed_df.columns:
                                if 'ts_code' in processed_df.columns:
                                    processed_df = processed_df.sort_values(['ts_code', 'trade_date'])
                                    processed_df[feature_name] = processed_df.groupby('ts_code')[base_feat].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
                                else:
                                    processed_df = processed_df.sort_values('trade_date')
                                    processed_df[feature_name] = processed_df[base_feat].rolling(3, min_periods=1).mean()
                    
                    except Exception as e:
                        self.logger.warning(f"重新生成特征 {feature_name} 失败: {e}")
                        # 如果无法生成，创建空列
                        processed_df[feature_name] = np.nan
            else:
                print("  ⚠️  推理模式但没有找到训练时生成的特征信息")
        
        # 更新特征名称列表
        updated_feature_names = feature_names + new_features if is_training else feature_names + getattr(self, 'generated_features', [])
        
        self.preprocessing_stats['feature_engineering'] = {
            'enabled': True,
            'original_count': len(feature_names),
            'new_count': len(new_features) if is_training else len(getattr(self, 'generated_features', [])),
            'generated_features': len(new_features) if is_training else len(getattr(self, 'generated_features', [])),
            'feature_names': new_features if is_training else getattr(self, 'generated_features', [])
        }
        
        return processed_df, updated_feature_names
    
    def _scale_features(self, df: pd.DataFrame, feature_names: List[str], 
                      is_training: bool) -> pd.DataFrame:
        """特征缩放（来自EnhancedFeaturePreprocessor）"""
        if self.scaling_method == 'none':
            return df
        
        print(f"🔧 特征缩放 ({self.scaling_method})...")
        
        processed_df = df.copy()
        
        # 获取数值特征
        numeric_features = []
        for col in feature_names:
            if col in processed_df.columns:
                if processed_df[col].dtype in ['float64', 'int64']:
                    numeric_features.append(col)
        
        if not numeric_features:
            print("  没有数值特征需要缩放")
            return processed_df
        
        feature_data = processed_df[numeric_features]
        
        if is_training:
            scaled_data = self.scaler.fit_transform(feature_data)
        else:
            scaled_data = self.scaler.transform(feature_data)
        
        # 更新数据
        processed_df[numeric_features] = scaled_data
        
        # 记录统计信息
        self.preprocessing_stats['scaling'] = {
            'method': self.scaling_method,
            'feature_count': len(numeric_features),
            'features': numeric_features
        }
        
        print(f"  缩放完成: {len(numeric_features)} 个特征")
        return processed_df
    
    def _enhance_stability(self, df: pd.DataFrame, feature_names: List[str], 
                          is_training: bool) -> pd.DataFrame:
        """增强稳定性（来自OptimizationMixin）"""
        if df.empty:
            return df
        
        print("🔧 稳定性增强...")
        
        enhanced_df = df.copy()
        config = self.stability_config
        
        # 确保数据按日期排序
        if 'trade_date' in enhanced_df.columns:
            if 'ts_code' in enhanced_df.columns:
                enhanced_df = enhanced_df.sort_values(['ts_code', 'trade_date'])
            else:
                enhanced_df = enhanced_df.sort_values('trade_date')
        
        # 获取数值特征
        numeric_features = [col for col in feature_names 
                          if col in enhanced_df.columns and 
                          enhanced_df[col].dtype in ['float64', 'int64', 'float32', 'int32']]
        
        for col in numeric_features:
            if col not in enhanced_df.columns:
                continue
            
            # 滚动标准化
            if config['enable_rolling_standardization']:
                window = config['rolling_window']
                if len(enhanced_df) >= window:
                    if 'ts_code' in enhanced_df.columns:
                        # 按股票分组进行滚动标准化
                        rolling_mean = enhanced_df.groupby('ts_code')[col].rolling(
                            window=window, min_periods=1).mean().reset_index(0, drop=True)
                        rolling_std = enhanced_df.groupby('ts_code')[col].rolling(
                            window=window, min_periods=1).std().reset_index(0, drop=True)
                    else:
                        rolling_mean = enhanced_df[col].rolling(window=window, min_periods=1).mean()
                        rolling_std = enhanced_df[col].rolling(window=window, min_periods=1).std()
                    
                    # 避免除零
                    rolling_std = rolling_std.fillna(1.0)
                    rolling_std = rolling_std.replace(0, 1.0)
                    
                    enhanced_df[col] = (enhanced_df[col] - rolling_mean) / rolling_std
            
            # 时间序列平滑
            if config['enable_smoothing']:
                alpha = config['smoothing_alpha']
                if 'ts_code' in enhanced_df.columns:
                    enhanced_df[col] = enhanced_df.groupby('ts_code')[col].transform(
                        lambda x: x.ewm(alpha=alpha).mean())
                else:
                    enhanced_df[col] = enhanced_df[col].ewm(alpha=alpha).mean()
            
            # 异常值平滑
            if config['enable_outlier_smoothing']:
                # 使用中位数绝对偏差进行异常值检测和平滑
                median_val = enhanced_df[col].median()
                mad_val = (enhanced_df[col] - median_val).abs().median()
                if mad_val > 0:
                    threshold = 3 * mad_val
                    outlier_mask = (enhanced_df[col] - median_val).abs() > threshold
                    if outlier_mask.sum() > 0:
                        # 用中位数替换异常值
                        enhanced_df.loc[outlier_mask, col] = median_val
        
        print(f"  稳定性增强完成: {len(numeric_features)} 个特征")
        return enhanced_df
    
    # OptimizationMixin兼容接口
    def _apply_optimization(self, raw_factors: pd.DataFrame, context: str) -> pd.DataFrame:
        """应用优化策略（兼容OptimizationMixin接口）"""
        if not self.enable_optimization or raw_factors.empty:
            return raw_factors
        
        # 获取因子列
        base_columns = {'ts_code', 'trade_date', 'close', 'full_price', 'volume', 'amount'}
        feature_columns = [col for col in raw_factors.columns if col not in base_columns]
        
        if feature_columns:
            try:
                processed_factors = self.preprocess_features(
                    raw_factors.copy(), 
                    feature_columns, 
                    is_training=True,
                    context=context
                )
                
                self.optimization_stats[context] = {
                    'method': 'UnifiedFeaturePreprocessor',
                    'original_shape': raw_factors.shape,
                    'processed_shape': processed_factors.shape,
                    'optimization_success': True
                }
                
                return processed_factors
            except Exception as e:
                self.logger.error(f"优化处理失败: {e}")
                self.optimization_stats[context] = {
                    'optimization_success': False,
                    'error': str(e)
                }
                return raw_factors
        else:
            return raw_factors
    
    def _apply_single_bond_optimization(self, factors: pd.DataFrame, date_str: str, bond_code: str) -> pd.DataFrame:
        """应用单债券优化（兼容OptimizationMixin接口）"""
        if not hasattr(self, 'enable_optimization'):
            self.enable_optimization = True
        if not self.enable_optimization or factors.empty:
            return factors
        
        context = f"single_bond_{bond_code}_{date_str}"
        return self._apply_optimization(factors, context)
    
    def set_optimization_enabled(self, enabled: bool):
        """设置优化开关（兼容OptimizationMixin接口）"""
        self.enable_optimization = enabled
    
    def is_optimization_enabled(self) -> bool:
        """检查优化是否启用（兼容OptimizationMixin接口）"""
        if not hasattr(self, 'enable_optimization'):
            self.enable_optimization = True
        return self.enable_optimization
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息（兼容OptimizationMixin接口）"""
        return self.optimization_stats.copy()
    
    def reset_optimization_stats(self):
        """重置优化统计信息（兼容OptimizationMixin接口）"""
        self.optimization_stats = {}


def create_unified_preprocessor(preset: str = 'default') -> UnifiedFeaturePreprocessor:
    """
    创建统一特征预处理器的工厂函数
    
    Args:
        preset: 预设类型，可选值：
            - 'default': 默认配置，平衡性能和效果
            - 'conservative': 保守配置，最小化数据变化
            - 'aggressive': 激进配置，最大化数据清理
            - 'time_series': 时间序列友好配置
            - 'fast_training': 快速训练配置
            - 'no_imputation': 不进行缺失值填充
            - 'factor_calculation': 因子计算专用配置
            - 'autogluon_friendly': AutoGluon友好配置
    
    Returns:
        UnifiedFeaturePreprocessor: 配置好的预处理器实例
    """
    
    presets = {
        'default': {
            'outlier_method': 'iqr',
            'outlier_config': {
                'iqr_factor': 1.5,
                'clip_extreme': True,
                'use_mad_preprocessing': False,
                'mad_threshold': 3.0
            },
            'scaling_method': 'robust',
            'missing_strategy': 'median',
            'feature_engineering': True,
            'time_series_aware': True
        },
        
        'conservative': {
            'outlier_method': 'none',
            'outlier_config': {},
            'scaling_method': 'none',
            'missing_strategy': 'forward_fill',
            'feature_engineering': False,
            'time_series_aware': True
        },
        
        'aggressive': {
            'outlier_method': 'iqr',
            'outlier_config': {
                'iqr_factor': 1.0,
                'clip_extreme': True,
                'use_mad_preprocessing': True,
                'mad_threshold': 2.5
            },
            'scaling_method': 'standard',
            'missing_strategy': 'knn',
            'feature_engineering': True,
            'time_series_aware': True
        },
        
        'time_series': {
            'outlier_method': 'winsorize',
            'outlier_config': {
                'winsorize_limits': (0.01, 0.01),
                'clip_extreme': True
            },
            'scaling_method': 'robust',
            'missing_strategy': 'forward_fill',
            'feature_engineering': True,
            'time_series_aware': True
        },
        
        'fast_training': {
            'outlier_method': 'winsorize',
            'outlier_config': {
                'winsorize_limits': (0.05, 0.05),
                'clip_extreme': True
            },
            'scaling_method': 'minmax',
            'missing_strategy': 'median',
            'feature_engineering': False,
            'time_series_aware': False
        },
        
        'no_imputation': {
            'outlier_method': 'iqr',
            'outlier_config': {
                'iqr_factor': 1.5,
                'clip_extreme': True
            },
            'scaling_method': 'robust',
            'missing_strategy': 'none',
            'feature_engineering': True,
            'time_series_aware': True
        },
        
        'factor_calculation': {
            'outlier_method': 'iqr',
            'outlier_config': {
                'iqr_factor': 2.0,
                'clip_extreme': True,
                'use_mad_preprocessing': True,
                'mad_threshold': 3.5
            },
            'scaling_method': 'robust',
            'missing_strategy': 'median',
            'feature_engineering': True,
            'time_series_aware': True
        },
        
        'autogluon_friendly': {
            'outlier_method': 'winsorize',
            'outlier_config': {
                'winsorize_limits': (0.02, 0.02),
                'clip_extreme': True
            },
            'scaling_method': 'standard',
            'missing_strategy': 'median',
            'feature_engineering': True,
            'time_series_aware': True
        }
    }
    
    if preset not in presets:
        raise ValueError(f"未知的预设类型: {preset}. 可选值: {list(presets.keys())}")
    
    config = presets[preset]
    
    return UnifiedFeaturePreprocessor(
        outlier_method=config['outlier_method'],
        outlier_config=config['outlier_config'],
        scaling_method=config['scaling_method'],
        missing_strategy=config['missing_strategy'],
        feature_engineering=config['feature_engineering'],
        time_series_aware=config.get('time_series_aware', True)
    )


if __name__ == "__main__":
    # 测试用例
    print("测试统一特征预处理器...")
    
    # 测试不同预设
    presets_to_test = ['default', 'conservative', 'aggressive', 'autogluon_friendly']
    
    for preset in presets_to_test:
        print(f"\n测试预设: {preset}")
        try:
            preprocessor = create_unified_preprocessor(preset)
            print(f"  ✅ {preset} 预设创建成功")
            print(f"  - 异常值方法: {preprocessor.outlier_method}")
            print(f"  - 缩放方法: {preprocessor.scaling_method}")
            print(f"  - 缺失值策略: {preprocessor.missing_strategy}")
            print(f"  - 特征工程: {preprocessor.feature_engineering}")
            print(f"  - 时间感知: {preprocessor.time_aware}")
            print(f"  - 稳定性增强: {preprocessor.stability_enhancement}")
        except Exception as e:
            print(f"  ❌ {preset} 预设创建失败: {e}")
    
    print("\n统一特征预处理器测试完成！")