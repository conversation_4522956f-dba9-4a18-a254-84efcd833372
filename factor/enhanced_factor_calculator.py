"""
增强因子计算器
实现可转债特有的高级因子，包括时间价值、转股博弈、市场结构等因子
"""

import pandas as pd
import numpy as np
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import scipy.stats

from .base_factor_calculator import FactorCalculator
from config.config import config_manager


class EnhancedFactorCalculator(FactorCalculator):
    """
    增强因子计算器
    
    实现可转债特有的高级因子：
    1. 可转债特有因子（时间价值、转股博弈等）
    2. 市场结构因子（资金流向、情绪等）
    3. 交叉组合因子（多因子组合）
    4. 技术形态因子（均线、支撑阻力等）
    """
    
    def __init__(self):
        super().__init__("enhanced_factor")
        print("增强因子计算器初始化完成")
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """计算指定日期的增强因子"""
        return self._calculate_enhanced_factors(date, date, **kwargs)
        
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """计算指定时期的增强因子"""
        return self._calculate_enhanced_factors(start_date, end_date, **kwargs)
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """计算单个可转债的增强因子"""
        result = self._calculate_enhanced_factors(date, date, **kwargs)
        if not result.empty:
            # 筛选特定债券
            result = result[result['ts_code'] == bond_code]
        return result
    
    def _calculate_enhanced_factors(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """计算增强因子的核心逻辑"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            
            # 获取基础数据
            base_data = self._get_base_data(conn, start_date, end_date)
            
            if base_data is None or base_data.empty:
                self.logger.warning("无法获取基础数据")
                return pd.DataFrame()
            
            # 计算各类增强因子
            result = base_data.copy()
            
            # 1. 可转债特有因子
            result = self._calculate_convertible_specific_factors(result, conn)
            
            # 2. 市场结构因子  
            result = self._calculate_market_structure_factors(result, conn)
            
            # 3. 交叉组合因子
            result = self._calculate_cross_combination_factors(result)
            
            # 4. 技术形态因子
            result = self._calculate_technical_pattern_factors(result, conn)
            
            conn.close()
            
            # 注意：因子计算器只负责计算原始因子数据，不进行预处理
            # 预处理在ML模型训练/预测时进行
            # 进行基本的后处理以确保数据质量
            result = self._postprocess_factors(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算增强因子时发生错误: {e}")
            return pd.DataFrame()
    
    def _get_base_data(self, conn: sqlite3.Connection, start_date: str, end_date: str) -> pd.DataFrame:
        """获取计算所需的基础数据"""
        # 格式化日期
        start_date = start_date.replace('-', '')
        end_date = end_date.replace('-', '')
        
        # 获取基础因子数据
        query = """
        SELECT f.*, d.close, d.open, d.high, d.low, d.vol, d.amount, d.full_price,
               s.close as stock_close_daily, s.open as stock_open, s.high as stock_high, 
               s.low as stock_low, s.vol as stock_vol,
               cb_info.maturity_date, cb_info.stk_code
        FROM cb_factor f
        LEFT JOIN cb_daily d ON f.ts_code = d.ts_code AND f.trade_date = d.trade_date
        LEFT JOIN cb_basic cb_info ON f.ts_code = cb_info.ts_code
        LEFT JOIN stock_daily s ON cb_info.stk_code = s.ts_code AND f.trade_date = s.trade_date  
        WHERE f.trade_date >= ? AND f.trade_date <= ?
        AND d.close > 0
        ORDER BY f.trade_date, f.ts_code
        """
        
        df = pd.read_sql(query, conn, params=(start_date, end_date))
        
        if df.empty:
            return None
            
        # 确保trade_date为字符串格式，与其他计算器保持一致
        df['trade_date'] = df['trade_date'].astype(str)
        
        return df
    
    def _calculate_convertible_specific_factors(self, df: pd.DataFrame, conn: sqlite3.Connection) -> pd.DataFrame:
        """计算可转债特有因子"""
        
        # 1. 时间价值衰减因子 - 根据实际到期时间差异化计算
        if 'maturity_date' in df.columns:
            df['maturity_date'] = pd.to_datetime(df['maturity_date'], errors='coerce')
            trade_date_dt = pd.to_datetime(df['trade_date'], format='%Y%m%d', errors='coerce')
            df['time_to_maturity_years'] = (df['maturity_date'] - trade_date_dt).dt.days / 365.0
            
            # 使用分段函数提供更好的区分度
            decay_years = df['time_to_maturity_years'].fillna(3.0)
            
            # 分段衰减函数：不同到期时间段使用不同的衰减模式
            df['time_decay_factor'] = np.where(
                decay_years < 0.5,  # 半年内：急剧衰减
                0.1 + 0.4 * decay_years * 2,  # 0.1-0.5线性增长
                np.where(
                    decay_years < 2.0,  # 半年到2年：平缓衰减
                    0.5 + 0.3 * np.exp(-0.5 * (decay_years - 0.5)),
                    # 2年以上：稳定衰减
                    0.8 * np.exp(-0.2 * (decay_years - 2.0))
                )
            )
        else:
            # 当没有到期日数据时，使用债券代码推断发行时间，提供差异化的默认值
            df['time_to_maturity_years'] = 3.0  # 默认3年
            
            # 根据债券代码的数字部分估算剩余时间（简化逻辑）
            def estimate_maturity_from_code(ts_code):
                try:
                    # 提取债券代码的数字部分，假设较大的数字表示较新发行
                    code_num = int(ts_code.split('.')[0][-3:])  # 取后3位数字
                    # 简化映射：数字越大，剩余时间越长
                    if code_num > 800:
                        return 4.0 + (code_num - 800) / 200  # 4-5年
                    elif code_num > 500:
                        return 2.5 + (code_num - 500) / 300 * 1.5  # 2.5-4年
                    else:
                        return 1.0 + code_num / 500 * 1.5  # 1-2.5年
                except:
                    return 3.0  # 默认值
            
            df['time_to_maturity_years'] = df['ts_code'].apply(estimate_maturity_from_code)
            decay_years = df['time_to_maturity_years']
            
            # 使用与上面相同的分段衰减函数
            df['time_decay_factor'] = np.where(
                decay_years < 0.5,
                0.1 + 0.4 * decay_years * 2,
                np.where(
                    decay_years < 2.0,
                    0.5 + 0.3 * np.exp(-0.5 * (decay_years - 0.5)),
                    0.8 * np.exp(-0.2 * (decay_years - 2.0))
                )
            )
        
        # 2. 强制赎回压力因子 - 使用sigmoid函数提供更平滑的渐变
        # 在110元开始产生压力，130元达到高压力水平，避免大量零值
        # 使用sigmoid函数: f(x) = 1 / (1 + exp(-k * (x - center)))
        center_price = 120  # 中心点
        steepness = 0.2  # 控制曲线陡峭程度
        df['forced_call_pressure'] = 1 / (1 + np.exp(-steepness * (df['close'] - center_price)))
        # 对于低价债券，减少压力值
        df['forced_call_pressure'] = np.where(df['close'] < 110, 
                                             df['forced_call_pressure'] * 0.5, 
                                             df['forced_call_pressure'])
        
        # 3. 回售保护价值
        df['put_protection_value'] = np.where(df['close'] < 70, 
                                             (70 - df['close']) / df['close'], 0)
        
        # 4. 转股倾向度（基于转股溢价率和价格位置）
        if 'premium_ratio' in df.columns:
            premium_ratio = pd.to_numeric(df['premium_ratio'], errors='coerce').fillna(0)
        else:
            premium_ratio = 0
        
        # 结合价格位置和溢价率，使用更复杂的公式提供更多变化
        price_factor = df['close'] / 130.0  # 价格因子，130元为基准
        price_factor = np.clip(price_factor, 0.5, 1.5)  # 限制在合理范围内
        
        # 基础转股倾向度：溢价率越低，倾向度越高
        base_tendency = np.maximum(0, 1 - premium_ratio * 2)  # 50%溢价时降为0
        
        # 价格调整：高价债券转股倾向更强
        df['conversion_tendency'] = base_tendency * price_factor
        
        # 添加时间衰减影响：临近到期时转股倾向增强
        if 'time_to_maturity_years' in df.columns:
            time_factor = np.where(df['time_to_maturity_years'] < 0.5, 1.5,  # 半年内增强50%
                                 np.where(df['time_to_maturity_years'] < 1.0, 1.2,  # 一年内增强20%
                                         1.0))
            df['conversion_tendency'] *= time_factor
        
        # 5. 转股套利效率
        if 'volume_percentile' in df.columns:
            volume_pct = pd.to_numeric(df['volume_percentile'], errors='coerce').fillna(0.5)
        else:
            volume_pct = 0.5
        df['conversion_arbitrage_efficiency'] = np.maximum(0, -premium_ratio) * volume_pct
        
        # 6. 正股涨跌停影响因子
        if 'stock_close_daily' in df.columns and 'stock_open' in df.columns:
            stock_pct_change = (df['stock_close_daily'] - df['stock_open']) / df['stock_open'] * 100
            df['stock_limit_impact'] = np.where(stock_pct_change > 9.5, 1.0,
                                              np.where(stock_pct_change < -9.5, -1.0, 0.0))
        else:
            df['stock_limit_impact'] = 0.0
        
        return df
    
    def _calculate_market_structure_factors(self, df: pd.DataFrame, conn: sqlite3.Connection) -> pd.DataFrame:
        """计算市场结构因子"""
        
        # 按ts_code分组计算
        for ts_code, group in df.groupby('ts_code'):
            group = group.sort_values('trade_date')
            indices = group.index
            
            # 7. 资金流向因子（简化版，基于成交额变化）
            if 'amount' in group.columns:
                amount_ma5 = group['amount'].rolling(window=5, min_periods=1).mean()
                df.loc[indices, 'net_inflow_factor'] = (group['amount'] - amount_ma5) / amount_ma5.replace(0, 1)
            else:
                df.loc[indices, 'net_inflow_factor'] = 0.0
            
            # 8. 相对强弱因子（相对于整体市场）
            if 'momentum_5d' in group.columns:
                # 计算市场平均动量（简化为组内均值）
                market_momentum = group['momentum_5d'].rolling(window=20, min_periods=5).mean()
                df.loc[indices, 'relative_strength_vs_market'] = group['momentum_5d'] - market_momentum
            else:
                df.loc[indices, 'relative_strength_vs_market'] = 0.0
            
            # 9. 流动性冲击因子
            if 'vol' in group.columns:
                vol_ma20 = group['vol'].rolling(window=20, min_periods=5).mean()
                vol_std20 = group['vol'].rolling(window=20, min_periods=5).std()
                df.loc[indices, 'liquidity_shock'] = (group['vol'] - vol_ma20) / vol_std20.replace(0, 1)
            else:
                df.loc[indices, 'liquidity_shock'] = 0.0
        
        # 填充缺失值
        df['net_inflow_factor'] = df['net_inflow_factor'].fillna(0)
        df['relative_strength_vs_market'] = df['relative_strength_vs_market'].fillna(0)
        df['liquidity_shock'] = df['liquidity_shock'].fillna(0)
        
        return df
    
    def _calculate_cross_combination_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算交叉组合因子"""
        
        # 安全获取数值列，处理None值
        def safe_get_numeric(df, col_name, default_value):
            if col_name in df.columns:
                series = df[col_name].fillna(default_value)
                return pd.to_numeric(series, errors='coerce').fillna(default_value)
            else:
                return default_value
        
        # 10. 动量-波动率交叉因子
        momentum_5d = safe_get_numeric(df, 'momentum_5d', 0)
        volatility_5d = safe_get_numeric(df, 'volatility_5d', 0.01)  # 避免除零
        df['momentum_vol_cross'] = momentum_5d * (1 / np.maximum(volatility_5d, 0.001))
        
        # 11. 动量一致性因子
        momentum_10d = safe_get_numeric(df, 'momentum_10d', 0)
        df['momentum_consistency'] = momentum_5d * momentum_10d
        
        # 12. 价值-质量交叉因子
        premium_ratio = safe_get_numeric(df, 'premium_ratio', 0)
        price_position = safe_get_numeric(df, 'price_position', 0.5)
        df['value_quality_score'] = (-premium_ratio) * price_position
        
        # 13. 套利空间效率
        arbitrage_space = safe_get_numeric(df, 'arbitrage_space', 0)
        volatility_10d = safe_get_numeric(df, 'volatility_10d', 0.01)
        df['arbitrage_efficiency'] = arbitrage_space / np.maximum(volatility_10d, 0.001)
        
        # 14. 非线性变换因子
        df['premium_ratio_squared'] = premium_ratio ** 2
        
        # 15. 动量排名得分（横截面排名）
        if 'momentum_5d' in df.columns:
            df['momentum_rank_score'] = df.groupby('trade_date')['momentum_5d'].rank(pct=True)
        else:
            df['momentum_rank_score'] = 0.5
        
        # 16. 波动率状态因子
        if 'volatility_10d' in df.columns:
            df['volatility_regime'] = df.groupby('ts_code')['volatility_10d'].transform(
                lambda x: (x > x.rolling(window=60, min_periods=10).quantile(0.8)).astype(int)
            )
        else:
            df['volatility_regime'] = 0
        
        return df
    
    def _calculate_technical_pattern_factors(self, df: pd.DataFrame, conn: sqlite3.Connection) -> pd.DataFrame:
        """计算技术形态因子"""
        
        # 按ts_code分组计算技术指标
        for ts_code, group in df.groupby('ts_code'):
            group = group.sort_values('trade_date')
            indices = group.index
            
            if 'close' not in group.columns:
                continue
                
            # 17. 均线系统因子
            ma5 = group['close'].rolling(window=5, min_periods=1).mean()
            ma20 = group['close'].rolling(window=20, min_periods=5).mean()
            df.loc[indices, 'ma5_ma20_cross'] = (ma5 - ma20) / ma20.replace(0, 1)
            
            # 18. 均线斜率
            ma5_slope = ma5.pct_change(periods=5)
            df.loc[indices, 'ma_slope_5d'] = ma5_slope
            
            # 19. 支撑阻力因子（简化版）
            rolling_low = group['close'].rolling(window=20, min_periods=5).min()
            rolling_high = group['close'].rolling(window=20, min_periods=5).max()
            
            df.loc[indices, 'support_strength'] = (group['close'] - rolling_low) / rolling_low.replace(0, 1)
            df.loc[indices, 'resistance_pressure'] = (rolling_high - group['close']) / group['close'].replace(0, 1)
            
            # 20. 缺口因子
            if 'open' in group.columns:
                prev_close = group['close'].shift(1)
                gap_up = group['open'] > prev_close * 1.02
                gap_down = group['open'] < prev_close * 0.98
                df.loc[indices, 'gap_factor'] = np.where(gap_up, 1, np.where(gap_down, -1, 0))
            else:
                df.loc[indices, 'gap_factor'] = 0
            
            # 21. 量价背离因子
            if 'vol' in group.columns:
                price_change = group['close'].pct_change()
                volume_change = group['vol'].pct_change()
                df.loc[indices, 'price_volume_divergence'] = np.sign(price_change) - np.sign(volume_change)
            else:
                df.loc[indices, 'price_volume_divergence'] = 0
        
        # 填充技术因子的缺失值
        technical_factors = ['ma5_ma20_cross', 'ma_slope_5d', 'support_strength', 
                           'resistance_pressure', 'gap_factor', 'price_volume_divergence']
        
        for factor in technical_factors:
            if factor in df.columns:
                df[factor] = df[factor].fillna(0)
        
        return df
    
    def _postprocess_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """后处理：清理异常值，标准化等（保留作为UnifiedFeaturePreprocessor的fallback）"""
        
        # 定义需要后处理的因子
        factors_to_process = [
            'time_decay_factor', 'forced_call_pressure', 'put_protection_value',
            'conversion_tendency', 'conversion_arbitrage_efficiency', 'stock_limit_impact',
            'net_inflow_factor', 'relative_strength_vs_market', 'liquidity_shock',
            'momentum_vol_cross', 'momentum_consistency', 'value_quality_score',
            'arbitrage_efficiency', 'premium_ratio_squared', 'momentum_rank_score',
            'volatility_regime', 'ma5_ma20_cross', 'ma_slope_5d', 'support_strength',
            'resistance_pressure', 'gap_factor', 'price_volume_divergence'
        ]
        
        for factor in factors_to_process:
            if factor in df.columns:
                # 处理无穷值
                df[factor] = df[factor].replace([np.inf, -np.inf], np.nan)
                
                # 异常值处理 - 修复：使用更稳健的MAD方法，避免全样本统计量
                median_val = df[factor].median()
                mad_val = np.median(np.abs(df[factor] - median_val))
                if mad_val > 0:
                    # 使用5倍MAD作为异常值阈值，比3倍标准差更稳健
                    lower_bound = median_val - 5 * mad_val
                    upper_bound = median_val + 5 * mad_val
                    df[factor] = df[factor].clip(lower=lower_bound, upper=upper_bound)
                
                # 填充剩余缺失值
                df[factor] = df[factor].fillna(0)
        
        # 确保所有因子都是数值型
        for factor in factors_to_process:
            if factor in df.columns:
                df[factor] = pd.to_numeric(df[factor], errors='coerce').fillna(0)
        
        return df
    
    def get_factor_names(self) -> List[str]:
        """返回该计算器生成的所有因子名称"""
        return [
            # 可转债特有因子
            'time_decay_factor', 'forced_call_pressure', 'put_protection_value',
            'conversion_tendency', 'conversion_arbitrage_efficiency', 'stock_limit_impact',
            
            # 市场结构因子  
            'net_inflow_factor', 'relative_strength_vs_market', 'liquidity_shock',
            
            # 交叉组合因子
            'momentum_vol_cross', 'momentum_consistency', 'value_quality_score',
            'arbitrage_efficiency', 'premium_ratio_squared', 'momentum_rank_score',
            'volatility_regime',
            
            # 技术形态因子
            'ma5_ma20_cross', 'ma_slope_5d', 'support_strength',
            'resistance_pressure', 'gap_factor', 'price_volume_divergence'
        ]


if __name__ == "__main__":
    # 测试增强因子计算器
    calculator = EnhancedFactorCalculator()
    
    # 测试计算
    test_date = "2024-01-15"
    result = calculator.calculate_factors_for_date(test_date)
    
    if not result.empty:
        print(f"成功计算增强因子，共 {len(result)} 条记录")
        print("新增因子:", calculator.get_factor_names())
        print("\n因子样例:")
        for factor in calculator.get_factor_names()[:5]:  # 显示前5个因子
            if factor in result.columns:
                print(f"{factor}: {result[factor].describe()}")
    else:
        print("未能计算增强因子")