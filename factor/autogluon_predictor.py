"""
基于AutoGluon的机器学习预测器
实现多种模型的自动化训练和预测，用于可转债因子预测
"""

import pandas as pd
import numpy as np
import sqlite3
import pickle
import json
from datetime import datetime, timedelta
from pathlib import Path
import warnings

from autogluon.core.metrics import make_scorer

warnings.filterwarnings('ignore')

# 导入sklearn metrics，无论是否有AutoGluon都需要
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# 导入基类和共享工具
from .base_predictor import (
    BasePredictor, top_mean_squarer, calculate_cross_sectional_r2,
    _split_data_by_bond, get_factor_config_by_name, UNIFIED_PREPROCESSING,
    calculate_sample_weights, add_sample_weights_to_dataframe, config_manager
)

try:
    from autogluon.tabular import TabularDataset, TabularPredictor
    AUTOGLUON_AVAILABLE = True
except ImportError:
    print("警告: AutoGluon未安装，将使用简化的机器学习模型")
    AUTOGLUON_AVAILABLE = False

class AutoGluonFactorPredictor(BasePredictor):
    """
    基于AutoGluon的因子预测器
    
    支持多种机器学习模型的自动化训练和预测
    """
    
    def __init__(self, model_name='default', target_days=1, feature_config=None,
                 preprocessing_config=None, create_unified_preprocessor_now=True, use_sample_weights=False, sorted_feature=False):
        """
        初始化AutoGluon预测器

        Args:
            model_name: 模型名称
            target_days: 预测天数（未来n天的收益率）
            feature_config: 特征配置字典
            preprocessing_config: 预处理配置 ('default', 'conservative', 'aggressive', 'time_series')
            create_unified_preprocessor_now: 是否立即创建预处理器（加载已有模型时应设为False）
            use_sample_weights: 是否使用样本权重训练
            sorted_feature: 是否使用排序特征（将每个特征转换为时间维度和横截面维度的排序分位数）
        """
        # 调用父类初始化
        preprocessing_config = preprocessing_config or 'autogluon_friendly'  # 默认使用autogluon_friendly
        super().__init__(
            model_name=model_name,
            target_days=target_days,
            feature_config=feature_config,
            preprocessing_config=preprocessing_config,
            create_unified_preprocessor_now=create_unified_preprocessor_now,
            use_sample_weights=use_sample_weights,
            sorted_feature=sorted_feature
        )

        # AutoGluon特有的属性
        self.predictor = None
        self.unified_preprocessor = self.preprocessor  # 保持兼容性

        # 备用：完整特征配置（包含所有特征，供对比使用）
        self.full_feature_config = {
            'zl_factors': ['arbitrage_space', 'model_delta', 'corr_coef', 'reg_slope', 'bond_floor', 'option_value'],
            'momentum_factors': ['momentum_5d', 'momentum_10d', 'momentum_20d'],
            'double_low_factors': ['price_factor', 'premium_factor', 'premium_ratio', 'double_low_score',
                                 'double_low_rank', 'double_low_percentile'],
            'technical_factors': [
                'bb_position', 'bb_width', 'bb_deviation',
                'volatility_5d', 'volatility_10d', 'volatility_20d', 'atr_14d', 'relative_volatility',
                'rsi_14d', 'williams_r',
                'trend_slope_5d', 'trend_slope_10d', 'trend_slope_20d', 'trend_consistency',
                'price_percentile', 'price_position', 'drawdown_from_high', 'gain_from_low',
                'volume_percentile', 'amount_percentile', 'volume_change_5d', 'volume_change_10d', 'volume_change_20d',
                'volume_trend', 'avg_turnover_20d', 'turnover_cv', 'amihud_illiquidity', 'obv_trend'
            ],
            'additional_factors': [
                'conv_value', 'macd',
                'cb_return', 'stock_return', 'predicted_return', 'expected_return'
            ],
            'enhanced_factors_full': [
                # 完整的增强因子列表
                'time_decay_factor', 'forced_call_pressure', 'put_protection_value',
                'conversion_tendency', 'conversion_arbitrage_efficiency', 'stock_limit_impact',
                'net_inflow_factor', 'relative_strength_vs_market', 'liquidity_shock',
                'momentum_vol_cross', 'momentum_consistency', 'value_quality_score',
                'arbitrage_efficiency', 'premium_ratio_squared', 'momentum_rank_score',
                'volatility_regime', 'ma5_ma20_cross', 'ma_slope_5d', 'support_strength',
                'resistance_pressure', 'gap_factor', 'price_volume_divergence'
            ]
        }

        # 备选模型（当AutoGluon不可用时）
        if not AUTOGLUON_AVAILABLE:
            self.sklearn_model = None

        if self.preprocessing_config == 'default':
            print("💡 提示：如果训练时处理缺失值很慢，可以尝试使用 'fast_training' 配置")

        print(f"AutoGluon因子预测器初始化 - 模型: {model_name}, 预测天数: {target_days}")
        print(f"AutoGluon可用: {AUTOGLUON_AVAILABLE}")

    def _get_models_dir(self) -> Path:
        """获取AutoGluon模型保存目录"""
        return Path('models/autogluon')


    
    def train_model(self, train_data, time_limit=300, presets='medium_quality', tuning_data=None):
        """
        训练模型
        
        Args:
            train_data: 训练数据DataFrame
            time_limit: 训练时间限制（秒）
            presets: 模型预设 ('medium_quality', 'high_quality', 'best_quality')
            tuning_data: 调优数据DataFrame（可选）
            
        Returns:
            训练结果字典
        """
        if train_data is None or train_data.empty:
            print("没有训练数据")
            return None
        
        print(f"开始训练模型...")
        
        if AUTOGLUON_AVAILABLE:
            return self._train_autogluon_model(train_data, time_limit, presets, tuning_data)
        else:
            return self._train_sklearn_model(train_data)
    
    def _train_autogluon_model(self, train_data, time_limit, presets, tuning_data=None):
        """使用AutoGluon训练模型"""
        try:
            # 准备训练数据
            required_columns = self.feature_names + [self.target_name]
            
            # 如果使用样本权重，需要添加权重列
            sample_weight_col = None
            if self.use_sample_weights and calculate_sample_weights is not None:
                print("正在为AutoGluon训练数据计算样本权重...")
                # 确保有必要的列用于权重计算
                if 'trade_date' in train_data.columns and 'ts_code' in train_data.columns:
                    # 添加样本权重列
                    train_data_with_weights = add_sample_weights_to_dataframe(
                        train_data, self.target_name, 'trade_date', 'sample_weight'
                    )
                    sample_weight_col = 'sample_weight'
                    required_columns.append(sample_weight_col)
                    train_features = train_data_with_weights[required_columns].copy()
                    print(f"已添加样本权重列: {sample_weight_col}")
                else:
                    print("警告: 缺少trade_date或ts_code列，无法计算样本权重，将不使用权重训练")
                    train_features = train_data[required_columns].copy()
            else:
                train_features = train_data[required_columns].copy()
                if self.use_sample_weights:
                    print("警告: 样本权重计算工具不可用，将不使用权重训练")
            
            # 删除包含任何NaN值的行
            before_count = len(train_features)
            train_features = train_features.dropna()
            after_count = len(train_features)
            
            if before_count != after_count:
                print(f"清理训练数据: {before_count} -> {after_count} (删除 {before_count - after_count} 个包含NaN的行)")
            
            train_dataset = TabularDataset(train_features)
            metrics = make_scorer("mean_squared_error", top_mean_squarer, optimum=0, greater_is_better=False)
            # metrics = 'root_mean_squared_error'
            # 准备调优数据
            tuning_dataset = None
            if tuning_data is not None and not tuning_data.empty:
                tuning_required_columns = self.feature_names + [self.target_name]
                
                # 如果使用样本权重，也为调优数据添加权重
                if sample_weight_col is not None:
                    if 'trade_date' in tuning_data.columns and 'ts_code' in tuning_data.columns:
                        tuning_data_with_weights = add_sample_weights_to_dataframe(
                            tuning_data, self.target_name, 'trade_date', 'sample_weight'
                        )
                        tuning_required_columns.append(sample_weight_col)
                        tuning_features = tuning_data_with_weights[tuning_required_columns].copy()
                    else:
                        print("警告: 调优数据缺少trade_date或ts_code列，调优数据将不使用权重")
                        tuning_features = tuning_data[tuning_required_columns].copy()
                else:
                    tuning_features = tuning_data[tuning_required_columns].copy()
                
                # 删除包含任何NaN值的行
                before_count = len(tuning_features)
                tuning_features = tuning_features.dropna()
                after_count = len(tuning_features)
                
                if before_count != after_count:
                    print(f"清理调优数据: {before_count} -> {after_count} (删除 {before_count - after_count} 个包含NaN的行)")
                
                tuning_dataset = TabularDataset(tuning_features)
                print(f"使用tuning数据: {len(tuning_dataset)} 条记录")
            else:
                print("未使用tuning数据")
            
            # 配置模型路径
            model_path = self.models_dir / f"{self.model_name}_autogluon"
            
            # 创建预测器
            predictor_kwargs = {
                'label': self.target_name,
                'path': str(model_path),
                'problem_type': 'regression',
                'eval_metric': 'root_mean_squared_error'
            }
            
            # 如果使用样本权重，添加sample_weight参数
            if sample_weight_col is not None:
                predictor_kwargs['sample_weight'] = sample_weight_col
                predictor_kwargs['weight_evaluation'] = True
                print(f"AutoGluon将使用样本权重列: {sample_weight_col}")

            predictor_kwargs['eval_metric'] = metrics
            self.predictor = TabularPredictor(**predictor_kwargs)
            
            # 获取hyperparameters配置
            hyperparameters = self._get_hyperparameters()
            good_quality = presets not in ['medium_quality']
            # 训练模型
            self.predictor.fit(
                train_dataset,
                tuning_data=tuning_dataset,  # 提供tuning数据
                num_stack_levels=0 if len(hyperparameters) > 1 and good_quality else 0,
                use_bag_holdout= presets not in ['medium_quality'],
                dynamic_stacking=False,
                num_bag_folds=5 if good_quality else 0,
                time_limit=time_limit,
                presets=presets,
                hyperparameters=hyperparameters,
                verbosity=2
            )
            
            # 获取训练结果
            results = self.predictor.fit_summary()
            
            # 评估模型
            train_predictions = self.predictor.predict(train_dataset)
            train_actual = train_dataset[self.target_name]
            
            train_mse = mean_squared_error(train_actual, train_predictions)
            
            # 计算横截面R²：从原始train_data获取trade_date信息
            if 'trade_date' in train_data.columns:
                # 使用train_features的索引从原始数据中获取trade_date
                train_eval_data = pd.DataFrame({
                    'trade_date': train_data.loc[train_features.index, 'trade_date'],
                    'y_true': train_actual,
                    'y_pred': train_predictions
                })
                train_r2 = calculate_cross_sectional_r2(train_eval_data, 'y_true', 'y_pred', 'trade_date')
            else:
                # 如果没有trade_date列，使用传统R²计算
                train_r2 = r2_score(train_actual, train_predictions)
                print("警告: 训练数据缺少trade_date列，使用传统R²计算")
            
            self.training_results = {
                'train_mse': train_mse,
                'train_rmse': np.sqrt(train_mse),
                'train_r2': train_r2,
                'model_summary': results,
                'feature_importance': self._get_feature_importance(tuning_dataset),
                'train_samples': len(train_dataset)
            }
            
            # 如果有tuning数据，也进行评估
            if tuning_dataset is not None:
                tuning_predictions = self.predictor.predict(tuning_dataset)
                tuning_actual = tuning_dataset[self.target_name]
                
                tuning_mse = mean_squared_error(tuning_actual, tuning_predictions)
                
                # 计算tuning数据的横截面R²：从原始tuning_data获取trade_date信息
                if 'trade_date' in tuning_data.columns:
                    # 使用tuning_features的索引从原始数据中获取trade_date
                    tuning_eval_data = pd.DataFrame({
                        'trade_date': tuning_data.loc[tuning_features.index, 'trade_date'],
                        'y_true': tuning_actual,
                        'y_pred': tuning_predictions
                    })
                    tuning_r2 = calculate_cross_sectional_r2(tuning_eval_data, 'y_true', 'y_pred', 'trade_date')
                else:
                    # 如果没有trade_date列，使用传统R²计算
                    tuning_r2 = r2_score(tuning_actual, tuning_predictions)
                    print("警告: tuning数据缺少trade_date列，使用传统R²计算")
                
                self.training_results.update({
                    'tuning_mse': tuning_mse,
                    'tuning_rmse': np.sqrt(tuning_mse),
                    'tuning_r2': tuning_r2,
                    'tuning_samples': len(tuning_dataset)
                })
                
                print(f"AutoGluon模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_dataset)}")
                print(f"  调优集 - RMSE: {np.sqrt(tuning_mse):.6f}, R²: {tuning_r2:.4f}, 样本数: {len(tuning_dataset)}")
            else:
                print(f"AutoGluon模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_dataset)}")
            
            # 保存模型元数据
            self._save_model_metadata()
            
            return self.training_results
            
        except Exception as e:
            print(f"AutoGluon模型训练失败: {e}")
            return None
    
    def _train_sklearn_model(self, train_data):
        """使用sklearn训练备选模型"""
        try:
            # 准备数据
            X = train_data[self.feature_names].values
            y = train_data[self.target_name].values
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 分割数据，保留索引以便后续重建trade_date信息
            X_train, X_test, y_train, y_test, idx_train, idx_test = train_test_split(
                X_scaled, y, train_data.index, test_size=0.2, random_state=42
            )
            
            # 使用随机森林作为默认模型
            self.sklearn_model = RandomForestRegressor(
                n_estimators=100,
                random_state=42,
                n_jobs=-1
            )
            
            # 训练模型
            self.sklearn_model.fit(X_train, y_train)
            
            # 评估模型
            y_pred_train = self.sklearn_model.predict(X_train)
            y_pred_test = self.sklearn_model.predict(X_test)
            
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            
            # 计算横截面R²
            if 'trade_date' in train_data.columns:
                # 重建训练集数据用于横截面R²计算
                train_eval_data = train_data.loc[idx_train, ['trade_date']].copy()
                train_eval_data['y_true'] = y_train
                train_eval_data['y_pred'] = y_pred_train
                train_r2 = calculate_cross_sectional_r2(train_eval_data, 'y_true', 'y_pred', 'trade_date')
                
                # 重建测试集数据用于横截面R²计算
                test_eval_data = train_data.loc[idx_test, ['trade_date']].copy()
                test_eval_data['y_true'] = y_test
                test_eval_data['y_pred'] = y_pred_test
                test_r2 = calculate_cross_sectional_r2(test_eval_data, 'y_true', 'y_pred', 'trade_date')
            else:
                # 如果没有trade_date列，使用传统R²计算
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)
                print("警告: sklearn训练数据缺少trade_date列，使用传统R²计算")
            
            self.training_results = {
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_rmse': np.sqrt(train_mse),
                'test_rmse': np.sqrt(test_mse),
                'train_r2': train_r2,
                'test_r2': test_r2,
                'feature_importance': dict(zip(self.feature_names, self.sklearn_model.feature_importances_)),
                'n_samples': len(X)
            }
            
            print(f"sklearn模型训练完成:")
            print(f"  训练RMSE: {np.sqrt(train_mse):.6f}")
            print(f"  测试RMSE: {np.sqrt(test_mse):.6f}")
            print(f"  训练R²: {train_r2:.4f}")
            print(f"  测试R²: {test_r2:.4f}")
            
            # 保存模型
            self._save_sklearn_model()
            
            return self.training_results
            
        except Exception as e:
            print(f"sklearn模型训练失败: {e}")
            return None
    
    def predict(self, factor_data):
        """
        使用训练好的模型进行预测
        
        Args:
            factor_data: 包含因子数据的DataFrame
            
        Returns:
            预测结果数组
        """
        if factor_data is None or factor_data.empty:
            return None
        
        # 应用与训练时相同的预处理
        processed_data = factor_data.copy()
        if self.preprocessor is not None and UNIFIED_PREPROCESSING:
            try:
                # 识别基础特征（非工程特征和非排序特征）
                engineered_suffixes = ['_x_', '_div_', '_squared', '_lag1', '_ma3']
                rank_suffixes = ['_time_rank', '_cross_rank']
                
                # 如果使用了排序特征，需要从排序特征名中提取基础特征名
                if self.sorted_feature:
                    base_features = []
                    for f in self.feature_names:
                        # 从排序特征名中提取基础特征名
                        if '_time_rank' in f:
                            base_feature = f.replace('_time_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)
                        elif '_cross_rank' in f:
                            base_feature = f.replace('_cross_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)
                    
                    # 进一步提取更基础的特征（去除工程特征后缀）
                    core_base_features = []
                    for feature in base_features:
                        # 去除工程特征后缀，找到核心基础特征
                        is_engineered = any(suffix in feature for suffix in engineered_suffixes)
                        if not is_engineered:
                            core_base_features.append(feature)
                        else:
                            # 如果是工程特征，提取其基础特征
                            for suffix in engineered_suffixes:
                                if suffix in feature:
                                    if suffix == '_x_':
                                        # 交互特征：feature1_x_feature2，提取两个基础特征
                                        parts = feature.split('_x_')
                                        for part in parts:
                                            if part and part not in core_base_features:
                                                core_base_features.append(part)
                                    else:
                                        # 其他工程特征：feature_suffix，提取基础特征
                                        core_feature = feature.split(suffix)[0]
                                        if core_feature and core_feature not in core_base_features:
                                            core_base_features.append(core_feature)
                                    break
                    
                    available_base_features = [f for f in core_base_features if f in processed_data.columns]
                else:
                    # 普通模式，识别基础特征（非工程特征）
                    base_features = [f for f in self.feature_names 
                                   if not any(suffix in f for suffix in engineered_suffixes)]
                    available_base_features = [f for f in base_features if f in processed_data.columns]
                
                # 使用预处理器处理基础特征并重建工程特征
                processed_data = self.preprocessor.preprocess_features(
                    processed_data,
                    available_base_features,  # 只传入可用的核心基础特征
                    is_training=False  # 使用训练时拟合的参数
                )
                # 预处理完成（日志已在预处理器中显示）
            except Exception as e:
                print(f"⚠️  推理预处理失败: {e}，使用原始数据")
                processed_data = factor_data.copy()
        
        # 2. 排序特征转换（如果模型使用了排序特征）
        if self.sorted_feature:
            try:
                print(f"🔄 应用排序特征转换（推理模式）...")
                # 确保数据包含必要的列
                if 'ts_code' not in processed_data.columns or 'trade_date' not in processed_data.columns:
                    print(f"⚠️  排序特征转换需要ts_code和trade_date列，当前列: {processed_data.columns.tolist()}")
                    if 'ts_code' not in processed_data.columns:
                        processed_data['ts_code'] = 'unknown'  # 提供默认值
                    if 'trade_date' not in processed_data.columns:
                        processed_data['trade_date'] = '20240101'  # 提供默认值
                
                # 获取工程特征后的特征列表（用于排序转换）
                available_features = [col for col in processed_data.columns 
                                    if col not in ['ts_code', 'trade_date', self.target_name]]
                
                # 应用排序特征转换（使用可用特征而不是self.feature_names中的排序特征）
                processed_data = self._apply_sorted_feature_transformation_for_prediction(processed_data, available_features)
                
                # 检查排序特征转换后是否还有数据
                if processed_data.empty:
                    print(f"⚠️  排序特征转换后数据为空，无法进行预测")
                    return None
                    
                print(f"排序特征转换完成（推理模式）: {len(processed_data)} 条记录，{len(self.feature_names)} 个特征")
            except Exception as e:
                print(f"⚠️  排序特征转换失败: {e}")
                return None
        
        # 检查特征是否存在（在预处理后）
        missing_features = [f for f in self.feature_names if f not in processed_data.columns]
        if missing_features:
            print(f"缺少特征: {missing_features[:10]}{'...' if len(missing_features) > 10 else ''} (共{len(missing_features)}个)")
            
            # 尝试创建缺失的工程特征
            if self.preprocessor is not None and hasattr(self.preprocessor, 'trained_engineered_features'):
                missing_engineered = [f for f in missing_features if any(suffix in f for suffix in ['_x_', '_div_', '_squared', '_lag1', '_ma3'])]
                if missing_engineered:
                    print(f"⚠️  检测到缺失工程特征 {len(missing_engineered)} 个，尝试重建...")
                    try:
                        # 调用预处理器的特征工程功能来重建缺失特征
                        for feature in missing_engineered:
                            if feature not in processed_data.columns:
                                processed_data[feature] = 0  # 默认填充0
                        print(f"✅ 已用默认值填充缺失的工程特征")
                    except Exception as e:
                        print(f"⚠️  重建工程特征失败: {e}")
                
                # 再次检查
                missing_features = [f for f in self.feature_names if f not in processed_data.columns]
                if missing_features:
                    print(f"仍然缺少特征: {missing_features[:5]}{'...' if len(missing_features) > 5 else ''}")
                    return None
            else:
                return None
        
        if AUTOGLUON_AVAILABLE and self.predictor is not None:
            return self._predict_autogluon(processed_data)
        elif self.sklearn_model is not None:
            return self._predict_sklearn(processed_data)
        else:
            print("没有可用的训练模型")
            return None
    
    def _predict_autogluon(self, factor_data):
        """使用AutoGluon模型预测"""
        try:
            # 准备预测数据
            predict_data = factor_data[self.feature_names].copy()
            
            # 预测
            predictions = self.predictor.predict(predict_data)
            
            return predictions.values if hasattr(predictions, 'values') else predictions
            
        except Exception as e:
            print(f"AutoGluon预测失败: {e}")
            return None
    
    def _predict_sklearn(self, factor_data):
        """使用sklearn模型预测"""
        try:
            # 准备预测数据
            X = factor_data[self.feature_names].values
            
            # 标准化
            X_scaled = self.scaler.transform(X)
            
            # 预测
            predictions = self.sklearn_model.predict(X_scaled)
            
            return predictions
            
        except Exception as e:
            print(f"sklearn预测失败: {e}")
            return None
    
    def _get_feature_importance(self, tuning_dataset):
        """获取特征重要性（AutoGluon）"""
        try:
            if self.predictor is not None:
                importance = self.predictor.feature_importance(data=tuning_dataset)
                if hasattr(importance, 'to_dict'):
                    # 如果是DataFrame，转换为字典
                    return importance.to_dict()
                elif isinstance(importance, dict):
                    # 如果已经是字典，直接返回
                    return importance
                else:
                    # 其他情况返回空字典
                    return {}
            return {}
        except Exception as e:
            print(f"获取特征重要性失败: {e}")
            return {}
    
    def _save_model_metadata(self):
        """保存模型元数据"""
        try:
            # 处理训练结果，确保JSON可序列化
            serializable_results = {}
            for k, v in self.training_results.items():
                try:
                    if isinstance(v, (pd.DataFrame, np.ndarray)):
                        # 跳过DataFrame和numpy数组
                        print(f"跳过非序列化类型 {k}: {type(v)}")
                        continue
                    elif isinstance(v, dict):
                        # 处理字典类型（如feature_importance）
                        try:
                            # 检查字典中的值类型
                            clean_dict = {}
                            for key, val in v.items():
                                if isinstance(val, (pd.DataFrame, pd.Series, np.ndarray)):
                                    print(f"跳过字典中的非序列化值 {k}.{key}: {type(val)}")
                                    continue
                                elif isinstance(val, (np.integer, np.floating)):
                                    clean_dict[str(key)] = float(val)
                                else:
                                    clean_dict[str(key)] = val
                            serializable_results[k] = clean_dict
                        except Exception as e:
                            print(f"处理字典 {k} 失败: {e}")
                            continue
                    elif isinstance(v, (np.integer, np.floating)):
                        # 转换numpy数值类型
                        serializable_results[k] = float(v)
                    else:
                        # 其他类型，尝试JSON序列化测试
                        try:
                            json.dumps(v)  # 测试是否可序列化
                            serializable_results[k] = v
                        except:
                            print(f"跳过不可序列化的值 {k}: {type(v)}")
                            continue
                except Exception as e:
                    print(f"处理训练结果 {k} 时出错: {e}")
                    continue

            # 保存预处理器状态
            if self.preprocessor is not None and UNIFIED_PREPROCESSING:
                import pickle
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                with open(preprocessor_path, 'wb') as f:
                    pickle.dump(self.preprocessor, f)
                print(f"预处理器状态已保存: {preprocessor_path}")

            metadata = {
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name,
                'feature_names': self.feature_names,
                'feature_config': self.feature_config,
                'training_results': serializable_results,
                'autogluon_available': AUTOGLUON_AVAILABLE,
                'preprocessing_config': self.preprocessing_config,
                'sorted_feature': self.sorted_feature,  # 保存排序特征参数
                'save_time': datetime.now().isoformat()
            }

            metadata_path = self.models_dir / f"{self.model_name}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            print(f"模型元数据已保存: {metadata_path}")

        except Exception as e:
            print(f"保存模型元数据失败: {e}")
            # 即使元数据保存失败，也不应该影响模型训练的成功
    
    def _save_sklearn_model(self):
        """保存sklearn模型"""
        if self.sklearn_model is not None:
            model_data = {
                'model': self.sklearn_model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'training_results': self.training_results,
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name
            }
            
            model_path = self.models_dir / f"{self.model_name}_sklearn.pkl"
            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)
    
    def load_model(self, model_name=None):
        """
        加载已训练的模型
        
        Args:
            model_name: 模型名称，如果为None则使用当前模型名称
        """
        if model_name:
            self.model_name = model_name
        
        if AUTOGLUON_AVAILABLE:
            return self._load_autogluon_model()
        else:
            return self._load_sklearn_model()
    
    def _load_autogluon_model(self):
        """加载AutoGluon模型"""
        try:
            model_path = self.models_dir / f"{self.model_name}_autogluon"
            
            if not model_path.exists():
                print(f"AutoGluon模型不存在: {model_path}")
                return False
            
            # 加载模型，允许Python版本不匹配
            self.predictor = TabularPredictor.load(str(model_path), require_py_version_match=False)
            
            # 加载元数据
            metadata_path = self.models_dir / f"{self.model_name}_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                self.feature_names = metadata.get('feature_names', [])
                self.target_days = metadata.get('target_days', 1)
                self.target_name = metadata.get('target_name', 'return_1d')
                self.training_results = metadata.get('training_results', {})
                self.preprocessing_config = metadata.get('preprocessing_config', 'default')
                self.sorted_feature = metadata.get('sorted_feature', False)  # 加载排序特征参数
            
            # 加载预处理器状态（优先使用保存的状态）
            if UNIFIED_PREPROCESSING:
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                if preprocessor_path.exists():
                    import pickle
                    with open(preprocessor_path, 'rb') as f:
                        self.preprocessor = pickle.load(f)
                    print(f"✅ 预处理器状态已加载: {preprocessor_path}")
                    
                    # 获取实际配置并进行一致性检查
                    if hasattr(self.preprocessor, 'get_actual_config'):
                        actual_config = self.preprocessor.get_actual_config()
                        print(f"  实际使用的预处理配置:")
                        print(f"    缺失值策略: {actual_config['missing_strategy']}")
                        print(f"    缩放方法: {actual_config['scaling_method']}")
                        if actual_config['missing_strategy'] != actual_config.get('original_missing_strategy'):
                            print(f"    注意：缺失值策略在训练时从 {actual_config['original_missing_strategy']} 调整为 {actual_config['missing_strategy']}")
                        if actual_config.get('trained_engineered_features'):
                            print(f"    特征工程: {len(actual_config['trained_engineered_features'])}个工程特征")
                    
                    # 进行一致性检查
                    if hasattr(self.preprocessor, 'ensure_consistency'):
                        self.preprocessor.ensure_consistency()
                    else:
                        # 兼容旧版本的预处理器
                        if hasattr(self.preprocessor, 'missing_strategy'):
                            actual_strategy = self.preprocessor.missing_strategy
                            actual_scaling = self.preprocessor.scaling_method
                            print(f"  实际使用的预处理配置: 缺失值={actual_strategy}, 缩放={actual_scaling}")
                else:
                    print(f"⚠️  未找到预处理器状态文件: {preprocessor_path}")
                    # 使用元数据中保存的配置重新创建预处理器
                    from .unified_feature_preprocessor import create_unified_preprocessor
                    config_to_use = self.preprocessing_config  # 使用从元数据加载的配置
                    self.preprocessor = create_unified_preprocessor(config_to_use)
                    print(f"⚠️  使用元数据配置重新创建预处理器: {config_to_use}")
                    print(f"⚠️  注意：这个预处理器是未训练的，可能导致数据分布不匹配")
            
            print(f"AutoGluon模型加载成功: {self.model_name}")
            print(f"  排序特征模式: {'启用' if self.sorted_feature else '禁用'}")
            if self.sorted_feature:
                print(f"  模型特征类型: 排序分位数特征")
            else:
                print(f"  模型特征类型: 原始特征")
            return True
            
        except Exception as e:
            print(f"加载AutoGluon模型失败: {e}")
            return False
    
    def _load_sklearn_model(self):
        """加载sklearn模型"""
        try:
            model_path = self.models_dir / f"{self.model_name}_sklearn.pkl"
            
            if not model_path.exists():
                print(f"sklearn模型不存在: {model_path}")
                return False
            
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.sklearn_model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.training_results = model_data['training_results']
            self.target_days = model_data.get('target_days', 1)
            self.target_name = model_data.get('target_name', 'return_1d')
            
            print(f"sklearn模型加载成功: {self.model_name}")
            return True
            
        except Exception as e:
            print(f"加载sklearn模型失败: {e}")
            return False
    

    
    def list_available_models(self):
        """列出所有可用的模型"""
        autogluon_models = list(self.models_dir.glob("*_autogluon"))
        sklearn_models = list(self.models_dir.glob("*_sklearn.pkl"))
        
        print(f"\n可用模型列表:")
        print("-" * 60)
        
        if autogluon_models:
            print("AutoGluon模型:")
            for model_path in autogluon_models:
                model_name = model_path.name.replace('_autogluon', '')
                print(f"  - {model_name}")
        
        if sklearn_models:
            print("sklearn模型:")
            for model_path in sklearn_models:
                model_name = model_path.name.replace('_sklearn.pkl', '')
                print(f"  - {model_name}")
        
    def _get_hyperparameters(self):
        """
        获取优化的hyperparameters配置，参考ai_retry/util.py
        
        Returns:
            dict: hyperparameters配置字典
        """
        # 基于ai_retry/util.py的ALL_HYPERPARAMETERS进行优化配置
        hyperparameters = {
            # 'FT_TRANSFORMER': {
            #     # 数据预处理
            #     'data.categorical.convert_to_text': True,
            #     # 训练环境
            #     'env.batch_size': 256,  # 提高批次规模，加速训练
            #     'env.per_gpu_batch_size': 128,  # 若多 GPU，可调高此值
            #     'env.num_workers': 4,  # 数据加载并行
            #     # 优化器设置
            #     'optim.max_epochs': 100,  # 设置一个合理上限
            #     'optim.weight_decay': 1e-4,  # 防止过拟合
            #     'optim.lr_choice': 1e-3,  # 固定学习率
            #     'optim.warmup_steps': 0.1,  # 10% 步骤预热
            #     'optim.patience': 10,  # 早停耐心
            #     'optim.top_k': 2,  # 选取前 2 次最优模型做集成
            # },
            'GBM': [
                {
                    'seed': 0,
                    'bagging_seed': 0,
                    'feature_fraction_seed': 0,
                    'data_random_seed': 0,
                    'feature_fraction': 0.9,
                    "learning_rate": 0.03,
                    "num_leaves": 128,
                    "min_data_in_leaf": 5,
                    'ag_args': dict(model_type="GBM", name_suffix="Large", priority=0),
                },
                {
                    "extra_trees": True,
                    'seed': 0,
                    'bagging_seed': 0,
                    'feature_fraction_seed': 0,
                    'drop_seed': 0,
                    'extra_seed': 0,
                    'objective_seed': 0,
                    'data_random_seed': 0,
                    'bagging_fraction': 1.0,
                    'feature_fraction': 1.0,
                    'bagging_freq': 0,
                    "ag_args": {"name_suffix": "XT", "priority": 0},
                    'early_stopping_round' : 100,
                    'force_col_wise': True,
                },
                {
                    'seed': 0,
                    'bagging_seed': 0,
                    'feature_fraction_seed': 0,
                    'data_random_seed': 0,
                },
            ],

            'CAT': {
                'depth': 8, 'learning_rate': 0.03,
                'l2_leaf_reg': 3.0, 'rsm': 0.9,
                'loss_function': 'RMSE',
            },

            # 'XGB': {
            #     'n_estimators': 2500,
            #     'learning_rate': 0.03,
            #     'max_depth': 6,
            #     'subsample': 0.8,
            #     'colsample_bytree': 0.8,
            #     'reg_alpha': 0.1,
            #     'reg_lambda': 1.0,
            #     'min_child_weight': 5,
            #     # 解决收益率±对称薄尾问题时，Huber 损失更稳
            #     'objective': 'reg:squarederror',
            # },
        }
        
        print(f"✅ 使用优化的hyperparameters配置")
        print(f"  包含模型: {list(hyperparameters.keys())}")
        
        return hyperparameters

def _split_data_by_bond(
        data: pd.DataFrame,
        tuning_ratio: float = 0.1,
        gap_days: int = 0,
        date_col: str = 'trade_date'
    ):
    """
    按时间将数据拆分为 (train_data, tuning_data)。

    参数
    ----
    data : DataFrame
        全量样本，含特征和标签列。
    tuning_ratio : float, default 0.1
        验证集占全部**日期段**的比例（例如 0.1 表示最近 10% 日期作为验证）。
    gap_days : int, default 0
        训练集末尾与验证集开头之间留多少天净空期，防信息渗漏。
    date_col : str, default 'trade_date'
        日期列名，允许字符串 'YYYYMMDD' / 'YYYY-MM-DD'。

    返回
    ----
    (train_data, tuning_data)
    """
    if date_col not in data.columns:
        raise KeyError(f"找不到日期列 {date_col}")

    # 1️⃣ 统一日期格式
    df = data.copy()
    df[date_col] = pd.to_datetime(df[date_col].astype(str), format='%Y%m%d', errors='coerce')
    if df[date_col].isna().any():
        raise ValueError(f"{date_col} 存在无法解析的日期")

    # 2️⃣ 计算验证集起点（最近 tuning_ratio% 的日期区间）
    uniq_dates = df[date_col].sort_values().unique()
    if len(uniq_dates) < 2:
        raise ValueError("数据日期种类太少，无法切分")

    # pivot_idx 至少为 1，确保有验证集
    pivot_idx = max(1, int(len(uniq_dates) * (1 - tuning_ratio)))
    val_start = uniq_dates[pivot_idx]

    # 3️⃣ 留空期（可选）
    if gap_days:
        train_end = val_start - timedelta(days=gap_days)
    else:
        train_end = val_start

    # 4️⃣ 切分
    train_data = df[df[date_col] < train_end].copy()
    tuning_data = df[df[date_col] >= val_start].copy()

    # 5️⃣ 打印信息（可注释掉）
    print(f"🔹 时间切分完成 (tuning_ratio={tuning_ratio:.2%})")
    print(f"   训练集: {train_data[date_col].min().date()} — {train_data[date_col].max().date()} | {len(train_data):,} 条")
    print(f"   验证集: {tuning_data[date_col].min().date()} — {tuning_data[date_col].max().date()} | {len(tuning_data):,} 条")
    if gap_days:
        print(f"   已留空 {gap_days} 天净空期")

    return train_data, tuning_data


def get_factor_config_by_name(config_name):
    """
    根据配置名称获取因子配置 - 与linear_regression_optimizer.py保持统一
    
    Args:
        config_name: 配置名称 ('default', 'best_ten', 'best_five', 'best_ret_10')
        
    Returns:
        factors列表
    """
    if config_name == 'best_ten':
        return [
            'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
            'volatility_10d', 'option_value', 'volatility_5d', 'gain_from_low',
            'conv_value', 'bb_width'
        ]
    elif config_name == 'best_five':
        return [
            'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
            'volatility_10d'
        ]
    elif config_name == 'best_ret_10':
        return [
            'trend_slope_5d', 'trend_slope_10d', 'price_factor', 'arbitrage_space',
            'momentum_5d', 'bb_deviation', 'bb_position', 'williams_r',
            'amihud_illiquidity', 'corr_coef'
        ]
    elif config_name == 'claude_rec':
        return [
            'avg_turnover_20d',  # 流动性因子（IC信息比率：-0.309）
            'arbitrage_space',  # 估值因子（IC信息比率：0.200）
            'value_quality_score',  # 质量因子（IC信息比率：0.195）
            'amihud_illiquidity',  # 流动性因子（IC信息比率：0.194）
            'expected_return',  # 预期收益因子（IC信息比率：0.160）
            'conversion_tendency',  # 转换因子（IC信息比率：0.165）
            'cb_return',  # 动量因子（IC信息比率：0.142）
            'predicted_return'  # 预测因子（IC信息比率：0.099）
        ]
    else:
        # 返回None，使用默认的feature_config结构
        return None


def train_autogluon_predictor(train_start, train_end, model_name='default',
                             target_days=1, time_limit=300, presets='medium_quality',
                             custom_features=None, preprocessing_config='no_imputation',
                             tuning_ratio=0.1, use_sample_weights=False, sorted_feature=False):
    """
    训练AutoGluon预测器的便捷函数（使用基类统一实现）

    Args:
        train_start: 训练开始日期
        train_end: 训练结束日期
        model_name: 模型名称
        target_days: 预测天数
        time_limit: 训练时间限制（建议600秒以上获得更好效果）
        presets: 模型预设 ('medium_quality', 'high_quality', 'best_quality')
        custom_features: 自定义特征列表
        preprocessing_config: 预处理配置 ('default', 'conservative', 'aggressive', 'time_series')
        tuning_ratio: tuning data占总数据的比例，默认0.1（1/10）
        use_sample_weights: 是否使用样本权重，基于每日收益率排名计算
        sorted_feature: 是否使用排序特征（将特征转换为时间和横截面排序分位数）

    Returns:
        训练好的预测器
    """
    return AutoGluonFactorPredictor.train_predictor(
        train_start=train_start,
        train_end=train_end,
        model_name=model_name,
        target_days=target_days,
        time_limit=time_limit,
        presets=presets,
        custom_features=custom_features,
        preprocessing_config=preprocessing_config,
        tuning_ratio=tuning_ratio,
        use_sample_weights=use_sample_weights,
        sorted_feature=sorted_feature
    )


def load_and_predict_autogluon(model_name, factor_data, factor_name='ml_predicted_return'):
    """
    加载模型并进行预测的便捷函数（使用基类统一实现）

    Args:
        model_name: 模型名称
        factor_data: 因子数据DataFrame
        factor_name: 预测因子名称

    Returns:
        包含预测结果的DataFrame
    """
    return AutoGluonFactorPredictor.load_and_predict(
        model_name=model_name,
        factor_data=factor_data,
        factor_name=factor_name
    )


if __name__ == "__main__":
    # 测试训练
    train_start = '20240101'
    train_end = '20240630'
    
    predictor = train_autogluon_predictor(
        train_start=train_start,
        train_end=train_end,
        model_name='test_model',
        target_days=1,
        time_limit=60,  # 短时间测试
        presets='medium_quality'
    )
    
    if predictor:
        print("训练成功！")
        
        # 列出所有模型
        predictor.list_available_models()