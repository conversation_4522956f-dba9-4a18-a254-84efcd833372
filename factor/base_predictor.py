"""
基础预测器基类
为AutoGluonFactorPredictor和FTTransformerPredictor提供共同的功能和接口
"""

import pandas as pd
import numpy as np
import sqlite3
import pickle
import json
from datetime import datetime, timedelta
from pathlib import Path
import warnings
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Union, Any

from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

warnings.filterwarnings('ignore')

# 导入统一特征预处理器
try:
    from .unified_feature_preprocessor import UnifiedFeaturePreprocessor, create_unified_preprocessor
    UNIFIED_PREPROCESSING = True
except ImportError:
    print("统一特征预处理器不可用，将使用基础预处理")
    UNIFIED_PREPROCESSING = False

# 导入样本权重计算工具
try:
    from utils.sample_weights import calculate_sample_weights, add_sample_weights_to_dataframe
except ImportError:
    print("样本权重计算工具不可用")
    calculate_sample_weights = None
    add_sample_weights_to_dataframe = None

try:
    from config.config import config_manager
except ImportError:
    # 如果无法导入config，使用默认路径
    class MockConfigManager:
        def get_db_path(self):
            return 'data_storage/convertible_bond.db'
    config_manager = MockConfigManager()


def top_mean_squarer(y_true, y_pred, *, sample_weight=None, multioutput="uniform_average"):
    """计算前10%预测值的均方误差"""
    sorted_idx = np.argsort(-y_pred)
    cutoff = int(np.ceil(len(y_pred) * 0.1))
    top_idx = sorted_idx[:cutoff]
    
    if hasattr(y_true, 'iloc'):
        y_true_selected = y_true.iloc[top_idx]
    else:
        y_true_selected = y_true[top_idx]
    
    y_pred_selected = y_pred[top_idx]
    
    sample_weight_selected = None
    if sample_weight is not None:
        if hasattr(sample_weight, 'iloc'):
            sample_weight_selected = sample_weight.iloc[top_idx]
        else:
            sample_weight_selected = sample_weight[top_idx]
    
    return mean_squared_error(y_true_selected, y_pred_selected, sample_weight=sample_weight_selected, multioutput=multioutput)


def calculate_cross_sectional_r2(data, y_true_col, y_pred_col, date_col='trade_date'):
    """
    计算横截面R²：按日期分组，计算每个交易日内预测vs真实的R²，再取均值
    
    Args:
        data: 包含真实值、预测值和日期的DataFrame
        y_true_col: 真实值列名
        y_pred_col: 预测值列名  
        date_col: 日期列名，默认为'trade_date'
        
    Returns:
        float: 横截面R²的均值
    """
    if data.empty:
        return 0.0
        
    daily_r2_list = []
    
    # 按日期分组计算每日R²
    for date, group in data.groupby(date_col):
        if len(group) < 2:  # 至少需要2个样本才能计算R²
            continue
            
        y_true = group[y_true_col]
        y_pred = group[y_pred_col]
        
        # 检查是否有有效数据
        if y_true.isna().all() or y_pred.isna().all():
            continue
            
        # 计算当日R²
        try:
            daily_r2 = r2_score(y_true, y_pred)
            daily_r2_list.append(daily_r2)
        except Exception as e:
            # 如果计算失败（比如方差为0），跳过这一天
            continue
    
    # 返回所有日期R²的均值
    if daily_r2_list:
        cross_sectional_r2 = np.mean(daily_r2_list)
        print(f"横截面R²计算完成: {len(daily_r2_list)}个交易日，平均R²: {cross_sectional_r2:.4f}")
        return cross_sectional_r2
    else:
        print("警告: 无法计算横截面R²，返回0.0")
        return 0.0


def _split_data_by_bond(
        data: pd.DataFrame,
        tuning_ratio: float = 0.1,
        gap_days: int = 0,
        date_col: str = 'trade_date'
    ):
    """
    按时间将数据拆分为 (train_data, tuning_data)。

    参数
    ----
    data : DataFrame
        全量样本，含特征和标签列。
    tuning_ratio : float, default 0.1
        验证集占全部**日期段**的比例（例如 0.1 表示最近 10% 日期作为验证）。
    gap_days : int, default 0
        训练集末尾与验证集开头之间留多少天净空期，防信息渗漏。
    date_col : str, default 'trade_date'
        日期列名，允许字符串 'YYYYMMDD' / 'YYYY-MM-DD'。

    返回
    ----
    (train_data, tuning_data)
    """
    if date_col not in data.columns:
        raise KeyError(f"找不到日期列 {date_col}")

    # 1️⃣ 统一日期格式
    df = data.copy()
    df[date_col] = pd.to_datetime(df[date_col].astype(str), format='%Y%m%d', errors='coerce')
    if df[date_col].isna().any():
        raise ValueError(f"{date_col} 存在无法解析的日期")

    # 2️⃣ 计算验证集起点（最近 tuning_ratio% 的日期区间）
    uniq_dates = df[date_col].sort_values().unique()
    if len(uniq_dates) < 2:
        raise ValueError("数据日期种类太少，无法切分")

    # pivot_idx 至少为 1，确保有验证集
    pivot_idx = max(1, int(len(uniq_dates) * (1 - tuning_ratio)))
    val_start = uniq_dates[pivot_idx]

    # 3️⃣ 留空期（可选）
    if gap_days:
        train_end = val_start - timedelta(days=gap_days)
    else:
        train_end = val_start

    # 4️⃣ 切分
    train_data = df[df[date_col] < train_end].copy()
    tuning_data = df[df[date_col] >= val_start].copy()

    # 5️⃣ 打印信息（可注释掉）
    print(f"🔹 时间切分完成 (tuning_ratio={tuning_ratio:.2%})")
    print(f"   训练集: {train_data[date_col].min().date()} — {train_data[date_col].max().date()} | {len(train_data):,} 条")
    print(f"   验证集: {tuning_data[date_col].min().date()} — {tuning_data[date_col].max().date()} | {len(tuning_data):,} 条")
    if gap_days:
        print(f"   已留空 {gap_days} 天净空期")

    return train_data, tuning_data


def get_factor_config_by_name(config_name):
    """
    根据配置名称获取因子配置 - 与linear_regression_optimizer.py保持统一
    
    Args:
        config_name: 配置名称 ('default', 'best_ten', 'best_five', 'best_ret_10')
        
    Returns:
        factors列表
    """
    if config_name == 'best_ten':
        return [
            'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
            'volatility_10d', 'option_value', 'volatility_5d', 'gain_from_low',
            'conv_value', 'bb_width'
        ]
    elif config_name == 'best_five':
        return [
            'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
            'volatility_10d'
        ]
    elif config_name == 'best_ret_10':
        return [
            'trend_slope_5d', 'trend_slope_10d', 'price_factor', 'arbitrage_space',
            'momentum_5d', 'bb_deviation', 'bb_position', 'williams_r',
            'amihud_illiquidity', 'corr_coef'
        ]
    elif config_name == 'claude_rec':
        return [
            'avg_turnover_20d',  # 流动性因子（IC信息比率：-0.309）
            'arbitrage_space',  # 估值因子（IC信息比率：0.200）
            'value_quality_score',  # 质量因子（IC信息比率：0.195）
            'amihud_illiquidity',  # 流动性因子（IC信息比率：0.194）
            'expected_return',  # 预期收益因子（IC信息比率：0.160）
            'conversion_tendency',  # 转换因子（IC信息比率：0.165）
            'cb_return',  # 动量因子（IC信息比率：0.142）
            'predicted_return'  # 预测因子（IC信息比率：0.099）
        ]
    else:
        # 返回None，使用默认的feature_config结构
        return None


class BasePredictor(ABC):
    """
    基础预测器抽象类
    为AutoGluonFactorPredictor和FTTransformerPredictor提供共同的功能
    """
    
    def __init__(self, model_name='default', target_days=1, feature_config=None, 
                 preprocessing_config=None, create_unified_preprocessor_now=True, 
                 use_sample_weights=False, sorted_feature=False):
        """
        初始化基础预测器
        
        Args:
            model_name: 模型名称
            target_days: 预测天数（未来n天的收益率）
            feature_config: 特征配置字典
            preprocessing_config: 预处理配置
            create_unified_preprocessor_now: 是否立即创建预处理器
            use_sample_weights: 是否使用样本权重训练
            sorted_feature: 是否使用排序特征
        """
        self.model_name = model_name
        self.target_days = target_days
        self.feature_names = []
        self.target_name = f'return_{target_days}d'
        self.training_results = {}
        self.use_sample_weights = use_sample_weights
        self.sorted_feature = sorted_feature
        
        # 初始化统一特征预处理器
        self.preprocessing_config = preprocessing_config or 'default'
        self.preprocessor = None
        self.unified_preprocessor = None  # 保持兼容性
        
        if create_unified_preprocessor_now and UNIFIED_PREPROCESSING:
            self.preprocessor = create_unified_preprocessor(self.preprocessing_config)
            self.unified_preprocessor = self.preprocessor  # 保持兼容性
            print(f"使用统一特征预处理: {self.preprocessing_config}")
        elif not create_unified_preprocessor_now:
            print("预处理器将在模型加载时初始化")
        else:
            print("使用基础特征预处理")
        
        # 默认特征配置
        self.default_feature_config = {
            'core_convertible_factors': [
                # 核心可转债估值因子 - 最重要的价格驱动因子
                'arbitrage_space', 'premium_ratio', 'conv_value', 'double_low_score',
                'price_factor', 'premium_factor', 'bond_floor', 'option_value'
            ],
            'market_dynamics': [
                # 市场动态因子 - 直接相关的收益率和动量
                'stock_return', 'cb_return', 'momentum_5d', 'momentum_10d'
            ],
            'volatility_risk': [
                # 波动率风险因子 - 匹配预测窗口的波动率
                'volatility_5d', 'volatility_10d', 'relative_volatility'
            ],
            'price_technicals': [
                # 价格技术因子 - 有预测价值的技术指标
                'price_position', 'price_percentile', 'bb_position', 'bb_width'
            ],
            'volume_liquidity': [
                # 成交量流动性因子 - 反映市场关注度
                'volume_change_5d', 'volume_percentile', 'avg_turnover_20d'
            ],
            'validation_needed': [
                # 需要验证的因子 - 预测价值待确认
                'model_delta', 'corr_coef', 'reg_slope'
            ],
            'enhanced_factors': [
                # 新增增强因子 - 可转债特有的高级因子
                'forced_call_pressure', 'conversion_tendency', 'time_decay_factor',
                'momentum_vol_cross', 'value_quality_score', 'relative_strength_vs_market',
                'liquidity_shock', 'ma5_ma20_cross', 'support_strength'
            ]
        }
        
        # 处理外部传入的feature_config参数
        if feature_config is not None:
            self.feature_config = feature_config
        else:
            self.feature_config = self.default_feature_config
        
        # 特征数量统计
        total_features = sum(len(factors) for factors in self.feature_config.values())
        config_type = "优化版核心特征" if self.feature_config == self.default_feature_config else "自定义特征"
        print(f"特征配置完成 - {config_type}: {total_features}个")
        
        # 标准化器
        self.scaler = StandardScaler()
    
    @abstractmethod
    def _get_models_dir(self) -> Path:
        """获取模型保存目录，子类需要实现"""
        pass
    
    @property
    def models_dir(self) -> Path:
        """模型保存目录"""
        if not hasattr(self, '_models_dir'):
            self._models_dir = self._get_models_dir()
            self._models_dir.mkdir(parents=True, exist_ok=True)
        return self._models_dir

    @abstractmethod
    def train_model(self, train_data, time_limit=300, presets='medium_quality', tuning_data=None):
        """训练模型，子类需要实现"""
        pass

    @abstractmethod
    def predict(self, factor_data):
        """预测，子类需要实现"""
        pass

    @abstractmethod
    def load_model(self, model_name=None):
        """加载模型，子类需要实现"""
        pass

    @abstractmethod
    def list_available_models(self):
        """列出可用模型，子类需要实现"""
        pass

    def create_prediction_factor(self, factor_data, factor_name='predicted_return'):
        """
        创建预测因子

        Args:
            factor_data: 因子数据DataFrame
            factor_name: 预测因子名称

        Returns:
            包含预测因子的DataFrame
        """
        predictions = self.predict(factor_data)

        if predictions is not None:
            factor_data = factor_data.copy()
            factor_data[factor_name] = predictions
            print(f"已创建预测因子: {factor_name}")
            print(f"预测值统计: 均值={np.mean(predictions):.4f}, 标准差={np.std(predictions):.4f}")

        return factor_data

    @classmethod
    def train_predictor(cls, train_start, train_end, model_name='default',
                       target_days=1, time_limit=300, presets='medium_quality',
                       custom_features=None, preprocessing_config=None,
                       tuning_ratio=0.1, use_sample_weights=False, sorted_feature=False,
                       **kwargs):
        """
        通用的预测器训练便捷函数

        Args:
            train_start: 训练开始日期
            train_end: 训练结束日期
            model_name: 模型名称
            target_days: 预测天数
            time_limit: 训练时间限制（秒）
            presets: 模型预设
            custom_features: 自定义特征列表
            preprocessing_config: 预处理配置
            tuning_ratio: tuning data占总数据的比例，默认0.1（1/10）
            use_sample_weights: 是否使用样本权重
            sorted_feature: 是否使用排序特征
            **kwargs: 子类特定的参数

        Returns:
            训练好的预测器实例
        """
        print(f"\n🚀 开始训练{cls.__name__}...")
        print(f"训练期间: {train_start} - {train_end}")
        print(f"模型名称: {model_name}")
        print(f"预测天数: {target_days}")
        print(f"时间限制: {time_limit}秒")
        print(f"模型预设: {presets}")
        print(f"预处理配置: {preprocessing_config}")
        print(f"样本权重: {use_sample_weights}")
        print(f"排序特征: {sorted_feature}")
        print(f"调优比例: {tuning_ratio}")

        # 创建预测器实例
        predictor = cls(
            model_name=model_name,
            target_days=target_days,
            preprocessing_config=preprocessing_config,
            use_sample_weights=use_sample_weights,
            sorted_feature=sorted_feature,
            **kwargs
        )

        # 准备数据
        print("\n🔄 数据准备阶段...")
        full_data = predictor.prepare_training_data(train_start, train_end, custom_features)

        if full_data is None:
            print("❌ 数据准备失败")
            return None

        # 分割数据：按转债随机分割成train和tuning
        print(f"\n📊 数据分割阶段...")
        train_data, tuning_data = _split_data_by_bond(full_data, tuning_ratio)

        print(f"总数据量: {len(full_data)} 条记录")
        print(f"训练数据: {len(train_data)} 条记录 ({len(train_data)/len(full_data)*100:.1f}%)")
        print(f"调优数据: {len(tuning_data)} 条记录 ({len(tuning_data)/len(full_data)*100:.1f}%)")

        # 训练模型
        print(f"\n🚀 模型训练阶段...")
        print(f"特征数: {len(predictor.feature_names)} 个")

        results = predictor.train_model(train_data, time_limit, presets, tuning_data)

        if results is None:
            print("❌ 模型训练失败")
            return None

        print(f"\n✅ 训练完成！模型已保存为: {model_name}")
        return predictor

    @classmethod
    def load_and_predict(cls, model_name, factor_data, factor_name=None, **kwargs):
        """
        通用的加载模型并进行预测的便捷函数

        Args:
            model_name: 模型名称
            factor_data: 因子数据DataFrame
            factor_name: 预测因子名称（如果为None，使用默认名称）
            **kwargs: 子类特定的参数

        Returns:
            包含预测结果的DataFrame
        """
        # 设置默认因子名称
        if factor_name is None:
            if cls.__name__ == 'AutoGluonFactorPredictor':
                factor_name = 'ml_predicted_return'
            elif cls.__name__ == 'FTTransformerPredictor':
                factor_name = 'ft_predicted_return'
            else:
                factor_name = 'predicted_return'

        # 创建预测器实例
        predictor = cls(model_name=model_name, **kwargs)

        # 加载模型
        if not predictor.load_model():
            print(f"加载模型失败: {model_name}")
            return factor_data

        # 创建预测因子
        result_data = predictor.create_prediction_factor(factor_data, factor_name)

        return result_data

    def prepare_training_data(self, start_date, end_date, custom_features=None):
        """
        准备训练数据（统一版本，包含目标变量计算和相对收益转换）

        Args:
            start_date: 开始日期
            end_date: 结束日期
            custom_features: 自定义特征列表

        Returns:
            准备好的训练数据DataFrame
        """
        print(f"准备训练数据: {start_date} - {end_date}")

        try:
            conn = sqlite3.connect(config_manager.get_db_path())

            query_template = """
                SELECT f.*, d.close, d.full_price
                FROM cb_factor f
                JOIN cb_daily d ON f.ts_code = d.ts_code AND f.trade_date = d.trade_date
                WHERE f.trade_date >= '{start_date}'
                AND f.trade_date <= '{end_date}'
                AND d.close > 0
                ORDER BY f.trade_date, f.ts_code
                """

            query = query_template.format(start_date=start_date, end_date=end_date)
            df = pd.read_sql_query(query, conn)

            if df is None or df.empty:
                print("❌ 查询结果为空")
                return None

            print(f"原始数据: {len(df)} 条记录")

            # 按债券分组计算目标变量（未来收益率）
            df = df.sort_values(['ts_code', 'trade_date'])
            df[self.target_name] = df.groupby('ts_code')['full_price'].pct_change(periods=self.target_days).shift(-self.target_days)

            # 转换为相对收益：每行数据相对于同一交易日所有标的的平均收益的相对收益
            print("正在计算相对收益...")
            daily_mean_return = df.groupby('trade_date')[self.target_name].mean()
            df[self.target_name] = df.apply(lambda row: row[self.target_name] - daily_mean_return.get(row['trade_date'], 0)
                                          if pd.notna(row[self.target_name]) else row[self.target_name], axis=1)

            # 限制相对收益率范围到 [-10%, 10%]
            before_clip_count = len(df[df[self.target_name].notna()])
            original_min = df[self.target_name].min()
            original_max = df[self.target_name].max()

            df[self.target_name] = df[self.target_name].clip(lower=-0.1, upper=0.1)

            after_clip_min = df[self.target_name].min()
            after_clip_max = df[self.target_name].max()

            print(f"相对收益转换完成，目标变量已调整为相对于同日平均收益的相对收益")
            print(f"收益率范围限制: [{original_min:.4f}, {original_max:.4f}] -> [{after_clip_min:.4f}, {after_clip_max:.4f}]")

            # 构建特征列表
            if custom_features is not None:
                self.feature_names = custom_features
                print(f"使用自定义特征: {len(self.feature_names)}个")
            else:
                self.feature_names = []
                for category, factors in self.feature_config.items():
                    self.feature_names.extend(factors)
                print(f"使用配置特征: {len(self.feature_names)}个")

            # 检查特征可用性
            available_features = [col for col in self.feature_names if col in df.columns]
            missing_features = [col for col in self.feature_names if col not in df.columns]

            if not available_features:
                print("❌ 没有找到可用的特征列")
                return None

            if missing_features:
                print(f"⚠️  缺失特征: {len(missing_features)}个")
                if len(missing_features) <= 10:
                    print(f"缺失特征: {missing_features}")

            self.feature_names = available_features
            print(f"✅ 最终使用特征: {len(available_features)}个")

            # 选择需要的列
            required_columns = ['ts_code', 'trade_date'] + available_features + [self.target_name]
            df_clean = df[required_columns].copy()

            # 数据预处理
            if self.preprocessor is not None:
                print("应用统一特征预处理...")
                df_clean = self.preprocessor.preprocess_features(df_clean, available_features)
            else:
                print("应用基础预处理...")
                df_clean = df_clean.dropna(subset=available_features + [self.target_name])

                # 过滤异常收益率
                df_clean = df_clean[
                    (df_clean[self.target_name] >= -0.5) &
                    (df_clean[self.target_name] <= 0.5)
                ]

            if df_clean.empty:
                print("❌ 预处理后数据为空")
                return None

            # 应用排序特征转换（如果启用）
            if self.sorted_feature:
                df_clean = self._apply_sorted_feature_transformation(df_clean)
                if df_clean.empty:
                    print("❌ 排序特征转换后数据为空")
                    return None

            # 添加样本权重（如果启用）
            if self.use_sample_weights and add_sample_weights_to_dataframe is not None:
                print("计算样本权重...")
                df_clean = add_sample_weights_to_dataframe(df_clean, self.target_name)

            print(f"✅ 数据准备完成: {len(df_clean)} 条记录")
            print(f"特征数量: {len(self.feature_names)}")
            print(f"目标变量统计: 均值={df_clean[self.target_name].mean():.4f}, 标准差={df_clean[self.target_name].std():.4f}")

            return df_clean

        except Exception as e:
            print(f"准备训练数据失败: {e}")
            return None
        finally:
            conn.close()

    def _apply_sorted_feature_transformation(self, df_clean):
        """
        应用排序特征转换：将每个特征转换为两个排序分位数特征
        1) 时间维度：该特征在同标的60天内的排序分位数
        2) 横截面维度：该特征在同交易日的排序分位数

        Args:
            df_clean: 预处理后的数据DataFrame

        Returns:
            转换后的DataFrame
        """
        print("  正在计算排序特征...")

        # 确保数据按ts_code和trade_date排序
        df_clean = df_clean.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)

        # 保存原始特征名
        original_features = self.feature_names.copy()
        new_features = []

        # 为每个特征计算排序分位数
        for feature in original_features:
            if feature in df_clean.columns:
                # 1) 时间维度排序：同标的60天内的排序分位数
                time_rank_col = f"{feature}_time_rank"
                df_clean[time_rank_col] = df_clean.groupby('ts_code')[feature].transform(
                    lambda x: x.rolling(window=60, min_periods=1).rank(pct=True)
                )
                new_features.append(time_rank_col)

                # 2) 横截面排序：同交易日的排序分位数
                cross_rank_col = f"{feature}_cross_rank"
                df_clean[cross_rank_col] = df_clean.groupby('trade_date')[feature].transform(
                    lambda x: x.rank(pct=True)
                )
                new_features.append(cross_rank_col)

        # 更新特征名列表（替换为排序特征）
        self.feature_names = new_features

        # 移除原始特征列（保留必要的列）
        keep_columns = ['ts_code', 'trade_date', self.target_name] + new_features
        if 'sample_weight' in df_clean.columns:
            keep_columns.append('sample_weight')

        df_clean = df_clean[keep_columns]

        print(f"  排序特征转换完成: {len(original_features)} -> {len(new_features)} 个特征")
        print(f"  时间维度特征: {len([f for f in new_features if '_time_rank' in f])}")
        print(f"  横截面特征: {len([f for f in new_features if '_cross_rank' in f])}")

        return df_clean

    def _apply_sorted_feature_transformation_for_prediction(self, df_clean, available_features):
        """
        应用排序特征转换（用于推理模式）：将可用特征转换为排序分位数特征

        Args:
            df_clean: 预处理后的数据DataFrame
            available_features: 可用的特征列表

        Returns:
            转换后的DataFrame
        """
        print("  正在计算排序特征（推理模式）...")

        # 确保数据按ts_code和trade_date排序
        df_clean = df_clean.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)

        new_features = []

        # 为每个可用特征计算排序分位数
        for feature in available_features:
            if feature in df_clean.columns:
                # 1) 时间维度排序：同标的60天内的排序分位数
                time_rank_col = f"{feature}_time_rank"
                df_clean[time_rank_col] = df_clean.groupby('ts_code')[feature].transform(
                    lambda x: x.rolling(window=60, min_periods=1).rank(pct=True)
                )
                new_features.append(time_rank_col)

                # 2) 横截面排序：同交易日的排序分位数
                cross_rank_col = f"{feature}_cross_rank"
                df_clean[cross_rank_col] = df_clean.groupby('trade_date')[feature].transform(
                    lambda x: x.rank(pct=True)
                )
                new_features.append(cross_rank_col)

        # 保留必要的列
        keep_columns = ['ts_code', 'trade_date'] + new_features
        df_clean = df_clean[keep_columns]

        print(f"  排序特征转换完成: {len(available_features)} -> {len(new_features)} 个特征")
        print(f"  时间维度特征: {len([f for f in new_features if '_time_rank' in f])}")
        print(f"  横截面特征: {len([f for f in new_features if '_cross_rank' in f])}")

        return df_clean
