"""
基础价格因子计算器
计算可转债的基础价格相关因子
"""

import pandas as pd
import numpy as np
import sqlite3
import time
import logging
from typing import Dict, Any

from .base_factor_calculator import FactorCalculator


class BasicPriceFactorCalculator(FactorCalculator):
    """
    基础价格因子计算器
    
    计算可转债的基础价格相关因子：
    - stock_close: 正股收盘价
    - conv_value: 转股价值
    - combined_factor: 组合因子（暂时设为0，可后续扩展）
    
    注意：转股价格从cb_basic表获取，转债价格从cb_daily表获取
    """
    
    def __init__(self):
        """初始化基础价格因子计算器"""
        super().__init__("基础价格因子计算器")
        print("基础价格因子计算器初始化完成")
    
    
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的基础价格因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含基础价格因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🔄 计算基础价格因子 - 日期: {date_str}")
        start_time = time.time()
        
        # 获取可转债数据
        df = self._get_cb_data_for_date(date_str)
        
        if df.empty:
            print(f"日期 {date_str} 没有可用的可转债数据")
            return pd.DataFrame()
        
        print(f"找到 {len(df)} 只可转债")
        
        # 计算基础价格因子
        df = self._calculate_basic_price_factors(df, date_str)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ 基础价格因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的基础价格因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含基础价格因子的DataFrame
        """
        print(f"\n🔄 计算基础价格因子 - 时间段: {start_date} 到 {end_date}")
        
        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')
        
        # 获取交易日
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)
        
        if not trading_dates:
            print("指定时间段内没有交易日数据")
            return pd.DataFrame()
        
        print(f"找到 {len(trading_dates)} 个交易日")
        
        all_factors = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")
            
            try:
                daily_factors = self.calculate_factors_for_date(trade_date, **kwargs)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                self.logger.error(f"处理日期 {trade_date} 时出错: {e}")
                continue
        
        if not all_factors:
            print("没有成功计算出任何因子数据")
            return pd.DataFrame()
        
        # 合并所有日期的数据
        result_df = pd.concat(all_factors, ignore_index=True)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        print(f"✅ 基础价格因子计算完成，共 {len(result_df)} 条记录")
        
        return result_df
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的基础价格因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数
            
        Returns:
            包含单个债券基础价格因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🎯 计算单债券基础价格因子 - 日期: {date_str}, 债券: {bond_code}")
        start_time = time.time()
        
        # 获取指定可转债的数据
        df = self._get_single_cb_data(date_str, bond_code)
        
        if df.empty:
            raise ValueError(f"债券 {bond_code} 在日期 {date_str} 没有找到数据")
        
        print(f"找到债券: {bond_code}")
        
        # 计算基础价格因子
        df = self._calculate_basic_price_factors(df, date_str)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ 单债券基础价格因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def _get_cb_data_for_date(self, date_str: str) -> pd.DataFrame:
        """
        获取指定日期的可转债数据（基础价格因子专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD

        Returns:
            包含可转债数据的DataFrame
        """
        # 使用基类的通用方法，需要包含转股价格等字段
        return super()._get_cb_data_for_date(
            date_str=date_str,
            fields="full",  # 需要完整字段包括conv_price和stk_code
            additional_filters="cb.conv_price > 0",
            strict_validation=False
        )
    
    def _get_single_cb_data(self, date_str: str, bond_code: str) -> pd.DataFrame:
        """
        获取指定日期单个可转债的数据（基础价格因子专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            bond_code: 可转债代码

        Returns:
            包含单个可转债数据的DataFrame
        """
        # 使用基类的通用方法，需要包含转股价格等字段
        return super()._get_single_cb_data(
            date_str=date_str,
            bond_code=bond_code,
            fields="full",  # 需要完整字段包括conv_price和stk_code
            additional_filters="cb.conv_price > 0",
            strict_validation=False
        )
    
    def _calculate_basic_price_factors(self, df: pd.DataFrame, date_str: str) -> pd.DataFrame:
        """
        计算基础价格因子
        
        Args:
            df: 包含可转债数据的DataFrame
            date_str: 日期字符串
            
        Returns:
            添加了基础价格因子的DataFrame
        """
        if df.empty:
            return df
        
        result = df.copy()
        
        try:
            # 1. 获取正股价格数据
            conn = sqlite3.connect(self.db_path)
            stock_data = self._get_stock_data_for_date(result, date_str, conn)
            conn.close()
            
            # 2. 创建stk_ts_code映射列
            result['stk_ts_code'] = result['stk_code'].apply(
                lambda x: x if '.' in str(x) else (f"{x}.SZ" if str(x).startswith(('0', '3')) else f"{x}.SH")
            )
            
            # 3. 合并正股数据
            if not stock_data.empty:
                result = pd.merge(result, stock_data, on='stk_ts_code', how='left', suffixes=('', '_stock'))
                result.rename(columns={'close_stock': 'stock_close', 'org_close_stock': 'stock_org_close'}, inplace=True)
                # 4. 计算转股价值 (conv_value)
                # 转股价值 = 正股原始价格 * 100 / 转股价格 (使用原始价格，避免前复权影响)
                # 如果stock_org_close不存在，使用org_close字段
                org_close_col = 'stock_org_close' if 'stock_org_close' in result.columns else 'org_close'
                result['conv_value'] = (result[org_close_col] * 100 / result['conv_price']).round(4)
                
            # 处理异常值
                result['conv_value'] = result['conv_value'].ffill().fillna(0)
                result.loc[result['conv_value'] < 0, 'conv_value'] = 0

                # 5. 计算转股溢价率 (premium_ratio)
                # 转股溢价率 = (转债价格 / 转股价值) - 1
                result['premium_ratio'] = ((result['close'] / result['conv_value']) - 1).round(4)
                result.loc[result['conv_value'] <= 0, 'premium_ratio'] = 0  # 转股价值为0时设置溢价率为0
                result['premium_ratio'] = result['premium_ratio'].clip(-0.5, 2.0)  # 限制在-50%到200%之间
            else:
                # 没有正股数据时设置默认值
                result['stock_close'] = 0
                result['conv_value'] = 0
                result['premium_ratio'] = 0
            
            # 6. 组合因子 (combined_factor) - 暂时设为0，可后续扩展
            result['combined_factor'] = 0.0
            
            # 填充缺失值 - 使用前向填充
            result['stock_close'] = result['stock_close'].ffill().fillna(0)
            result['conv_value'] = result['conv_value'].ffill().fillna(0)
            result['premium_ratio'] = result['premium_ratio'].ffill().fillna(0)  # 修复：添加premium_ratio的缺失值填充
            result['combined_factor'] = result['combined_factor'].ffill().fillna(0)

            return result

        except Exception as e:
            self.logger.warning(f"计算基础价格因子失败: {e}")
            # 设置默认值
            result['stock_close'] = 0
            result['conv_value'] = 0
            result['premium_ratio'] = 0  # 修复：添加premium_ratio的默认值
            result['combined_factor'] = 0
            return result
    
    def _get_stock_data_for_date(self, cb_df: pd.DataFrame, date_str: str, conn) -> pd.DataFrame:
        """
        获取指定日期的正股数据
        
        Args:
            cb_df: 可转债数据DataFrame
            date_str: 日期字符串
            conn: 数据库连接
            
        Returns:
            正股数据DataFrame
        """
        if cb_df.empty or 'stk_code' not in cb_df.columns:
            return pd.DataFrame()
        
        # 获取所有正股代码
        stock_codes = cb_df['stk_code'].dropna().unique()
        if len(stock_codes) == 0:
            return pd.DataFrame()
        
        # 处理正股ts_code（cb_basic表中stk_code可能已经包含后缀）
        stock_ts_codes = []
        for code in stock_codes:
            # 如果已经包含后缀，直接使用
            if '.' in str(code):
                stock_ts_codes.append(code)
            else:
                # 否则根据规则添加后缀
                if code.startswith('0') or code.startswith('3'):
                    stock_ts_codes.append(f"{code}.SZ")
                else:
                    stock_ts_codes.append(f"{code}.SH")
        
        if not stock_ts_codes:
            return pd.DataFrame()
        
        # 查询正股数据
        stock_codes_str = "','".join(stock_ts_codes)
        query = f"""
        SELECT ts_code, close, COALESCE(org_close, close) as org_close
        FROM stock_daily
        WHERE ts_code IN ('{stock_codes_str}')
        AND trade_date = '{date_str}'
        """
        
        try:
            stock_data = pd.read_sql(query, conn)
            if not stock_data.empty:
                # 创建映射关系
                cb_df_copy = cb_df.copy()
                cb_df_copy['stk_ts_code'] = cb_df_copy['stk_code'].apply(
                    lambda x: x if '.' in str(x) else (f"{x}.SZ" if str(x).startswith(('0', '3')) else f"{x}.SH")
                )
                
                return stock_data.rename(columns={'ts_code': 'stk_ts_code'})
            else:
                return pd.DataFrame()
        except Exception as e:
            self.logger.warning(f"获取正股数据失败: {e}")
            return pd.DataFrame()
    
