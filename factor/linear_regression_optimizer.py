#!/usr/bin/env python3
"""
线性回归模型优化器
用于优化combined_factor中的参数组合系数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import sqlite3
import pickle
import json
from datetime import datetime, timedelta
from pathlib import Path
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

from config.config import config_manager
from utils.sample_weights import calculate_sample_weights

# 导入top_mean_squarer函数
try:
    from .base_predictor import top_mean_squarer
except ImportError:
    # 如果导入失败，定义一个本地版本
    def top_mean_squarer(y_true, y_pred, *, sample_weight=None, multioutput="uniform_average"):
        """计算前10%预测值的均方误差"""
        sorted_idx = np.argsort(-y_pred)
        cutoff = int(np.ceil(len(y_pred) * 0.1))
        top_idx = sorted_idx[:cutoff]

        if hasattr(y_true, 'iloc'):
            y_true_selected = y_true.iloc[top_idx]
        else:
            y_true_selected = y_true[top_idx]

        y_pred_selected = y_pred[top_idx]

        sample_weight_selected = None
        if sample_weight is not None:
            if hasattr(sample_weight, 'iloc'):
                sample_weight_selected = sample_weight.iloc[top_idx]
            else:
                sample_weight_selected = sample_weight[top_idx]

        return mean_squared_error(y_true_selected, y_pred_selected, sample_weight=sample_weight_selected, multioutput=multioutput)


class FactorLinearRegression:
    """增强版因子线性回归优化器"""

    def __init__(self, regularization='ridge', alpha=1.0, model_name='default',
                 factor_config=None, target_factor='next_return', return_days=1,
                 factor_config_name='default', use_sample_weights=False, sorted_feature=False,
                 use_top_mean_squarer=False):
        """
        初始化线性回归优化器

        Args:
            regularization: 正则化方法 ('none', 'ridge', 'lasso')
            alpha: 正则化强度
            model_name: 模型名称，用于保存和加载
            factor_config: 因子配置字典，指定要使用的因子
            target_factor: 目标因子名称
            return_days: 收益率计算天数，默认1天
            factor_config_name: 因子配置名称 ('default' 或 'best_ten')
            use_sample_weights: 是否使用样本权重训练
            sorted_feature: 是否使用排序特征（将每个特征转换为时间维度和横截面维度的排序分位数）
            use_top_mean_squarer: 是否使用top_mean_squarer优化目标（只对预测值前10%的样本计算损失）
        """
        self.regularization = regularization
        self.alpha = alpha
        self.model_name = model_name
        self.target_factor = target_factor
        self.return_days = return_days
        self.use_sample_weights = use_sample_weights
        self.sorted_feature = sorted_feature
        self.use_top_mean_squarer = use_top_mean_squarer
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.training_results = {}

        # 默认因子配置
        self.default_factor_config = {
            'zl_factors': ['arbitrage_space', 'model_delta', 'corr_coef', 'reg_slope'],
            'momentum_factors': ['momentum_5d', 'momentum_10d', 'momentum_20d'],
            'double_low_factors': ['price_factor', 'premium_factor', 'double_low_score'],
            'technical_factors': ['rsi_6d', 'rsi_12d', 'macd', 'vol_change'],
            'fundamental_factors': ['bond_floor', 'option_value']
        }
        
        # IC排名前十因子配置
        self.best_ten_factor_config = {
            'best_ten_factors': [
                'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
                'volatility_10d', 'option_value', 'volatility_5d', 'gain_from_low',
                'conv_value', 'bb_width'
            ]
        }
        
        # IC排名前五因子配置
        self.best_five_factor_config = {
            'best_five_factors': [
                'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
                'volatility_10d'
            ]
        }
        
        # 收益率排名前十因子配置
        self.best_ret_10_factor_config = {
            'best_ret_10_factors': [
                'trend_slope_5d', 'trend_slope_10d', 'price_factor', 'arbitrage_space',
                'momentum_5d', 'bb_deviation', 'bb_position', 'williams_r',
                'amihud_illiquidity', 'corr_coef'
            ]
        }

        # 根据factor_config_name选择因子配置
        if factor_config is not None:
            self.factor_config = factor_config
            # 如果是传入的配置，使用实际的配置名称
            self.factor_config_name = factor_config_name
        else:
            self.factor_config = self.get_factor_config_by_name(factor_config_name)
            self.factor_config_name = factor_config_name

        # 模型保存路径
        self.models_dir = Path('models/linear_regression')
        self.models_dir.mkdir(parents=True, exist_ok=True)

        print(f"线性回归优化器初始化完成 - 模型: {model_name}, 正则化: {regularization}")
        print(f"目标因子: {target_factor}, 收益率天数: {return_days}")
        print(f"因子配置名称: {self.factor_config_name}")
        print(f"因子配置: {list(self.factor_config.keys())}")
        print(f"使用top_mean_squarer优化: {'是' if use_top_mean_squarer else '否'}")
        
        # 显示具体的因子列表
        for group_name, factors in self.factor_config.items():
            print(f"  {group_name}: {factors}")
    
    def get_factor_config_by_name(self, config_name):
        """
        根据配置名称获取因子配置
        
        Args:
            config_name: 配置名称 ('default', 'best_ten', 'best_five', 'best_ret_10')
            
        Returns:
            因子配置字典
        """
        if config_name == 'best_ten':
            return self.best_ten_factor_config
        elif config_name == 'best_five':
            return self.best_five_factor_config
        elif config_name == 'best_ret_10':
            return self.best_ret_10_factor_config
        else:
            return self.default_factor_config

    def save_model(self, model_path=None):
        """
        保存模型到文件

        Args:
            model_path: 模型保存路径，如果为None则使用默认路径
        """
        if self.model is None:
            print("模型未训练，无法保存")
            return False

        if model_path is None:
            model_path = self.models_dir / f"{self.model_name}.pkl"

        try:
            # 准备保存的数据
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'training_results': self.training_results,
                'regularization': self.regularization,
                'alpha': self.alpha,
                'target_factor': self.target_factor,
                'return_days': self.return_days,
                'factor_config': self.factor_config,
                'model_name': self.model_name,
                'use_sample_weights': self.use_sample_weights,
                'sorted_feature': self.sorted_feature,
                'use_top_mean_squarer': self.use_top_mean_squarer,
                'save_time': datetime.now().isoformat()
            }

            # 保存模型
            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)

            # 保存配置文件（便于查看）
            config_path = model_path.with_suffix('.json')
            config_data = {
                'model_name': self.model_name,
                'regularization': self.regularization,
                'alpha': self.alpha,
                'target_factor': self.target_factor,
                'return_days': self.return_days,
                'factor_config': self.factor_config,
                'feature_names': self.feature_names,
                'use_sample_weights': self.use_sample_weights,
                'sorted_feature': self.sorted_feature,
                'use_top_mean_squarer': self.use_top_mean_squarer,
                'training_results': {k: v for k, v in self.training_results.items()
                                   if not isinstance(v, np.ndarray)},  # 排除numpy数组
                'save_time': datetime.now().isoformat()
            }

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            print(f"模型已保存到: {model_path}")
            print(f"配置已保存到: {config_path}")
            return True

        except Exception as e:
            print(f"保存模型失败: {e}")
            return False

    def load_model(self, model_path=None):
        """
        从文件加载模型

        Args:
            model_path: 模型文件路径，如果为None则使用默认路径
        """
        if model_path is None:
            model_path = self.models_dir / f"{self.model_name}.pkl"

        if not Path(model_path).exists():
            print(f"模型文件不存在: {model_path}")
            return False

        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            # 恢复模型状态
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.training_results = model_data['training_results']
            self.regularization = model_data['regularization']
            self.alpha = model_data['alpha']
            self.target_factor = model_data['target_factor']
            self.return_days = model_data.get('return_days', 1)  # 向后兼容
            self.factor_config = model_data['factor_config']
            self.use_sample_weights = model_data.get('use_sample_weights', False)  # 向后兼容
            self.sorted_feature = model_data.get('sorted_feature', False)  # 向后兼容
            self.use_top_mean_squarer = model_data.get('use_top_mean_squarer', False)  # 向后兼容

            print(f"模型已加载: {model_path}")
            print(f"模型名称: {model_data['model_name']}")
            print(f"保存时间: {model_data['save_time']}")
            print(f"特征数量: {len(self.feature_names)}")
            print(f"使用top_mean_squarer: {'是' if self.use_top_mean_squarer else '否'}")

            if self.training_results:
                print(f"训练R2: {self.training_results.get('r2', 'N/A'):.4f}")
                print(f"交叉验证R2: {self.training_results.get('cv_mean', 'N/A'):.4f}")
                if 'top_mse' in self.training_results:
                    print(f"Top MSE (前10%): {self.training_results['top_mse']:.6f}")

            return True

        except Exception as e:
            print(f"加载模型失败: {e}")
            return False

    def list_available_models(self):
        """列出所有可用的模型"""
        model_files = list(self.models_dir.glob("*.pkl"))

        if not model_files:
            print("没有找到已保存的模型")
            return []

        print(f"\n可用模型列表 (共{len(model_files)}个):")
        print("-" * 80)
        print(f"{'模型名称':<20} {'保存时间':<20} {'正则化':<10} {'R2':<8} {'特征数':<8}")
        print("-" * 80)

        models_info = []
        for model_file in sorted(model_files):
            try:
                config_file = model_file.with_suffix('.json')
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    model_name = config.get('model_name', model_file.stem)
                    save_time = config.get('save_time', 'Unknown')[:19]
                    regularization = config.get('regularization', 'Unknown')
                    r2 = config.get('training_results', {}).get('r2', 0)
                    feature_count = len(config.get('feature_names', []))

                    print(f"{model_name:<20} {save_time:<20} {regularization:<10} {r2:<8.4f} {feature_count:<8}")

                    models_info.append({
                        'name': model_name,
                        'path': model_file,
                        'save_time': save_time,
                        'regularization': regularization,
                        'r2': r2,
                        'feature_count': feature_count
                    })
                else:
                    print(f"{model_file.stem:<20} {'No config':<20} {'Unknown':<10} {'N/A':<8} {'N/A':<8}")

            except Exception as e:
                print(f"{model_file.stem:<20} {'Error':<20} {'Error':<10} {'N/A':<8} {'N/A':<8}")

        return models_info

    def prepare_training_data(self, start_date, end_date, custom_factors=None):
        """
        准备训练数据（支持灵活的因子选择）

        Args:
            start_date: 训练开始日期
            end_date: 训练结束日期
            custom_factors: 自定义因子列表，如果为None则使用配置的因子

        Returns:
            X, y: 特征矩阵和目标变量
            如果use_sample_weights为True，还会返回sample_weights
        """
        print(f"准备训练数据: {start_date} - {end_date}")

        # 连接数据库
        conn = sqlite3.connect(config_manager.get_db_path())

        try:
            # 获取因子数据
            query = f"""
            SELECT f.*, d.close, d.full_price
            FROM cb_factor f
            JOIN cb_daily d ON f.ts_code = d.ts_code AND f.trade_date = d.trade_date
            WHERE f.trade_date >= '{start_date}' AND f.trade_date <= '{end_date}'
            AND d.close > 0
            ORDER BY f.trade_date, f.ts_code
            """

            df = pd.read_sql(query, conn)

            if df.empty:
                print("没有找到训练数据")
                return None, None

            print(f"加载数据: {len(df)} 条记录")

            # 计算目标变量
            df = df.sort_values(['ts_code', 'trade_date'])

            if self.target_factor == 'next_return':
                # 计算指定天数后的收益率
                df['next_return'] = df.groupby('ts_code')['full_price'].pct_change(periods=self.return_days).shift(-self.return_days)
            elif self.target_factor == 'next_close_return':
                # 计算指定天数后的收盘价收益率
                df['next_close_return'] = df.groupby('ts_code')['full_price'].pct_change(periods=self.return_days).shift(-self.return_days)
            elif self.target_factor.startswith('return_'):
                # 支持动态天数收益率，如 'return_5d', 'return_10d'
                try:
                    days = int(self.target_factor.split('_')[1].replace('d', ''))
                    df[self.target_factor] = df.groupby('ts_code')['full_price'].pct_change(periods=days).shift(-days)
                except (ValueError, IndexError):
                    print(f"无法解析目标因子天数: {self.target_factor}")
                    return None, None
            elif self.target_factor.startswith('close_return_'):
                # 支持动态天数收盘价收益率，如 'close_return_5d', 'close_return_10d'
                try:
                    # 解析格式：close_return_5d -> 5
                    parts = self.target_factor.split('_')
                    if len(parts) >= 3:  # close_return_5d
                        days = int(parts[2].replace('d', ''))
                    else:  # close_return_5
                        days = int(parts[1].replace('d', ''))
                    df[self.target_factor] = df.groupby('ts_code')['full_price'].pct_change(periods=days).shift(-days)
                except (ValueError, IndexError):
                    print(f"无法解析目标因子天数: {self.target_factor}")
                    return None, None
            elif self.target_factor in df.columns:
                # 使用现有的因子作为目标
                pass
            else:
                print(f"未知的目标因子: {self.target_factor}")
                return None, None

            # 选择特征
            if custom_factors:
                feature_columns = custom_factors
            else:
                # 根据配置选择因子
                feature_columns = []
                for category, factors in self.factor_config.items():
                    feature_columns.extend(factors)

            # 检查特征是否存在
            available_features = [col for col in feature_columns if col in df.columns]
            if not available_features:
                print("没有找到可用的特征列")
                print(f"请求的特征: {feature_columns}")
                print(f"可用列: {df.columns.tolist()}")
                return None, None

            self.feature_names = available_features
            print(f"使用特征 ({len(available_features)}个): {self.feature_names}")

            # 移除缺失值，但保留日期信息用于权重计算
            required_columns = available_features + [self.target_factor]
            if self.use_sample_weights:
                required_columns += ['trade_date', 'ts_code']
            
            df_clean = df[required_columns].dropna()

            if df_clean.empty:
                print("移除缺失值后没有有效数据")
                if self.use_sample_weights:
                    return None, None, None
                else:
                    return None, None

            # 排序特征转换
            if self.sorted_feature:
                print(f"\n🔄 应用排序特征转换...")
                df_clean = self._apply_sorted_feature_transformation(df_clean, available_features)
                # 更新特征名列表（原始特征被转换为排序特征）
                available_features = [col for col in df_clean.columns 
                                    if col not in ['ts_code', 'trade_date', self.target_factor]]
                self.feature_names = available_features
                print(f"排序特征转换完成: {len(available_features)} 个特征")

            # 准备特征矩阵和目标变量
            X = df_clean[available_features].values
            y = df_clean[self.target_factor].values

            # 移除异常值
            if self.target_factor in ['next_return', 'next_close_return']:
                # 收益率异常值过滤
                valid_mask = (np.abs(y) <= 0.5) & np.isfinite(X).all(axis=1) & np.isfinite(y)
            else:
                # 一般异常值过滤
                y_q1, y_q99 = np.percentile(y, [1, 99])
                valid_mask = (y >= y_q1) & (y <= y_q99) & np.isfinite(X).all(axis=1) & np.isfinite(y)

            # 应用异常值过滤
            X = X[valid_mask]
            y = y[valid_mask]
            df_filtered = df_clean.iloc[valid_mask].copy()

            print(f"最终训练样本: {len(X)} 个")
            print(f"目标变量 ({self.target_factor}) 统计: 均值={y.mean():.4f}, 标准差={y.std():.4f}")

            # 计算样本权重（如果需要）
            if self.use_sample_weights:
                sample_weights = self._calculate_sample_weights(df_filtered)
                print(f"样本权重统计: 均值={sample_weights.mean():.4f}, 标准差={sample_weights.std():.4f}")
                return X, y, sample_weights
            else:
                return X, y

        except Exception as e:
            print(f"准备训练数据失败: {e}")
            if self.use_sample_weights:
                return None, None, None
            else:
                return None, None
        finally:
            conn.close()
    
    def _apply_sorted_feature_transformation(self, df_clean, available_features):
        """
        应用排序特征转换：将每个特征转换为两个排序分位数特征
        1) 时间维度：该特征在同标的60天内的排序分位数
        2) 横截面维度：该特征在同交易日的排序分位数
        
        Args:
            df_clean: 预处理后的数据DataFrame
            available_features: 可用特征列表
            
        Returns:
            转换后的DataFrame
        """
        print("  正在计算排序特征...")
        
        # 确保数据按时间排序
        df_clean = df_clean.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)
        
        # 获取原始特征名（不包括ts_code, trade_date, target）
        original_features = [col for col in available_features 
                           if col not in ['ts_code', 'trade_date', self.target_factor]]
        
        new_features = []
        
        for feature in original_features:
            if feature not in df_clean.columns:
                print(f"  警告: 特征 {feature} 不存在，跳过")
                continue
            
            # 1) 时间维度排序：同标的60天内的排序分位数
            time_rank_col = f"{feature}_time_rank"
            df_clean[time_rank_col] = df_clean.groupby('ts_code')[feature].transform(
                lambda x: x.rolling(window=60, min_periods=5).rank(pct=True)
            )
            new_features.append(time_rank_col)
            
            # 2) 横截面维度排序：同交易日的排序分位数
            cross_rank_col = f"{feature}_cross_rank"
            df_clean[cross_rank_col] = df_clean.groupby('trade_date')[feature].transform(
                lambda x: x.rank(pct=True)
            )
            new_features.append(cross_rank_col)
        
        # 删除原始特征列
        columns_to_drop = [col for col in original_features if col in df_clean.columns]
        df_clean = df_clean.drop(columns=columns_to_drop)
        
        # 删除包含NaN的行（主要是时间序列排序产生的NaN）
        before_count = len(df_clean)
        df_clean = df_clean.dropna()
        after_count = len(df_clean)
        
        if before_count != after_count:
            print(f"  排序特征转换后清理数据: {before_count} -> {after_count} 条记录")
        
        print(f"  原始特征: {len(original_features)} -> 排序特征: {len(new_features)}")
        print(f"  时间维度特征: {len([f for f in new_features if '_time_rank' in f])}")
        print(f"  横截面特征: {len([f for f in new_features if '_cross_rank' in f])}")
        
        return df_clean
    
    def _calculate_sample_weights(self, df_filtered):
        """
        计算样本权重：使用通用的样本权重计算函数
        
        Args:
            df_filtered: 经过过滤的DataFrame，包含trade_date, ts_code和target_factor
            
        Returns:
            sample_weights: numpy数组，样本权重
        """
        return calculate_sample_weights(df_filtered, self.target_factor, 'trade_date')
    
    def train_model(self, X, y, sample_weights=None):
        """
        训练线性回归模型
        
        Args:
            X: 特征矩阵
            y: 目标变量
            sample_weights: 样本权重（可选）
            
        Returns:
            训练结果字典
        """
        if X is None or y is None:
            return None
            
        print(f"开始训练{self.regularization}线性回归模型...")
        if sample_weights is not None:
            print(f"使用样本权重训练，权重数量: {len(sample_weights)}")
        if self.use_top_mean_squarer:
            print("使用top_mean_squarer优化目标（专注于预测值前10%的样本）")

        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)

        # 验证数据有效性
        if np.any(np.isnan(X_scaled)) or np.any(np.isinf(X_scaled)):
            print("警告: 标准化后的特征矩阵包含NaN或无穷值")
            X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=1e6, neginf=-1e6)

        if np.any(np.isnan(y)) or np.any(np.isinf(y)):
            print("警告: 目标变量包含NaN或无穷值")
            y = np.nan_to_num(y, nan=0.0, posinf=1e6, neginf=-1e6)

        # 如果使用top_mean_squarer，需要使用自定义训练过程
        if self.use_top_mean_squarer:
            return self._train_with_top_mean_squarer(X_scaled, y, sample_weights)

        # 标准训练过程
        # 选择模型
        if self.regularization == 'ridge':
            self.model = Ridge(alpha=self.alpha, random_state=42)
        elif self.regularization == 'lasso':
            self.model = Lasso(alpha=self.alpha, random_state=42)
        else:
            self.model = LinearRegression()

        # 训练模型
        if sample_weights is not None:
            self.model.fit(X_scaled, y, sample_weight=sample_weights)
        else:
            self.model.fit(X_scaled, y)

        # 预测
        y_pred = self.model.predict(X_scaled)

        # 计算评估指标
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred)

        # 如果使用top_mean_squarer，也计算top_mean_squarer指标
        top_mse = None
        if self.use_top_mean_squarer:
            top_mse = top_mean_squarer(y, y_pred, sample_weight=sample_weights)
        
        # 交叉验证
        cv_scores = cross_val_score(
            self.model, X_scaled, y, 
            cv=TimeSeriesSplit(n_splits=5), 
            scoring='r2'
        )
        
        # 获取系数
        coefficients = self.model.coef_
        intercept = self.model.intercept_
        
        # 计算特征重要性（绝对值）
        feature_importance = np.abs(coefficients)
        feature_importance_normalized = feature_importance / np.sum(feature_importance)
        
        results = {
            'mse': mse,
            'r2': r2,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'coefficients': coefficients,
            'intercept': intercept,
            'feature_names': self.feature_names,
            'feature_importance': feature_importance_normalized,
            'n_samples': len(y)
        }

        # 添加top_mse指标（如果计算了的话）
        if top_mse is not None:
            results['top_mse'] = top_mse

        self.training_results = results

        print(f"训练完成:")
        print(f"  R2: {r2:.4f}")
        print(f"  MSE: {mse:.6f}")
        if top_mse is not None:
            print(f"  Top MSE (前10%): {top_mse:.6f}")
        print(f"  交叉验证R2: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        return results

    def _train_with_top_mean_squarer(self, X_scaled, y, sample_weights=None):
        """
        使用top_mean_squarer优化目标训练模型

        这个方法使用迭代优化来最小化top_mean_squarer损失函数

        Args:
            X_scaled: 标准化后的特征矩阵
            y: 目标变量
            sample_weights: 样本权重（可选）

        Returns:
            训练结果字典
        """
        from scipy.optimize import minimize

        print("使用top_mean_squarer优化目标进行训练...")

        # 初始化参数（使用标准线性回归的结果作为起点）
        if self.regularization == 'ridge':
            init_model = Ridge(alpha=self.alpha, random_state=42)
        elif self.regularization == 'lasso':
            init_model = Lasso(alpha=self.alpha, random_state=42)
        else:
            init_model = LinearRegression()

        if sample_weights is not None:
            init_model.fit(X_scaled, y, sample_weight=sample_weights)
        else:
            init_model.fit(X_scaled, y)

        # 初始参数
        initial_coef = init_model.coef_
        initial_intercept = init_model.intercept_
        initial_params = np.concatenate([initial_coef, [initial_intercept]])

        # 定义目标函数
        def objective(params):
            coef = params[:-1]
            intercept = params[-1]

            # 预测
            y_pred = X_scaled @ coef + intercept

            # 计算top_mean_squarer损失
            loss = top_mean_squarer(y, y_pred, sample_weight=sample_weights)

            # 添加正则化项
            if self.regularization == 'ridge':
                loss += self.alpha * np.sum(coef ** 2)
            elif self.regularization == 'lasso':
                loss += self.alpha * np.sum(np.abs(coef))

            return loss

        # 优化
        print("开始优化参数...")
        result = minimize(objective, initial_params, method='L-BFGS-B')

        if not result.success:
            print(f"优化警告: {result.message}")
            print("使用初始参数作为最终结果")
            optimized_params = initial_params
        else:
            optimized_params = result.x
            print(f"优化成功，迭代次数: {result.nit}")

        # 提取优化后的参数
        optimized_coef = optimized_params[:-1]
        optimized_intercept = optimized_params[-1]

        # 创建模型对象（用于预测）
        if self.regularization == 'ridge':
            self.model = Ridge(alpha=self.alpha, random_state=42)
        elif self.regularization == 'lasso':
            self.model = Lasso(alpha=self.alpha, random_state=42)
        else:
            self.model = LinearRegression()

        # 手动设置优化后的参数
        self.model.coef_ = optimized_coef
        self.model.intercept_ = optimized_intercept

        # 预测
        y_pred = X_scaled @ optimized_coef + optimized_intercept

        # 计算评估指标
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred)
        top_mse = top_mean_squarer(y, y_pred, sample_weight=sample_weights)

        # 交叉验证（使用标准模型进行交叉验证）
        cv_scores = cross_val_score(
            init_model, X_scaled, y,
            cv=TimeSeriesSplit(n_splits=5),
            scoring='r2'
        )

        # 计算特征重要性（绝对值）
        feature_importance = np.abs(optimized_coef)
        feature_importance_normalized = feature_importance / np.sum(feature_importance)

        results = {
            'mse': mse,
            'r2': r2,
            'top_mse': top_mse,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'coefficients': optimized_coef,
            'intercept': optimized_intercept,
            'feature_names': self.feature_names,
            'feature_importance': feature_importance_normalized,
            'n_samples': len(y),
            'optimization_success': result.success if 'result' in locals() else False
        }

        self.training_results = results

        print(f"top_mean_squarer训练完成:")
        print(f"  R2: {r2:.4f}")
        print(f"  MSE: {mse:.6f}")
        print(f"  Top MSE (前10%): {top_mse:.6f}")
        print(f"  交叉验证R2: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        return results

    def get_optimized_weights(self):
        """
        获取优化后的权重
        
        Returns:
            权重字典
        """
        if self.model is None or not self.training_results:
            print("模型未训练")
            return None
            
        # 使用特征重要性作为权重
        weights = {}
        for i, feature_name in enumerate(self.feature_names):
            weights[f'{feature_name}_zscore'] = self.training_results['feature_importance'][i]
            
        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            for key in weights:
                weights[key] /= total_weight
                
        return weights

    def predict_factors(self, factor_data):
        """
        使用训练好的模型预测因子值

        Args:
            factor_data: 包含因子数据的DataFrame

        Returns:
            预测结果数组
        """
        if self.model is None:
            print("模型未训练")
            return None

        # 检查特征是否存在
        missing_features = [f for f in self.feature_names if f not in factor_data.columns]
        if missing_features:
            print(f"缺少特征: {missing_features}")
            print(f"期望特征: {self.feature_names}")
            print(f"可用特征: {list(factor_data.columns)}")
            print(f"可用特征数量: {len(factor_data.columns)}")
            print(f"factor_data形状: {factor_data.shape}")
            # 检查期望特征中哪些是可用的
            available_expected = [f for f in self.feature_names if f in factor_data.columns]
            print(f"期望特征中可用的: {available_expected} (数量: {len(available_expected)})")
            return None

        # 准备特征矩阵
        print(f"准备提取特征: {self.feature_names}")
        print(f"从数据中提取特征，数据形状: {factor_data.shape}")
        X = factor_data[self.feature_names].values
        print(f"提取的特征矩阵形状: {X.shape}")
        
        # 确保数据是数值类型
        try:
            X = X.astype(float)
        except (ValueError, TypeError) as e:
            print(f"数据类型转换失败: {e}")
            return None
        
        # 处理NaN值 - 使用中位数填充
        if np.isnan(X).any():
            print(f"检测到NaN值，使用中位数填充...")
            print(f"NaN填充前X的形状: {X.shape}")
            
            # 检查每列的NaN情况
            nan_counts = np.isnan(X).sum(axis=0)
            print(f"每列NaN数量: {nan_counts}")
            print(f"每列NaN比例: {nan_counts / X.shape[0]}")
            
            # 检查是否有全NaN的列
            all_nan_cols = np.where(nan_counts == X.shape[0])[0]
            if len(all_nan_cols) > 0:
                print(f"发现全NaN列: {all_nan_cols}")
                print(f"对应特征: {[self.feature_names[i] for i in all_nan_cols]}")
            
            # 对于全NaN的列，先用0填充，然后再用中位数填充其他NaN值
            for i, col_nan_count in enumerate(nan_counts):
                if col_nan_count == X.shape[0]:  # 全NaN列
                    print(f"将全NaN列 {self.feature_names[i]} 填充为0")
                    X[:, i] = 0.0
            
            # 现在用中位数填充剩余的NaN值
            from sklearn.impute import SimpleImputer
            imputer = SimpleImputer(strategy='median')
            X = imputer.fit_transform(X)
            print(f"NaN填充后X的形状: {X.shape}")
            print(f"NaN值填充完成")

        # 标准化
        print(f"标准化前X的形状: {X.shape}")
        print(f"StandardScaler期望的特征数: {self.scaler.n_features_in_}")
        X_scaled = self.scaler.transform(X)
        print(f"标准化后X_scaled的形状: {X_scaled.shape}")

        # 预测
        predictions = self.model.predict(X_scaled)

        return predictions

    def create_combined_factor(self, factor_data, factor_name='linear_combined_factor'):
        """
        创建线性组合因子

        Args:
            factor_data: 包含因子数据的DataFrame
            factor_name: 组合因子的名称

        Returns:
            包含组合因子的DataFrame
        """
        if self.model is None:
            print("模型未训练，无法创建组合因子")
            return factor_data

        # 预测
        predictions = self.predict_factors(factor_data)

        if predictions is not None:
            factor_data = factor_data.copy()
            factor_data[factor_name] = predictions
            print(f"✅ 线性模型预测完成，创建临时因子: {factor_name}")

        return factor_data

    def get_factor_weights(self):
        """
        获取因子权重（基于模型系数）

        Returns:
            因子权重字典
        """
        if self.model is None or not self.training_results:
            print("模型未训练")
            return None

        coefficients = self.training_results['coefficients']

        # 计算权重（使用系数的绝对值）- 与训练时的计算逻辑完全一致
        abs_coefficients = np.abs(coefficients)
        weights = abs_coefficients / np.sum(abs_coefficients)

        weight_dict = {}
        for i, feature_name in enumerate(self.feature_names):
            weight_dict[feature_name] = {
                'weight': weights[i],  # 这与training_results['feature_importance'][i]数值相同
                'coefficient': coefficients[i],
                'abs_coefficient': abs_coefficients[i]
            }

        return weight_dict

    def print_feature_analysis(self):
        """打印特征分析结果"""
        if not self.training_results:
            print("模型未训练")
            return
            
        print("\n=== 特征分析结果 ===")
        print(f"{'特征名称':<25} {'系数':<12} {'重要性':<12} {'权重建议':<12}")
        print("-" * 65)
        
        coefficients = self.training_results['coefficients']
        importance = self.training_results['feature_importance']
        
        # 按重要性排序
        sorted_indices = np.argsort(importance)[::-1]
        
        for i in sorted_indices:
            feature_name = self.feature_names[i]
            coef = coefficients[i]
            imp = importance[i]
            weight = imp  # 使用重要性作为权重建议
            
            print(f"{feature_name:<25} {coef:<12.4f} {imp:<12.4f} {weight:<12.4f}")
    
    def compare_with_current_weights(self):
        """与当前权重进行比较"""
        # 当前权重（根据实际因子调整）
        current_weights = {
            'arbitrage_space_zscore': 0.50,
            'momentum_5d_zscore': 0.25,
            'momentum_10d_zscore': 0.15,
            'momentum_20d_zscore': 0.10
        }
        
        optimized_weights = self.get_optimized_weights()
        
        if optimized_weights is None:
            return
            
        print("\n=== 权重对比分析 ===")
        print(f"{'特征名称':<30} {'当前权重':<12} {'优化权重':<12} {'变化':<12}")
        print("-" * 70)
        
        for feature_name in self.feature_names:
            key = f'{feature_name}_zscore'
            current = current_weights.get(key, 0.0)
            optimized = optimized_weights.get(key, 0.0)
            change = optimized - current
            
            print(f"{feature_name:<30} {current:<12.4f} {optimized:<12.4f} {change:<12.4f}")


def train_factor_regression(train_start, train_end, regularization='ridge', alpha=1.0,
                          model_name='default', factor_config=None, target_factor='next_return',
                          return_days=1, custom_factors=None, save_model=True, factor_config_name='default',
                          use_sample_weights=False, sorted_feature=False, use_top_mean_squarer=False):
    """
    训练因子回归模型（增强版）

    Args:
        train_start: 训练开始日期
        train_end: 训练结束日期
        regularization: 正则化方法
        alpha: 正则化强度
        model_name: 模型名称
        factor_config: 因子配置字典
        target_factor: 目标因子名称
        return_days: 收益率计算天数
        custom_factors: 自定义因子列表
        save_model: 是否保存模型
        factor_config_name: 因子配置名称 ('default' 或 'best_ten')
        use_sample_weights: 是否使用样本权重训练
        sorted_feature: 是否使用排序特征（将特征转换为时间和横截面排序分位数）
        use_top_mean_squarer: 是否使用top_mean_squarer优化目标（专注于预测值前10%的样本）

    Returns:
        训练好的模型
    """
    print(f"=== 增强版因子线性回归训练 ===")
    print(f"训练期间: {train_start} - {train_end}")
    print(f"正则化方法: {regularization}, alpha: {alpha}")
    print(f"模型名称: {model_name}")
    print(f"目标因子: {target_factor}, 收益率天数: {return_days}")
    print(f"因子配置: {factor_config_name}")
    print(f"排序特征: {'启用' if sorted_feature else '禁用'}")
    print(f"top_mean_squarer优化: {'启用' if use_top_mean_squarer else '禁用'}")

    # 根据配置名称获取因子配置
    if factor_config is None:
        # 直接使用类方法获取因子配置，避免创建临时实例
        temp_model = FactorLinearRegression.__new__(FactorLinearRegression)
        temp_model.default_factor_config = {
            'zl_factors': ['arbitrage_space', 'model_delta', 'corr_coef', 'reg_slope'],
            'momentum_factors': ['momentum_5d', 'momentum_10d', 'momentum_20d'],
            'double_low_factors': ['price_factor', 'premium_factor', 'double_low_score'],
            'technical_factors': ['rsi_6d', 'rsi_12d', 'macd', 'vol_change'],
            'fundamental_factors': ['bond_floor', 'option_value']
        }
        temp_model.best_ten_factor_config = {
            'best_ten_factors': [
                'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
                'volatility_10d', 'option_value', 'volatility_5d', 'gain_from_low',
                'conv_value', 'bb_width'
            ]
        }
        temp_model.best_five_factor_config = {
            'best_five_factors': [
                'price_factor', 'atr_14d', 'avg_turnover_20d', 'volatility_20d',
                'volatility_10d'
            ]
        }
        temp_model.best_ret_10_factor_config = {
            'best_ret_10_factors': [
                'trend_slope_5d', 'trend_slope_10d', 'price_factor', 'arbitrage_space',
                'momentum_5d', 'bb_deviation', 'bb_position', 'williams_r',
                'amihud_illiquidity', 'corr_coef'
            ]
        }
        factor_config = temp_model.get_factor_config_by_name(factor_config_name)
        print(f"使用预设因子配置: {factor_config_name}")
        print(f"包含因子组: {list(factor_config.keys())}")

    # 创建模型
    model = FactorLinearRegression(
        regularization=regularization,
        alpha=alpha,
        model_name=model_name,
        factor_config=factor_config,
        target_factor=target_factor,
        return_days=return_days,
        use_sample_weights=use_sample_weights,
        sorted_feature=sorted_feature,
        factor_config_name=factor_config_name,  # 传递配置名称
        use_top_mean_squarer=use_top_mean_squarer  # 传递top_mean_squarer参数
    )

    # 准备数据
    if use_sample_weights:
        X, y, sample_weights = model.prepare_training_data(train_start, train_end, custom_factors)
        if X is None:
            print("数据准备失败")
            return None
        # 训练模型
        results = model.train_model(X, y, sample_weights)
    else:
        X, y = model.prepare_training_data(train_start, train_end, custom_factors)
        if X is None:
            print("数据准备失败")
            return None
        # 训练模型
        results = model.train_model(X, y)

    if results is None:
        print("模型训练失败")
        return None

    # 分析结果
    model.print_feature_analysis()

    # 保存模型
    if save_model:
        model.save_model()

    return model


def test_different_regularizations(train_start, train_end):
    """测试不同正则化方法的效果"""
    print("=== 测试不同正则化方法 ===\n")
    
    methods = [
        ('none', 0.0),
        ('ridge', 0.1),
        ('ridge', 1.0),
        ('ridge', 10.0),
        ('lasso', 0.1),
        ('lasso', 1.0)
    ]
    
    results = {}
    
    for method, alpha in methods:
        print(f"\n{'='*50}")
        print(f"测试方法: {method}, alpha: {alpha}")
        print(f"{'='*50}")
        
        model = train_factor_regression(train_start, train_end, method, alpha, return_days=1)
        
        if model and model.training_results:
            results[f"{method}_{alpha}"] = {
                'model': model,
                'r2': model.training_results['r2'],
                'cv_mean': model.training_results['cv_mean'],
                'cv_std': model.training_results['cv_std']
            }
    
    # 比较结果
    print("\n" + "="*80)
    print("不同正则化方法效果对比")
    print("="*80)
    print(f"{'方法':<15} {'R2':<10} {'交叉验证R2':<15} {'CV标准差':<12}")
    print("-" * 55)
    
    for method_name, result in results.items():
        r2 = result['r2']
        cv_mean = result['cv_mean']
        cv_std = result['cv_std']
        print(f"{method_name:<15} {r2:<10.4f} {cv_mean:<15.4f} {cv_std:<12.4f}")
    
    # 选择最佳方法
    best_method = max(results.items(), key=lambda x: x[1]['cv_mean'])
    print(f"\n最佳方法: {best_method[0]} (交叉验证R2: {best_method[1]['cv_mean']:.4f})")
    
    return results


def load_and_predict(model_name, factor_data, factor_name='linear_combined_factor'):
    """
    加载模型并进行预测

    Args:
        model_name: 模型名称
        factor_data: 因子数据DataFrame
        factor_name: 组合因子名称

    Returns:
        包含预测结果的DataFrame
    """
    # 创建模型实例
    model = FactorLinearRegression(model_name=model_name)

    # 加载模型
    if not model.load_model():
        print(f"加载模型失败: {model_name}")
        return factor_data

    # 创建组合因子
    result_data = model.create_combined_factor(factor_data, factor_name)

    return result_data


def list_models():
    """列出所有可用的模型"""
    model = FactorLinearRegression()
    return model.list_available_models()


def create_factor_config(zl_factors=None, momentum_factors=None, double_low_factors=None,
                        technical_factors=None, fundamental_factors=None):
    """
    创建因子配置

    Args:
        zl_factors: ZL模型因子列表
        momentum_factors: 动量因子列表
        double_low_factors: 双低因子列表
        technical_factors: 技术因子列表
        fundamental_factors: 基本面因子列表

    Returns:
        因子配置字典
    """
    config = {}

    if zl_factors:
        config['zl_factors'] = zl_factors
    if momentum_factors:
        config['momentum_factors'] = momentum_factors
    if double_low_factors:
        config['double_low_factors'] = double_low_factors
    if technical_factors:
        config['technical_factors'] = technical_factors
    if fundamental_factors:
        config['fundamental_factors'] = fundamental_factors

    return config


if __name__ == "__main__":
    # 使用2020-2021年作为训练集
    train_start = '20240101'  # 由于只有2024年数据，先用2024年前半年
    train_end = '20240630'
    
    # 测试不同正则化方法
    results = test_different_regularizations(train_start, train_end)
