#!/usr/bin/env python3
"""
基于FT-Transformer的因子预测器
实现与AutoGluon FT_TRANSFORMER完全一致的功能，但不依赖AutoGluon
使用top_mean_squarer作为损失函数
"""

import pandas as pd
import numpy as np
import sqlite3
import pickle
import json
import warnings
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, LabelEncoder

from factor.unified_feature_preprocessor import create_unified_preprocessor

warnings.filterwarnings('ignore')

# 导入基类和共享工具
from .base_predictor import (
    BasePredictor, top_mean_squarer, calculate_cross_sectional_r2,
    _split_data_by_bond, get_factor_config_by_name, UNIFIED_PREPROCESSING,
    calculate_sample_weights, add_sample_weights_to_dataframe, config_manager
)


class TabularDataset(Dataset):
    """表格数据集类，用于PyTorch训练"""
    
    def __init__(self, features: np.ndarray, targets: np.ndarray, sample_weights: Optional[np.ndarray] = None):
        """
        初始化数据集
        
        Args:
            features: 特征矩阵 (n_samples, n_features)
            targets: 目标变量 (n_samples,)
            sample_weights: 样本权重 (n_samples,)，可选
        """
        self.features = torch.FloatTensor(features)
        self.targets = torch.FloatTensor(targets)
        self.sample_weights = torch.FloatTensor(sample_weights) if sample_weights is not None else None
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        item = {
            'features': self.features[idx],
            'targets': self.targets[idx]
        }
        if self.sample_weights is not None:
            item['sample_weights'] = self.sample_weights[idx]
        return item


class FTTransformer(nn.Module):
    """
    FT-Transformer模型实现
    基于"Revisiting Deep Learning Models for Tabular Data"论文
    """
    
    def __init__(self, n_features: int, d_model: int = 192, n_heads: int = 8, 
                 n_layers: int = 3, dropout: float = 0.2, d_ffn_factor: float = 8/3):
        """
        初始化FT-Transformer
        
        Args:
            n_features: 输入特征数量
            d_model: 模型维度
            n_heads: 注意力头数
            n_layers: Transformer层数
            dropout: Dropout率
            d_ffn_factor: FFN维度因子
        """
        super().__init__()
        
        self.n_features = n_features
        self.d_model = d_model
        
        # 特征嵌入层
        self.feature_embeddings = nn.ModuleList([
            nn.Linear(1, d_model) for _ in range(n_features)
        ])
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, d_model))
        
        # 位置编码
        self.position_embeddings = nn.Parameter(torch.randn(1, n_features + 1, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=int(d_model * d_ffn_factor),
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出层
        self.output_norm = nn.LayerNorm(d_model)
        self.output_linear = nn.Linear(d_model, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 (batch_size, n_features)
            
        Returns:
            预测结果 (batch_size, 1)
        """
        batch_size = x.size(0)
        
        # 特征嵌入
        embeddings = []
        for i, embedding_layer in enumerate(self.feature_embeddings):
            feature_value = x[:, i:i+1]  # (batch_size, 1)
            feature_emb = embedding_layer(feature_value)  # (batch_size, d_model)
            feature_emb = feature_emb.unsqueeze(1)  # (batch_size, 1, d_model)
            embeddings.append(feature_emb)
        
        # 堆叠特征嵌入
        embeddings = torch.cat(embeddings, dim=1)  # (batch_size, n_features, d_model)
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)  # (batch_size, 1, d_model)
        embeddings = torch.cat([cls_tokens, embeddings], dim=1)  # (batch_size, n_features+1, d_model)
        
        # 添加位置编码
        embeddings = embeddings + self.position_embeddings
        
        # Transformer编码
        encoded = self.transformer(embeddings)  # (batch_size, n_features+1, d_model)
        
        # 使用CLS token的输出
        cls_output = encoded[:, 0, :]  # (batch_size, d_model)
        
        # 输出层
        output = self.output_norm(cls_output)
        output = self.dropout(output)
        output = self.output_linear(output)  # (batch_size, 1)
        
        return output.squeeze(-1)  # (batch_size,)


class TopMeanSquaredLoss(nn.Module):
    """Top Mean Squared Loss - 只对预测值前10%的样本计算损失"""
    
    def __init__(self):
        super().__init__()
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor, 
                sample_weights: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算top mean squared loss
        
        Args:
            predictions: 预测值 (batch_size,)
            targets: 真实值 (batch_size,)
            sample_weights: 样本权重 (batch_size,)，可选
            
        Returns:
            损失值
        """
        # 按预测值降序排序
        sorted_indices = torch.argsort(predictions, descending=True)
        cutoff = int(torch.ceil(torch.tensor(len(predictions) * 0.1)).item())
        top_indices = sorted_indices[:cutoff]
        
        # 选择前10%的样本
        top_predictions = predictions[top_indices]
        top_targets = targets[top_indices]
        
        # 计算MSE
        if sample_weights is not None:
            top_weights = sample_weights[top_indices]
            # 加权MSE
            weighted_diff = top_weights * (top_predictions - top_targets) ** 2
            loss = torch.sum(weighted_diff) / torch.sum(top_weights)
        else:
            loss = torch.mean((top_predictions - top_targets) ** 2)
            
        return loss


class FTTransformerPredictor(BasePredictor):
    """
    基于FT-Transformer的因子预测器
    与AutoGluon FT_TRANSFORMER功能完全一致
    """
    
    def __init__(self, model_name: str = 'default', target_days: int = 1,
                 feature_config: Optional[Dict] = None, preprocessing_config: Optional[str] = None,
                 create_unified_preprocessor_now: bool = True, use_sample_weights: bool = False,
                 sorted_feature: bool = False, device: Optional[str] = None):
        """
        初始化FT-Transformer预测器

        Args:
            model_name: 模型名称
            target_days: 预测天数
            feature_config: 特征配置字典
            preprocessing_config: 预处理配置
            create_unified_preprocessor_now: 是否立即创建预处理器
            use_sample_weights: 是否使用样本权重
            sorted_feature: 是否使用排序特征
            device: 计算设备 ('cpu', 'cuda', 'mps')
        """
        # 调用父类初始化
        preprocessing_config = preprocessing_config or 'no_imputation'
        super().__init__(
            model_name=model_name,
            target_days=target_days,
            feature_config=feature_config,
            preprocessing_config=preprocessing_config,
            create_unified_preprocessor_now=create_unified_preprocessor_now,
            use_sample_weights=use_sample_weights,
            sorted_feature=sorted_feature
        )

        # FT-Transformer特有的属性
        # 设备设置
        if device is None:
            if torch.cuda.is_available():
                self.device = torch.device('cuda')
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                self.device = torch.device('mps')
            else:
                self.device = torch.device('cpu')
        else:
            self.device = torch.device(device)

        print(f"使用计算设备: {self.device}")

        # 模型相关
        self.model = None
        self.scaler = StandardScaler()

        print(f"FT-Transformer因子预测器初始化 - 模型: {model_name}, 预测天数: {target_days}")

    def _get_models_dir(self) -> Path:
        """获取FT-Transformer模型保存目录"""
        return Path('models/ft_transformer')
        
        self.training_config = {
            'batch_size': 256,
            'num_workers': 4,
            'max_epochs': 100,
            'weight_decay': 1e-4,
            'lr': 1e-3,
            'warmup_steps': 0.1,
            'patience': 10,
            'd_model': 192,
            'n_heads': 8,
            'n_layers': 3,
            'dropout': 0.2
        }
        
        print(f"FT-Transformer预测器初始化完成 - 模型: {model_name}, 预测天数: {target_days}")
        print(f"训练配置: batch_size={self.training_config['batch_size']}, "
              f"max_epochs={self.training_config['max_epochs']}, "
              f"lr={self.training_config['lr']}")
    

    

    
    def train_model(self, train_data: pd.DataFrame, time_limit: int = 300, 
                   presets: str = 'medium_quality', tuning_data: Optional[pd.DataFrame] = None):
        """
        训练FT-Transformer模型
        
        Args:
            train_data: 训练数据
            time_limit: 训练时间限制（秒）
            presets: 模型预设（保持接口一致性）
            tuning_data: 调优数据
            
        Returns:
            训练结果字典
        """
        if train_data is None or train_data.empty:
            print("没有训练数据")
            return None
        
        print(f"开始训练FT-Transformer模型...")
        
        try:
            # 准备训练数据
            required_columns = self.feature_names + [self.target_name]
            
            # 处理样本权重
            sample_weight_col = None
            if self.use_sample_weights and calculate_sample_weights is not None:
                print("正在计算样本权重...")
                if 'trade_date' in train_data.columns and 'ts_code' in train_data.columns:
                    train_data_with_weights = add_sample_weights_to_dataframe(
                        train_data, self.target_name, 'trade_date', 'sample_weight'
                    )
                    sample_weight_col = 'sample_weight'
                    required_columns.append(sample_weight_col)
                    train_features = train_data_with_weights[required_columns].copy()
                    print(f"已添加样本权重列: {sample_weight_col}")
                else:
                    print("警告: 缺少trade_date或ts_code列，无法计算样本权重")
                    train_features = train_data[required_columns].copy()
            else:
                train_features = train_data[required_columns].copy()
            
            # 清理数据
            before_count = len(train_features)
            train_features = train_features.dropna()
            after_count = len(train_features)
            
            if before_count != after_count:
                print(f"清理训练数据: {before_count} -> {after_count}")
            
            # 准备特征和目标
            X_train = train_features[self.feature_names].values
            y_train = train_features[self.target_name].values
            sample_weights_train = None
            
            if sample_weight_col is not None:
                sample_weights_train = train_features[sample_weight_col].values
            
            # 标准化特征
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 准备调优数据
            X_val_scaled = None
            y_val = None
            sample_weights_val = None
            
            if tuning_data is not None and not tuning_data.empty:
                tuning_required_columns = self.feature_names + [self.target_name]
                
                if sample_weight_col is not None:
                    if 'trade_date' in tuning_data.columns and 'ts_code' in tuning_data.columns:
                        tuning_data_with_weights = add_sample_weights_to_dataframe(
                            tuning_data, self.target_name, 'trade_date', 'sample_weight'
                        )
                        tuning_required_columns.append(sample_weight_col)
                        tuning_features = tuning_data_with_weights[tuning_required_columns].copy()
                    else:
                        tuning_features = tuning_data[tuning_required_columns].copy()
                else:
                    tuning_features = tuning_data[tuning_required_columns].copy()
                
                tuning_features = tuning_features.dropna()
                
                if not tuning_features.empty:
                    X_val = tuning_features[self.feature_names].values
                    y_val = tuning_features[self.target_name].values
                    X_val_scaled = self.scaler.transform(X_val)
                    
                    if sample_weight_col is not None and sample_weight_col in tuning_features.columns:
                        sample_weights_val = tuning_features[sample_weight_col].values
                    
                    print(f"使用调优数据: {len(tuning_features)} 条记录")
            
            # 创建数据集
            train_dataset = TabularDataset(X_train_scaled, y_train, sample_weights_train)
            train_loader = DataLoader(
                train_dataset, 
                batch_size=self.training_config['batch_size'], 
                shuffle=True,
                num_workers=0  # 设为0避免多进程问题
            )
            
            val_loader = None
            if X_val_scaled is not None:
                val_dataset = TabularDataset(X_val_scaled, y_val, sample_weights_val)
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.training_config['batch_size'],
                    shuffle=False,
                    num_workers=0
                )
            
            # 创建模型
            n_features = len(self.feature_names)
            self.model = FTTransformer(
                n_features=n_features,
                d_model=self.training_config['d_model'],
                n_heads=self.training_config['n_heads'],
                n_layers=self.training_config['n_layers'],
                dropout=self.training_config['dropout']
            ).to(self.device)
            
            # 损失函数和优化器
            criterion = TopMeanSquaredLoss()
            optimizer = optim.AdamW(
                self.model.parameters(), 
                lr=self.training_config['lr'],
                weight_decay=self.training_config['weight_decay']
            )
            
            # 学习率调度器
            total_steps = len(train_loader) * self.training_config['max_epochs']
            warmup_steps = int(total_steps * self.training_config['warmup_steps'])
            
            def lr_lambda(step):
                if step < warmup_steps:
                    return step / warmup_steps
                else:
                    return max(0.1, (total_steps - step) / (total_steps - warmup_steps))
            
            scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
            
            # 早停
            best_val_loss = float('inf')
            patience_counter = 0
            best_model_state = None
            
            # 训练循环
            train_losses = []
            val_losses = []
            
            print(f"开始训练，最大epoch: {self.training_config['max_epochs']}")
            
            for epoch in range(self.training_config['max_epochs']):
                # 训练阶段
                self.model.train()
                epoch_train_loss = 0.0
                
                for batch in train_loader:
                    features = batch['features'].to(self.device)
                    targets = batch['targets'].to(self.device)
                    weights = batch.get('sample_weights')
                    if weights is not None:
                        weights = weights.to(self.device)
                    
                    optimizer.zero_grad()
                    predictions = self.model(features)
                    loss = criterion(predictions, targets, weights)
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    scheduler.step()
                    
                    epoch_train_loss += loss.item()
                
                avg_train_loss = epoch_train_loss / len(train_loader)
                train_losses.append(avg_train_loss)
                
                # 验证阶段
                if val_loader is not None:
                    self.model.eval()
                    epoch_val_loss = 0.0
                    
                    with torch.no_grad():
                        for batch in val_loader:
                            features = batch['features'].to(self.device)
                            targets = batch['targets'].to(self.device)
                            weights = batch.get('sample_weights')
                            if weights is not None:
                                weights = weights.to(self.device)
                            
                            predictions = self.model(features)
                            loss = criterion(predictions, targets, weights)
                            epoch_val_loss += loss.item()
                    
                    avg_val_loss = epoch_val_loss / len(val_loader)
                    val_losses.append(avg_val_loss)
                    
                    # 早停检查
                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        patience_counter = 0
                        best_model_state = self.model.state_dict().copy()
                    else:
                        patience_counter += 1
                    
                    if epoch % 10 == 0:
                        print(f"Epoch {epoch}: Train Loss = {avg_train_loss:.6f}, Val Loss = {avg_val_loss:.6f}")
                    
                    # 早停
                    if patience_counter >= self.training_config['patience']:
                        print(f"早停在epoch {epoch}，验证损失未改善")
                        break
                else:
                    if epoch % 10 == 0:
                        print(f"Epoch {epoch}: Train Loss = {avg_train_loss:.6f}")
            
            # 恢复最佳模型
            if best_model_state is not None:
                self.model.load_state_dict(best_model_state)
            
            # 评估模型
            train_predictions = self._predict_dataset(train_loader)
            train_mse = mean_squared_error(y_train, train_predictions)
            
            # 计算横截面R²
            if 'trade_date' in train_data.columns:
                train_eval_data = pd.DataFrame({
                    'trade_date': train_data.loc[train_features.index, 'trade_date'],
                    'y_true': y_train,
                    'y_pred': train_predictions
                })
                train_r2 = calculate_cross_sectional_r2(train_eval_data, 'y_true', 'y_pred', 'trade_date')
            else:
                train_r2 = r2_score(y_train, train_predictions)
            
            self.training_results = {
                'train_mse': train_mse,
                'train_rmse': np.sqrt(train_mse),
                'train_r2': train_r2,
                'train_samples': len(train_dataset),
                'train_losses': train_losses,
                'val_losses': val_losses,
                'epochs_trained': epoch + 1
            }
            
            # 如果有验证数据，也进行评估
            if val_loader is not None:
                val_predictions = self._predict_dataset(val_loader)
                val_mse = mean_squared_error(y_val, val_predictions)
                
                if 'trade_date' in tuning_data.columns:
                    val_eval_data = pd.DataFrame({
                        'trade_date': tuning_data.loc[tuning_features.index, 'trade_date'],
                        'y_true': y_val,
                        'y_pred': val_predictions
                    })
                    val_r2 = calculate_cross_sectional_r2(val_eval_data, 'y_true', 'y_pred', 'trade_date')
                else:
                    val_r2 = r2_score(y_val, val_predictions)
                
                self.training_results.update({
                    'tuning_mse': val_mse,
                    'tuning_rmse': np.sqrt(val_mse),
                    'tuning_r2': val_r2,
                    'tuning_samples': len(val_dataset)
                })
                
                print(f"FT-Transformer模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_dataset)}")
                print(f"  调优集 - RMSE: {np.sqrt(val_mse):.6f}, R²: {val_r2:.4f}, 样本数: {len(val_dataset)}")
            else:
                print(f"FT-Transformer模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_dataset)}")
            
            # 保存模型
            self._save_model()
            
            return self.training_results
            
        except Exception as e:
            print(f"FT-Transformer模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _predict_dataset(self, data_loader: DataLoader) -> np.ndarray:
        """对数据集进行预测"""
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for batch in data_loader:
                features = batch['features'].to(self.device)
                pred = self.model(features)
                predictions.extend(pred.cpu().numpy())
        
        return np.array(predictions)
    
    def predict(self, factor_data: pd.DataFrame) -> Optional[np.ndarray]:
        """
        使用训练好的模型进行预测
        
        Args:
            factor_data: 因子数据DataFrame
            
        Returns:
            预测结果数组
        """
        if factor_data is None or factor_data.empty:
            return None
        
        if self.model is None:
            print("模型未训练")
            return None
        
        # 应用与训练时相同的预处理（重用autogluon_predictor的逻辑）
        processed_data = factor_data.copy()
        
        if self.preprocessor is not None and UNIFIED_PREPROCESSING:
            try:
                # 识别基础特征
                engineered_suffixes = ['_x_', '_div_', '_squared', '_lag1', '_ma3']
                rank_suffixes = ['_time_rank', '_cross_rank']
                
                if self.sorted_feature:
                    base_features = []
                    for f in self.feature_names:
                        if '_time_rank' in f:
                            base_feature = f.replace('_time_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)
                        elif '_cross_rank' in f:
                            base_feature = f.replace('_cross_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)
                    
                    core_base_features = []
                    for feature in base_features:
                        is_engineered = any(suffix in feature for suffix in engineered_suffixes)
                        if not is_engineered:
                            core_base_features.append(feature)
                        else:
                            for suffix in engineered_suffixes:
                                if suffix in feature:
                                    if suffix == '_x_':
                                        parts = feature.split('_x_')
                                        for part in parts:
                                            if part and part not in core_base_features:
                                                core_base_features.append(part)
                                    else:
                                        core_feature = feature.split(suffix)[0]
                                        if core_feature and core_feature not in core_base_features:
                                            core_base_features.append(core_feature)
                                    break
                    
                    available_base_features = [f for f in core_base_features if f in processed_data.columns]
                else:
                    base_features = [f for f in self.feature_names 
                                   if not any(suffix in f for suffix in engineered_suffixes)]
                    available_base_features = [f for f in base_features if f in processed_data.columns]
                
                processed_data = self.preprocessor.preprocess_features(
                    processed_data,
                    available_base_features,
                    is_training=False
                )
            except Exception as e:
                print(f"⚠️  推理预处理失败: {e}，使用原始数据")
                processed_data = factor_data.copy()
        
        # 排序特征转换（如果需要）
        if self.sorted_feature:
            try:
                print(f"🔄 应用排序特征转换（推理模式）...")
                if 'ts_code' not in processed_data.columns or 'trade_date' not in processed_data.columns:
                    if 'ts_code' not in processed_data.columns:
                        processed_data['ts_code'] = 'unknown'
                    if 'trade_date' not in processed_data.columns:
                        processed_data['trade_date'] = '20240101'
                
                available_features = [col for col in processed_data.columns 
                                    if col not in ['ts_code', 'trade_date', self.target_name]]
                
                processed_data = self._apply_sorted_feature_transformation_for_prediction(processed_data, available_features)
                
                if processed_data.empty:
                    print(f"⚠️  排序特征转换后数据为空，无法进行预测")
                    return None
                    
                print(f"排序特征转换完成（推理模式）: {len(processed_data)} 条记录")
            except Exception as e:
                print(f"⚠️  排序特征转换失败: {e}")
                return None
        
        # 检查特征是否存在
        missing_features = [f for f in self.feature_names if f not in processed_data.columns]
        if missing_features:
            print(f"缺少特征: {missing_features[:10]}{'...' if len(missing_features) > 10 else ''}")
            return None
        
        try:
            # 准备特征矩阵
            X = processed_data[self.feature_names].values
            
            # 检查并处理NaN值
            if np.isnan(X).any():
                print(f"⚠️  检测到 {np.isnan(X).sum()} 个NaN值，进行填充处理")
                # 使用训练时的scaler的均值填充NaN（如果可用）
                nan_mask = np.isnan(X)
                if hasattr(self.scaler, 'mean_') and self.scaler.mean_ is not None:
                    # 对于每个特征，用该特征的训练时均值填充
                    for i in range(X.shape[1]):
                        if nan_mask[:, i].any():
                            X[nan_mask[:, i], i] = self.scaler.mean_[i]
                else:
                    # 如果没有训练时的均值信息，用0填充
                    X[nan_mask] = 0.0
                print(f"✅ NaN值填充完成")
            
            X_scaled = self.scaler.transform(X)
            
            # 再次检查缩放后的数据
            if np.isnan(X_scaled).any() or np.isinf(X_scaled).any():
                print(f"⚠️  缩放后检测到异常值，进行清理")
                X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # 创建数据集和数据加载器
            dataset = TabularDataset(X_scaled, np.zeros(len(X_scaled)))  # 目标变量用占位符
            data_loader = DataLoader(dataset, batch_size=self.training_config['batch_size'], shuffle=False, num_workers=0)
            
            # 预测
            predictions = self._predict_dataset(data_loader)
            
            return predictions
            
        except Exception as e:
            print(f"FT-Transformer预测失败: {e}")
            return None
    

    
    def _save_model(self):
        """保存模型"""
        try:
            model_path = self.models_dir / f"{self.model_name}_ft_transformer.pth"
            
            # 保存模型状态
            model_data = {
                'model_state_dict': self.model.state_dict(),
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'training_results': self.training_results,
                'training_config': self.training_config,
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name,
                'preprocessing_config': self.preprocessing_config,
                'sorted_feature': self.sorted_feature,
                'n_features': len(self.feature_names),
                'save_time': datetime.now().isoformat()
            }
            
            torch.save(model_data, model_path)
            print(f"模型已保存: {model_path}")
            
            # 保存预处理器状态
            if self.preprocessor is not None and UNIFIED_PREPROCESSING:
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                with open(preprocessor_path, 'wb') as f:
                    pickle.dump(self.preprocessor, f)
                print(f"预处理器状态已保存: {preprocessor_path}")
            
            # 保存元数据
            metadata_path = self.models_dir / f"{self.model_name}_metadata.json"
            metadata = {
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name,
                'feature_names': self.feature_names,
                'preprocessing_config': self.preprocessing_config,
                'sorted_feature': self.sorted_feature,
                'training_config': self.training_config,
                'save_time': datetime.now().isoformat()
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            print(f"元数据已保存: {metadata_path}")
            
        except Exception as e:
            print(f"保存模型失败: {e}")
    
    def load_model(self, model_name: Optional[str] = None) -> bool:
        """
        加载已训练的模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            是否加载成功
        """
        if model_name:
            self.model_name = model_name
        
        try:
            model_path = self.models_dir / f"{self.model_name}_ft_transformer.pth"
            
            if not model_path.exists():
                print(f"模型文件不存在: {model_path}")
                return False
            
            # 加载模型数据
            model_data = torch.load(model_path, map_location=self.device, weights_only=False)
            
            # 恢复模型参数
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.training_results = model_data['training_results']
            self.training_config = model_data['training_config']
            self.target_days = model_data['target_days']
            self.target_name = model_data['target_name']
            self.preprocessing_config = model_data['preprocessing_config']
            self.sorted_feature = model_data['sorted_feature']
            
            # 重建模型
            n_features = model_data['n_features']
            self.model = FTTransformer(
                n_features=n_features,
                d_model=self.training_config['d_model'],
                n_heads=self.training_config['n_heads'],
                n_layers=self.training_config['n_layers'],
                dropout=self.training_config['dropout']
            ).to(self.device)
            
            self.model.load_state_dict(model_data['model_state_dict'])
            self.model.eval()
            
            # 加载预处理器状态
            if UNIFIED_PREPROCESSING:
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                if preprocessor_path.exists():
                    with open(preprocessor_path, 'rb') as f:
                        self.preprocessor = pickle.load(f)
                    print(f"✅ 预处理器状态已加载: {preprocessor_path}")
                else:
                    print(f"⚠️  未找到预处理器状态文件，重新创建")
                    self.preprocessor = create_unified_preprocessor(self.preprocessing_config)
            
            print(f"FT-Transformer模型加载成功: {self.model_name}")
            print(f"  特征数量: {len(self.feature_names)}")
            print(f"  排序特征模式: {'启用' if self.sorted_feature else '禁用'}")
            
            return True
            
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    

    
    def list_available_models(self):
        """列出所有可用的FT-Transformer模型"""
        model_files = list(self.models_dir.glob("*_ft_transformer.pth"))
        metadata_files = list(self.models_dir.glob("*_metadata.json"))
        
        if not model_files:
            print("没有找到已保存的FT-Transformer模型")
            return []
        
        print(f"\n可用FT-Transformer模型列表 (共{len(model_files)}个):")
        print("-" * 80)
        print(f"{'模型名称':<25} {'保存时间':<20} {'预测天数':<10} {'特征数':<8} {'R²':<8}")
        print("-" * 80)
        
        models_info = []
        for model_file in sorted(model_files):
            try:
                model_name = model_file.stem.replace('_ft_transformer', '')
                metadata_file = self.models_dir / f"{model_name}_metadata.json"
                
                if metadata_file.exists():
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    save_time = metadata.get('save_time', 'Unknown')[:19]
                    target_days = metadata.get('target_days', 'N/A')
                    feature_count = len(metadata.get('feature_names', []))
                    
                    # 尝试获取R²值
                    r2_value = 0.0
                    try:
                        # 加载模型数据获取训练结果
                        model_data = torch.load(model_file, map_location='cpu', weights_only=False)
                        training_results = model_data.get('training_results', {})
                        r2_value = training_results.get('train_r2', training_results.get('r2', 0.0))
                    except:
                        r2_value = 0.0
                    
                    print(f"{model_name:<25} {save_time:<20} {target_days:<10} {feature_count:<8} {r2_value:<8.4f}")
                    
                    models_info.append({
                        'name': model_name,
                        'path': model_file,
                        'save_time': save_time,
                        'target_days': target_days,
                        'feature_count': feature_count,
                        'r2': r2_value
                    })
                else:
                    print(f"{model_name:<25} {'No metadata':<20} {'N/A':<10} {'N/A':<8} {'N/A':<8}")
            except Exception as e:
                print(f"{model_file.stem:<25} {'Error':<20} {'Error':<10} {'N/A':<8} {'N/A':<8}")
        
        return models_info


def train_ft_transformer_predictor(train_start: str, train_end: str, model_name: str = 'default', 
                                  target_days: int = 1, time_limit: int = 300, presets: str = 'medium_quality',
                                  custom_features: Optional[List[str]] = None, 
                                  preprocessing_config: str = 'no_imputation',
                                  tuning_ratio: float = 0.1, use_sample_weights: bool = False, 
                                  sorted_feature: bool = False, device: Optional[str] = None):
    """
    训练FT-Transformer预测器的便捷函数
    
    Args:
        train_start: 训练开始日期
        train_end: 训练结束日期
        model_name: 模型名称
        target_days: 预测天数
        time_limit: 训练时间限制（秒）
        presets: 模型预设（保持接口一致性）
        custom_features: 自定义特征列表
        preprocessing_config: 预处理配置
        tuning_ratio: tuning data占总数据的比例
        use_sample_weights: 是否使用样本权重
        sorted_feature: 是否使用排序特征
        device: 计算设备
        
    Returns:
        训练好的预测器
    """
    return FTTransformerPredictor.train_predictor(
        train_start=train_start,
        train_end=train_end,
        model_name=model_name,
        target_days=target_days,
        time_limit=time_limit,
        presets=presets,
        custom_features=custom_features,
        preprocessing_config=preprocessing_config,
        tuning_ratio=tuning_ratio,
        use_sample_weights=use_sample_weights,
        sorted_feature=sorted_feature,
        device=device
    )


def load_and_predict_ft_transformer(model_name: str, factor_data: pd.DataFrame, 
                                   factor_name: str = 'ft_predicted_return', device: Optional[str] = None):
    """
    加载FT-Transformer模型并进行预测的便捷函数（使用基类统一实现）

    Args:
        model_name: 模型名称
        factor_data: 因子数据DataFrame
        factor_name: 预测因子名称
        device: 计算设备

    Returns:
        包含预测结果的DataFrame
    """
    return FTTransformerPredictor.load_and_predict(
        model_name=model_name,
        factor_data=factor_data,
        factor_name=factor_name,
        device=device
    )


if __name__ == "__main__":
    # 测试训练
    train_start = '20240101'
    train_end = '20240630'
    
    # 测试不同配置
    configs_to_test = [
        {'factor_config': 'best_ten', 'preprocessing_config': 'conservative'},
        {'factor_config': 'best_five', 'preprocessing_config': 'conservative'},
    ]
    
    for i, config in enumerate(configs_to_test):
        print(f"\n{'='*60}")
        print(f"测试配置 {i+1}: {config}")
        print(f"{'='*60}")
        
        # 获取因子配置
        custom_features = get_factor_config_by_name(config['factor_config'])
        
        predictor = train_ft_transformer_predictor(
            train_start=train_start,
            train_end=train_end,
            model_name=f"ft_test_{config['factor_config']}",
            target_days=1,
            time_limit=60,  # 短时间测试
            custom_features=custom_features,
            preprocessing_config=config['preprocessing_config'],
            tuning_ratio=0.1
        )
        
        if predictor:
            print(f"✅ 配置 {i+1} 训练成功！")
        else:
            print(f"❌ 配置 {i+1} 训练失败！")