"""
增强版ZL模型实现模块 - 重构后的核心模型类
主要改进：
1. 恢复债券底仓（包含票息现值）
2. 标准化期权定价公式
3. 计算敏感度指标（相关系数、回归斜率）
4. 统一使用收益率度量
5. 引入动态参数与微观因子
6. 可量化诊断指标输出（相关系数、回归斜率等）
"""

import numpy as np
import pandas as pd
import sqlite3
import logging
from scipy.stats import norm
from scipy.optimize import minimize_scalar
from multiprocessing import cpu_count
import time
from datetime import datetime, timedelta
from tqdm import tqdm

from config.config import config_manager

# 精度等级配置
PRECISION_LEVELS = {
    'fast': {
        'vol_window': 20,
        'corr_window': 20,
        'mc_simulations': 1000,
        'description': '快速模式'
    },
    'medium': {
        'vol_window': 60,
        'corr_window': 60,
        'mc_simulations': 5000,
        'description': '中等精度'
    },
    'high': {
        'vol_window': 120,
        'corr_window': 120,
        'mc_simulations': 10000,
        'description': '高精度'
    },
    'full': {
        'vol_window': 252,
        'corr_window': 252,
        'mc_simulations': 50000,
        'description': '完整精度'
    }
}


class EnhancedZLModel:
    """增强版ZL模型类 - 专注于提升价格敏感度和IC值"""
    
    def __init__(self, n_processes=None, precision_level='medium', use_dynamic_params=True):
        """
        初始化增强版ZL模型
        
        Args:
            n_processes: 进程数
            precision_level: 精度等级
            use_dynamic_params: 是否使用动态参数
        """
        self.n_processes = n_processes or min(cpu_count(), 8)
        self.precision_level = precision_level
        self.use_dynamic_params = use_dynamic_params
        self.db_path = config_manager.get_db_path()
        self.logger = logging.getLogger(__name__)
        
        # 获取精度配置
        self.precision_config = PRECISION_LEVELS.get(precision_level, PRECISION_LEVELS['medium'])
        
        # 模型参数
        self.risk_free_rate = 0.025  # 无风险利率
        self.dividend_yield = 0.02   # 股息率
        
        # 缓存
        self._volatility_cache = {}
        self._correlation_cache = {}
        
        print(f"增强版ZL模型初始化完成 - 精度: {precision_level}")
    
    def calculate_theoretical_price_with_sensitivity(self, cb_row, stock_data):
        """
        计算理论价格并进行敏感度分析
        
        Args:
            cb_row: 可转债数据行
            stock_data: 正股数据
            
        Returns:
            包含理论价格和敏感度分析的字典
        """
        try:
            # 基础参数
            S = stock_data['close']  # 正股价格
            K = cb_row['conv_price']  # 转股价
            T = self._calculate_time_to_maturity(cb_row)  # 到期时间
            r = self.risk_free_rate  # 无风险利率
            q = self.dividend_yield  # 股息率
            
            # 计算波动率
            sigma = self._get_volatility(cb_row['ts_code'], cb_row['trade_date'])
            
            # 计算债券底仓（包含票息现值）
            bond_floor = self._calculate_bond_floor_with_coupon(cb_row, r)
            
            # 计算期权价值（使用Black-Scholes公式）
            option_value = self._calculate_option_value(S, K, T, r, q, sigma)
            
            # 理论价格 = 债券底仓 + 期权价值
            theoretical_price = bond_floor + option_value
            
            # 计算敏感度分析指标
            sensitivity_metrics = self._calculate_sensitivity_metrics(cb_row, stock_data)
            
            return {
                'theoretical_price': theoretical_price,
                'bond_floor': bond_floor,
                'option_value': option_value,
                'model_delta': self._calculate_delta(S, K, T, r, q, sigma),
                'volatility': sigma,
                **sensitivity_metrics
            }
            
        except Exception as e:
            self.logger.warning(f"计算理论价格失败: {e}")
            return {
                'theoretical_price': cb_row.get('full_price', 100),
                'bond_floor': 100,
                'option_value': 0,
                'model_delta': 0,
                'volatility': 0.3,
                'corr_coef': 0,
                'reg_slope': 0,
                'cb_return': 0,
                'stock_return': 0,
                'predicted_return': 0,
                'expected_return': 0
            }
    
    def _calculate_time_to_maturity(self, cb_row):
        """计算到期时间（年）"""
        try:
            current_date = pd.to_datetime(str(cb_row['trade_date']), format='%Y%m%d')
            maturity_date = pd.to_datetime(str(cb_row['maturity_date']), format='%Y%m%d')
            days_to_maturity = (maturity_date - current_date).days
            return max(days_to_maturity / 365.0, 0.01)  # 最小0.01年
        except:
            return 2.0  # 默认2年
    
    def _calculate_bond_floor_with_coupon(self, cb_row, r):
        """计算包含票息现值的债券底仓"""
        try:
            # 基础面值
            par_value = cb_row.get('par', 100)
            
            # 票息率和付息频率
            coupon_rate = cb_row.get('coupon_rate', 0.5) / 100  # 转换为小数
            pay_per_year = cb_row.get('pay_per_year', 1)  # 每年付息次数
            
            # 到期时间
            T = self._calculate_time_to_maturity(cb_row)
            
            # 计算票息现值
            if coupon_rate > 0 and pay_per_year > 0:
                coupon_payment = par_value * coupon_rate / pay_per_year
                periods = int(T * pay_per_year)
                
                coupon_pv = 0
                for i in range(1, periods + 1):
                    t = i / pay_per_year
                    coupon_pv += coupon_payment * np.exp(-r * t)
            else:
                coupon_pv = 0
            
            # 本金现值
            principal_pv = par_value * np.exp(-r * T)
            
            # 债券底仓 = 本金现值 + 票息现值
            bond_floor = principal_pv + coupon_pv
            
            return max(bond_floor, 80)  # 最小80元
            
        except Exception as e:
            self.logger.warning(f"计算债券底仓失败: {e}")
            return 90  # 默认值
    
    def _calculate_option_value(self, S, K, T, r, q, sigma):
        """使用Black-Scholes公式计算期权价值"""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            # Black-Scholes公式
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            # 看涨期权价值
            call_value = S * np.exp(-q * T) * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
            
            # 转换为可转债期权价值（每张债券可转换100/K股）
            conversion_ratio = 100 / K
            option_value = call_value * conversion_ratio
            
            return max(option_value, 0)
            
        except Exception as e:
            self.logger.warning(f"计算期权价值失败: {e}")
            return 0
    
    def _calculate_delta(self, S, K, T, r, q, sigma):
        """计算Delta值"""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            delta = np.exp(-q * T) * norm.cdf(d1)
            
            # 转换为可转债Delta
            conversion_ratio = 100 / K
            return delta * conversion_ratio
            
        except Exception as e:
            return 0
    
    def _get_volatility(self, ts_code, trade_date):
        """获取波动率"""
        cache_key = f"{ts_code}_{trade_date}"
        if cache_key in self._volatility_cache:
            return self._volatility_cache[cache_key]
        
        try:
            # 获取正股代码
            conn = sqlite3.connect(self.db_path)
            query = "SELECT stk_code FROM cb_basic WHERE ts_code = ?"
            result = pd.read_sql(query, conn, params=[ts_code])
            
            if result.empty:
                conn.close()
                return 0.3  # 默认波动率
            
            stk_code = result.iloc[0]['stk_code']
            
            # 获取历史价格数据
            window = self.precision_config['vol_window']
            query = f"""
            SELECT close FROM stock_daily 
            WHERE ts_code = ? AND trade_date <= ? 
            ORDER BY trade_date DESC 
            LIMIT {window}
            """
            
            prices = pd.read_sql(query, conn, params=[stk_code, trade_date])
            conn.close()
            
            if len(prices) < 10:
                return 0.3  # 默认波动率
            
            # 计算对数收益率
            returns = np.log(prices['close'] / prices['close'].shift(1)).dropna()
            
            if len(returns) < 5:
                return 0.3
            
            # 年化波动率
            volatility = returns.std() * np.sqrt(252)
            volatility = max(min(volatility, 1.0), 0.1)  # 限制在0.1-1.0之间
            
            self._volatility_cache[cache_key] = volatility
            return volatility
            
        except Exception as e:
            self.logger.warning(f"计算波动率失败: {e}")
            return 0.3
    
    def _calculate_sensitivity_metrics(self, cb_row, stock_data):
        """计算敏感度分析指标"""
        try:
            # 获取历史数据计算相关性和回归斜率
            corr_window = self.precision_config['corr_window']
            
            conn = sqlite3.connect(self.db_path)
            
            # 获取正股代码
            basic_query = "SELECT stk_code FROM cb_basic WHERE ts_code = ?"
            basic_result = pd.read_sql(basic_query, conn, params=[cb_row['ts_code']])
            
            if basic_result.empty:
                conn.close()
                return self._default_sensitivity_metrics()
            
            stk_code = basic_result.iloc[0]['stk_code']
            
            # 获取历史数据
            query = f"""
            SELECT cd.trade_date, cd.close as cb_close, sd.close as stock_close
            FROM cb_daily cd
            JOIN stock_daily sd ON cd.trade_date = sd.trade_date
            WHERE cd.ts_code = ? AND sd.ts_code = ? AND cd.trade_date <= ?
            ORDER BY cd.trade_date DESC
            LIMIT {corr_window}
            """
            
            hist_data = pd.read_sql(query, conn, params=[cb_row['ts_code'], stk_code, cb_row['trade_date']])
            conn.close()
            
            if len(hist_data) < 10:
                return self._default_sensitivity_metrics()
            
            # 计算收益率
            hist_data['cb_return'] = hist_data['cb_close'].pct_change()
            hist_data['stock_return'] = hist_data['stock_close'].pct_change()
            hist_data = hist_data.dropna()
            
            if len(hist_data) < 5:
                return self._default_sensitivity_metrics()
            
            # 计算相关系数
            corr_coef = hist_data['cb_return'].corr(hist_data['stock_return'])
            
            # 计算回归斜率
            from scipy import stats
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                hist_data['stock_return'], hist_data['cb_return']
            )
            
            # 当前收益率
            cb_return = hist_data['cb_return'].iloc[0] if len(hist_data) > 0 else 0
            stock_return = hist_data['stock_return'].iloc[0] if len(hist_data) > 0 else 0
            
            # 预测收益率
            predicted_return = intercept + slope * stock_return
            
            # 期望收益率（基于历史均值）
            expected_return = hist_data['cb_return'].mean()
            
            return {
                'corr_coef': corr_coef if not np.isnan(corr_coef) else 0,
                'reg_slope': slope if not np.isnan(slope) else 0,
                'cb_return': cb_return if not np.isnan(cb_return) else 0,
                'stock_return': stock_return if not np.isnan(stock_return) else 0,
                'predicted_return': predicted_return if not np.isnan(predicted_return) else 0,
                'expected_return': expected_return if not np.isnan(expected_return) else 0
            }
            
        except Exception as e:
            self.logger.warning(f"计算敏感度指标失败: {e}")
            return self._default_sensitivity_metrics()
    
    def _default_sensitivity_metrics(self):
        """默认敏感度指标"""
        return {
            'corr_coef': 0,
            'reg_slope': 0,
            'cb_return': 0,
            'stock_return': 0,
            'predicted_return': 0,
            'expected_return': 0
        }
    
    def _get_stock_data(self, ts_code, trade_date):
        """获取正股数据"""
        try:
            # 从cb_basic获取对应的正股代码
            conn = sqlite3.connect(self.db_path)

            # 获取正股代码
            basic_query = f"""
            SELECT stk_code FROM cb_basic WHERE ts_code = '{ts_code}'
            """
            basic_result = pd.read_sql(basic_query, conn)

            if basic_result.empty:
                conn.close()
                return None

            stk_code = basic_result.iloc[0]['stk_code']

            # 获取正股基础数据
            query = f"""
            SELECT close, vol as volume, amount
            FROM stock_daily
            WHERE ts_code = '{stk_code}' AND trade_date = '{trade_date}'
            """

            result = pd.read_sql(query, conn)
            conn.close()

            if result.empty:
                return None

            stock_data = {
                'ts_code': stk_code,
                'close': result.iloc[0]['close'],
                'volume': result.iloc[0].get('volume', 1000000),
                'pe': 20,  # 默认市盈率
                'pb': 2,   # 默认市净率
                'market_cap': result.iloc[0].get('amount', 1000000) * 10000,  # 基于成交额估算市值
                'turnover': 0.01  # 默认换手率
            }

            return stock_data

        except Exception as e:
            self.logger.warning(f"获取正股数据失败 {ts_code}: {e}")
            return None
