#!/usr/bin/env python3
"""
基于LightGBM的因子预测器
实现与AutoGluon类似的功能，但专门使用LightGBM模型
使用top_mean_squarer作为损失函数
"""

import pandas as pd
import numpy as np
import sqlite3
import pickle
import json
import warnings
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any

import lightgbm as lgb
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

warnings.filterwarnings('ignore')

# 导入基类和共享工具
from .base_predictor import (
    BasePredictor, top_mean_squarer, calculate_cross_sectional_r2,
    _split_data_by_bond, get_factor_config_by_name, UNIFIED_PREPROCESSING,
    calculate_sample_weights, add_sample_weights_to_dataframe, config_manager
)


def top_mse_objective(y_pred, y_true):
    """
    LightGBM自定义目标函数：只对预测值前10%的样本计算损失

    Args:
        y_pred: 预测值 (LightGBM传入的参数顺序)
        y_true: 真实值 (LightGBM的Dataset对象)

    Returns:
        grad: 梯度
        hess: 二阶导数
    """
    # 从Dataset对象中获取真实标签
    if hasattr(y_true, 'get_label'):
        y_true_values = y_true.get_label()
    else:
        y_true_values = y_true

    # 找到预测值前10%的样本
    sorted_idx = np.argsort(-y_pred)
    cutoff = int(np.ceil(len(y_pred) * 0.1))
    top_idx = sorted_idx[:cutoff]

    # 初始化梯度和二阶导数
    grad = np.zeros_like(y_pred)
    hess = np.zeros_like(y_pred)

    # 只对前10%的样本计算梯度和二阶导数
    if len(top_idx) > 0:
        y_true_top = y_true_values[top_idx]
        y_pred_top = y_pred[top_idx]

        # MSE的梯度: 2 * (y_pred - y_true) / n
        # MSE的二阶导数: 2 / n
        residual = y_pred_top - y_true_top
        grad[top_idx] = 2.0 * residual / len(top_idx)
        hess[top_idx] = 2.0 / len(top_idx)

    return grad, hess


def top_mse_eval_metric(y_pred, y_true):
    """
    LightGBM自定义评估指标：计算前10%预测值的MSE

    Args:
        y_pred: 预测值 (LightGBM传入的参数顺序)
        y_true: 真实值 (LightGBM的Dataset对象)

    Returns:
        eval_name: 评估指标名称
        eval_result: 评估结果
        is_higher_better: 是否越高越好
    """
    # 从Dataset对象中获取真实标签
    if hasattr(y_true, 'get_label'):
        y_true_values = y_true.get_label()
    else:
        y_true_values = y_true

    # 找到预测值前10%的样本
    sorted_idx = np.argsort(-y_pred)
    cutoff = int(np.ceil(len(y_pred) * 0.1))
    top_idx = sorted_idx[:cutoff]

    if len(top_idx) == 0:
        return 'top_mse', 0.0, False

    # 计算前10%样本的MSE
    y_true_top = y_true_values[top_idx]
    y_pred_top = y_pred[top_idx]
    mse = np.mean((y_true_top - y_pred_top) ** 2)

    return 'top_mse', mse, False  # False表示越小越好


class LightGBMPredictor(BasePredictor):
    """
    基于LightGBM的因子预测器
    
    支持LightGBM模型的训练和预测，使用与AutoGluon相似的参数配置
    """
    
    def __init__(self, model_name: str = 'default', target_days: int = 1,
                 feature_config: Optional[Dict] = None, preprocessing_config: Optional[str] = None,
                 create_unified_preprocessor_now: bool = True, use_sample_weights: bool = False,
                 sorted_feature: bool = False):
        """
        初始化LightGBM预测器

        Args:
            model_name: 模型名称
            target_days: 预测天数
            feature_config: 特征配置字典
            preprocessing_config: 预处理配置
            create_unified_preprocessor_now: 是否立即创建预处理器
            use_sample_weights: 是否使用样本权重
            sorted_feature: 是否使用排序特征
        """
        # 调用父类初始化
        preprocessing_config = preprocessing_config or 'conservative'
        super().__init__(
            model_name=model_name,
            target_days=target_days,
            feature_config=feature_config,
            preprocessing_config=preprocessing_config,
            create_unified_preprocessor_now=create_unified_preprocessor_now,
            use_sample_weights=use_sample_weights,
            sorted_feature=sorted_feature
        )

        # LightGBM特有的属性
        self.model = None
        self.scaler = StandardScaler()
        
        # LightGBM参数配置（参考autogluon_predictor中的配置）
        self.lgb_params = {
            "extra_trees": True,
            'seed': 0,
            'bagging_seed': 0,
            'feature_fraction_seed': 0,
            'drop_seed': 0,
            'extra_seed': 0,
            'objective_seed': 0,
            'data_random_seed': 0,
            'bagging_fraction': 1.0,
            'feature_fraction': 1.0,
            'bagging_freq': 0,
            'early_stopping_round': 100,
            'force_col_wise': True,
            'objective': top_mse_objective,  # 使用自定义的top MSE目标函数
            'metric': 'None',  # 禁用默认指标，使用自定义评估指标
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.1,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1
        }

        print(f"LightGBM因子预测器初始化 - 模型: {model_name}, 预测天数: {target_days}")

    def _get_models_dir(self) -> Path:
        """获取LightGBM模型保存目录"""
        return Path('models/lightgbm')

    def train_model(self, train_data: pd.DataFrame, time_limit: int = 300, 
                   presets: str = 'medium_quality', tuning_data: Optional[pd.DataFrame] = None):
        """
        训练LightGBM模型
        
        Args:
            train_data: 训练数据
            time_limit: 训练时间限制（秒）
            presets: 模型预设（保持接口一致性）
            tuning_data: 调优数据
            
        Returns:
            训练结果字典
        """
        if train_data is None or train_data.empty:
            print("没有训练数据")
            return None
        
        print(f"开始训练LightGBM模型...")
        
        try:
            # 准备训练数据
            required_columns = self.feature_names + [self.target_name]
            
            # 处理样本权重
            sample_weight_col = None
            if self.use_sample_weights and calculate_sample_weights is not None:
                print("正在计算样本权重...")
                if 'trade_date' in train_data.columns and 'ts_code' in train_data.columns:
                    train_data_with_weights = add_sample_weights_to_dataframe(
                        train_data, self.target_name, 'trade_date', 'sample_weight'
                    )
                    sample_weight_col = 'sample_weight'
                    required_columns.append(sample_weight_col)
                    train_features = train_data_with_weights[required_columns].copy()
                    print(f"已添加样本权重列: {sample_weight_col}")
                else:
                    print("警告: 缺少trade_date或ts_code列，无法计算样本权重")
                    train_features = train_data[required_columns].copy()
            else:
                train_features = train_data[required_columns].copy()
            
            # 清理数据
            before_count = len(train_features)
            train_features = train_features.dropna()
            after_count = len(train_features)
            
            if before_count != after_count:
                print(f"清理训练数据: {before_count} -> {after_count}")
            
            # 准备特征和目标
            X_train = train_features[self.feature_names].values
            y_train = train_features[self.target_name].values
            sample_weights_train = None
            
            if sample_weight_col is not None:
                sample_weights_train = train_features[sample_weight_col].values
            
            # 标准化特征
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 准备调优数据
            X_val_scaled = None
            y_val = None
            sample_weights_val = None
            
            if tuning_data is not None and not tuning_data.empty:
                tuning_required_columns = self.feature_names + [self.target_name]
                
                if sample_weight_col is not None:
                    if 'trade_date' in tuning_data.columns and 'ts_code' in tuning_data.columns:
                        tuning_data_with_weights = add_sample_weights_to_dataframe(
                            tuning_data, self.target_name, 'trade_date', 'sample_weight'
                        )
                        tuning_required_columns.append(sample_weight_col)
                        tuning_features = tuning_data_with_weights[tuning_required_columns].copy()
                    else:
                        tuning_features = tuning_data[tuning_required_columns].copy()
                else:
                    tuning_features = tuning_data[tuning_required_columns].copy()
                
                tuning_features = tuning_features.dropna()
                
                if not tuning_features.empty:
                    X_val = tuning_features[self.feature_names].values
                    y_val = tuning_features[self.target_name].values
                    X_val_scaled = self.scaler.transform(X_val)
                    
                    if sample_weight_col is not None and sample_weight_col in tuning_features.columns:
                        sample_weights_val = tuning_features[sample_weight_col].values
                    
                    print(f"使用调优数据: {len(tuning_features)} 条记录")
            
            # 创建LightGBM数据集
            train_dataset = lgb.Dataset(
                X_train_scaled, 
                label=y_train, 
                weight=sample_weights_train,
                feature_name=self.feature_names
            )
            
            valid_sets = [train_dataset]
            valid_names = ['train']
            
            if X_val_scaled is not None:
                val_dataset = lgb.Dataset(
                    X_val_scaled, 
                    label=y_val, 
                    weight=sample_weights_val,
                    reference=train_dataset,
                    feature_name=self.feature_names
                )
                valid_sets.append(val_dataset)
                valid_names.append('valid')
            
            # 训练模型
            print(f"开始LightGBM训练，参数: {self.lgb_params}")
            
            self.model = lgb.train(
                self.lgb_params,
                train_dataset,
                valid_sets=valid_sets,
                valid_names=valid_names,
                num_boost_round=1000,
                feval=top_mse_eval_metric,  # 添加自定义评估指标
                callbacks=[
                    lgb.early_stopping(self.lgb_params['early_stopping_round'], first_metric_only=True),
                    lgb.log_evaluation(period=100)
                ]
            )
            
            # 评估模型
            train_predictions = self.model.predict(X_train_scaled)
            train_mse = mean_squared_error(y_train, train_predictions)
            
            # 计算横截面R²
            if 'trade_date' in train_data.columns:
                train_eval_data = pd.DataFrame({
                    'trade_date': train_data.loc[train_features.index, 'trade_date'],
                    'y_true': y_train,
                    'y_pred': train_predictions
                })
                train_r2 = calculate_cross_sectional_r2(train_eval_data, 'y_true', 'y_pred', 'trade_date')
            else:
                train_r2 = r2_score(y_train, train_predictions)
            
            self.training_results = {
                'train_mse': train_mse,
                'train_rmse': np.sqrt(train_mse),
                'train_r2': train_r2,
                'train_samples': len(train_features),
                'feature_importance': dict(zip(self.feature_names, self.model.feature_importance())),
                'best_iteration': self.model.best_iteration
            }
            
            # 如果有验证数据，也进行评估
            if X_val_scaled is not None:
                val_predictions = self.model.predict(X_val_scaled)
                val_mse = mean_squared_error(y_val, val_predictions)
                
                if 'trade_date' in tuning_data.columns:
                    val_eval_data = pd.DataFrame({
                        'trade_date': tuning_data.loc[tuning_features.index, 'trade_date'],
                        'y_true': y_val,
                        'y_pred': val_predictions
                    })
                    val_r2 = calculate_cross_sectional_r2(val_eval_data, 'y_true', 'y_pred', 'trade_date')
                else:
                    val_r2 = r2_score(y_val, val_predictions)
                
                self.training_results.update({
                    'tuning_mse': val_mse,
                    'tuning_rmse': np.sqrt(val_mse),
                    'tuning_r2': val_r2,
                    'tuning_samples': len(tuning_features)
                })
                
                print(f"LightGBM模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_features)}")
                print(f"  调优集 - RMSE: {np.sqrt(val_mse):.6f}, R²: {val_r2:.4f}, 样本数: {len(tuning_features)}")
            else:
                print(f"LightGBM模型训练完成:")
                print(f"  训练集 - RMSE: {np.sqrt(train_mse):.6f}, R²: {train_r2:.4f}, 样本数: {len(train_features)}")
            
            print(f"  最佳迭代次数: {self.model.best_iteration}")
            
            # 保存模型
            self._save_model()
            
            return self.training_results
            
        except Exception as e:
            print(f"LightGBM模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def predict(self, factor_data: pd.DataFrame) -> Optional[np.ndarray]:
        """
        使用训练好的模型进行预测

        Args:
            factor_data: 因子数据DataFrame

        Returns:
            预测结果数组
        """
        if factor_data is None or factor_data.empty:
            return None

        if self.model is None:
            print("模型未训练")
            return None

        # 应用与训练时相同的预处理（重用autogluon_predictor的逻辑）
        processed_data = factor_data.copy()

        if self.preprocessor is not None and UNIFIED_PREPROCESSING:
            try:
                # 识别基础特征
                engineered_suffixes = ['_x_', '_div_', '_squared', '_lag1', '_ma3']
                rank_suffixes = ['_time_rank', '_cross_rank']

                if self.sorted_feature:
                    base_features = []
                    for f in self.feature_names:
                        if '_time_rank' in f:
                            base_feature = f.replace('_time_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)
                        elif '_cross_rank' in f:
                            base_feature = f.replace('_cross_rank', '')
                            if base_feature not in base_features:
                                base_features.append(base_feature)

                    core_base_features = []
                    for feature in base_features:
                        is_engineered = any(suffix in feature for suffix in engineered_suffixes)
                        if not is_engineered:
                            core_base_features.append(feature)
                        else:
                            for suffix in engineered_suffixes:
                                if suffix in feature:
                                    if suffix == '_x_':
                                        parts = feature.split('_x_')
                                        for part in parts:
                                            if part and part not in core_base_features:
                                                core_base_features.append(part)
                                    else:
                                        core_feature = feature.split(suffix)[0]
                                        if core_feature and core_feature not in core_base_features:
                                            core_base_features.append(core_feature)
                                    break

                    available_base_features = [f for f in core_base_features if f in processed_data.columns]
                else:
                    base_features = [f for f in self.feature_names
                                   if not any(suffix in f for suffix in engineered_suffixes)]
                    available_base_features = [f for f in base_features if f in processed_data.columns]

                processed_data = self.preprocessor.preprocess_features(
                    processed_data,
                    available_base_features,
                    is_training=False
                )
            except Exception as e:
                print(f"⚠️  推理预处理失败: {e}，使用原始数据")
                processed_data = factor_data.copy()

        # 排序特征转换（如果需要）
        if self.sorted_feature:
            try:
                print(f"🔄 应用排序特征转换（推理模式）...")
                if 'ts_code' not in processed_data.columns or 'trade_date' not in processed_data.columns:
                    if 'ts_code' not in processed_data.columns:
                        processed_data['ts_code'] = 'unknown'
                    if 'trade_date' not in processed_data.columns:
                        processed_data['trade_date'] = '20240101'

                available_features = [col for col in processed_data.columns
                                    if col not in ['ts_code', 'trade_date', self.target_name]]

                processed_data = self._apply_sorted_feature_transformation_for_prediction(processed_data, available_features)

                if processed_data.empty:
                    print(f"⚠️  排序特征转换后数据为空，无法进行预测")
                    return None

                print(f"排序特征转换完成（推理模式）: {len(processed_data)} 条记录")
            except Exception as e:
                print(f"⚠️  排序特征转换失败: {e}")
                return None

        # 检查特征是否存在
        missing_features = [f for f in self.feature_names if f not in processed_data.columns]
        if missing_features:
            print(f"缺少特征: {missing_features[:10]}{'...' if len(missing_features) > 10 else ''}")
            return None

        try:
            # 准备特征矩阵
            X = processed_data[self.feature_names].values

            # 检查并处理NaN值
            if np.isnan(X).any():
                print(f"⚠️  检测到 {np.isnan(X).sum()} 个NaN值，进行填充处理")
                # 使用训练时的scaler的均值填充NaN（如果可用）
                nan_mask = np.isnan(X)
                if hasattr(self.scaler, 'mean_') and self.scaler.mean_ is not None:
                    # 对于每个特征，用该特征的训练时均值填充
                    for i in range(X.shape[1]):
                        if nan_mask[:, i].any():
                            X[nan_mask[:, i], i] = self.scaler.mean_[i]
                else:
                    # 如果没有训练时的均值信息，用0填充
                    X[nan_mask] = 0.0
                print(f"✅ NaN值填充完成")

            X_scaled = self.scaler.transform(X)

            # 再次检查缩放后的数据
            if np.isnan(X_scaled).any() or np.isinf(X_scaled).any():
                print(f"⚠️  缩放后检测到异常值，进行清理")
                X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=1.0, neginf=-1.0)

            # 预测
            predictions = self.model.predict(X_scaled)

            return predictions

        except Exception as e:
            print(f"LightGBM预测失败: {e}")
            return None

    def _save_model(self):
        """保存LightGBM模型"""
        try:
            model_path = self.models_dir / f"{self.model_name}_lightgbm.pkl"

            # 保存模型状态
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'training_results': self.training_results,
                'lgb_params': self.lgb_params,
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name,
                'preprocessing_config': self.preprocessing_config,
                'sorted_feature': self.sorted_feature,
                'save_time': datetime.now().isoformat()
            }

            with open(model_path, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"LightGBM模型已保存: {model_path}")

            # 保存预处理器状态
            if self.preprocessor is not None and UNIFIED_PREPROCESSING:
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                with open(preprocessor_path, 'wb') as f:
                    pickle.dump(self.preprocessor, f)
                print(f"预处理器状态已保存: {preprocessor_path}")

            # 保存元数据
            metadata_path = self.models_dir / f"{self.model_name}_metadata.json"
            metadata = {
                'model_name': self.model_name,
                'target_days': self.target_days,
                'target_name': self.target_name,
                'feature_names': self.feature_names,
                'preprocessing_config': self.preprocessing_config,
                'sorted_feature': self.sorted_feature,
                'lgb_params': self.lgb_params,
                'save_time': datetime.now().isoformat()
            }

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            print(f"元数据已保存: {metadata_path}")

        except Exception as e:
            print(f"保存LightGBM模型失败: {e}")

    def load_model(self, model_name: Optional[str] = None) -> bool:
        """
        加载已训练的LightGBM模型

        Args:
            model_name: 模型名称

        Returns:
            是否加载成功
        """
        if model_name:
            self.model_name = model_name

        try:
            model_path = self.models_dir / f"{self.model_name}_lightgbm.pkl"

            if not model_path.exists():
                print(f"LightGBM模型文件不存在: {model_path}")
                return False

            # 加载模型数据
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)

            # 恢复模型参数
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.training_results = model_data['training_results']
            self.lgb_params = model_data['lgb_params']
            self.target_days = model_data['target_days']
            self.target_name = model_data['target_name']
            self.preprocessing_config = model_data['preprocessing_config']
            self.sorted_feature = model_data['sorted_feature']

            # 加载预处理器状态
            if UNIFIED_PREPROCESSING:
                preprocessor_path = self.models_dir / f"{self.model_name}_preprocessor.pkl"
                if preprocessor_path.exists():
                    with open(preprocessor_path, 'rb') as f:
                        self.preprocessor = pickle.load(f)
                    print(f"✅ 预处理器状态已加载: {preprocessor_path}")
                else:
                    print(f"⚠️  未找到预处理器状态文件，重新创建")
                    from .unified_feature_preprocessor import create_unified_preprocessor
                    self.preprocessor = create_unified_preprocessor(self.preprocessing_config)

            print(f"LightGBM模型加载成功: {self.model_name}")
            print(f"  特征数量: {len(self.feature_names)}")
            print(f"  排序特征模式: {'启用' if self.sorted_feature else '禁用'}")

            return True

        except Exception as e:
            print(f"加载LightGBM模型失败: {e}")
            return False

    def list_available_models(self):
        """列出所有可用的LightGBM模型"""
        model_files = list(self.models_dir.glob("*_lightgbm.pkl"))
        metadata_files = list(self.models_dir.glob("*_metadata.json"))

        if not model_files:
            print("没有找到已保存的LightGBM模型")
            return []

        print(f"\n可用LightGBM模型列表 (共{len(model_files)}个):")
        print("-" * 80)
        print(f"{'模型名称':<25} {'保存时间':<20} {'预测天数':<10} {'特征数':<8} {'R²':<8}")
        print("-" * 80)

        models_info = []
        for model_file in sorted(model_files):
            try:
                model_name = model_file.stem.replace('_lightgbm', '')
                metadata_file = self.models_dir / f"{model_name}_metadata.json"

                if metadata_file.exists():
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

                    save_time = metadata.get('save_time', 'Unknown')[:19]
                    target_days = metadata.get('target_days', 'N/A')
                    feature_count = len(metadata.get('feature_names', []))

                    # 尝试获取R²值
                    r2_value = 0.0
                    try:
                        # 加载模型数据获取训练结果
                        with open(model_file, 'rb') as f:
                            model_data = pickle.load(f)
                        training_results = model_data.get('training_results', {})
                        r2_value = training_results.get('train_r2', training_results.get('r2', 0.0))
                    except:
                        r2_value = 0.0

                    print(f"{model_name:<25} {save_time:<20} {target_days:<10} {feature_count:<8} {r2_value:<8.4f}")

                    models_info.append({
                        'name': model_name,
                        'path': model_file,
                        'save_time': save_time,
                        'target_days': target_days,
                        'feature_count': feature_count,
                        'r2': r2_value
                    })
                else:
                    print(f"{model_name:<25} {'No metadata':<20} {'N/A':<10} {'N/A':<8} {'N/A':<8}")
            except Exception as e:
                print(f"{model_file.stem:<25} {'Error':<20} {'Error':<10} {'N/A':<8} {'N/A':<8}")

        return models_info


def train_lightgbm_predictor(train_start: str, train_end: str, model_name: str = 'default',
                            target_days: int = 1, time_limit: int = 300, presets: str = 'medium_quality',
                            custom_features: Optional[List[str]] = None,
                            preprocessing_config: str = 'conservative',
                            tuning_ratio: float = 0.1, use_sample_weights: bool = False,
                            sorted_feature: bool = False):
    """
    训练LightGBM预测器的便捷函数

    Args:
        train_start: 训练开始日期
        train_end: 训练结束日期
        model_name: 模型名称
        target_days: 预测天数
        time_limit: 训练时间限制（秒）
        presets: 模型预设（保持接口一致性）
        custom_features: 自定义特征列表
        preprocessing_config: 预处理配置
        tuning_ratio: tuning data占总数据的比例
        use_sample_weights: 是否使用样本权重
        sorted_feature: 是否使用排序特征

    Returns:
        训练好的预测器
    """
    return LightGBMPredictor.train_predictor(
        train_start=train_start,
        train_end=train_end,
        model_name=model_name,
        target_days=target_days,
        time_limit=time_limit,
        presets=presets,
        custom_features=custom_features,
        preprocessing_config=preprocessing_config,
        tuning_ratio=tuning_ratio,
        use_sample_weights=use_sample_weights,
        sorted_feature=sorted_feature
    )


def load_and_predict_lightgbm(model_name: str, factor_data: pd.DataFrame,
                             factor_name: str = 'combined_factor'):
    """
    加载LightGBM模型并进行预测的便捷函数（使用基类统一实现）

    Args:
        model_name: 模型名称
        factor_data: 因子数据DataFrame
        factor_name: 预测因子名称

    Returns:
        包含预测结果的DataFrame
    """
    return LightGBMPredictor.load_and_predict(
        model_name=model_name,
        factor_data=factor_data,
        factor_name=factor_name
    )


if __name__ == "__main__":
    # 测试训练
    train_start = '20240101'
    train_end = '20240630'

    # 测试不同配置
    configs_to_test = [
        {'factor_config': 'best_ten', 'preprocessing_config': 'conservative'},
        {'factor_config': 'best_five', 'preprocessing_config': 'conservative'},
    ]

    for i, config in enumerate(configs_to_test):
        print(f"\n{'='*60}")
        print(f"测试配置 {i+1}: {config}")
        print(f"{'='*60}")

        # 获取因子配置
        custom_features = get_factor_config_by_name(config['factor_config'])

        predictor = train_lightgbm_predictor(
            train_start=train_start,
            train_end=train_end,
            model_name=f"lgbm_test_{config['factor_config']}",
            target_days=1,
            time_limit=60,  # 短时间测试
            custom_features=custom_features,
            preprocessing_config=config['preprocessing_config'],
            tuning_ratio=0.1
        )

        if predictor:
            print(f"✅ 配置 {i+1} 训练成功！")
        else:
            print(f"❌ 配置 {i+1} 训练失败！")
