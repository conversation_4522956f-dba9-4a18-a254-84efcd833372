"""
双低因子计算器
基于"低价格"和"低转股溢价"的双低策略因子计算
整合自原double_low_factor.py模块
"""

import pandas as pd
import numpy as np
import sqlite3
import time
import logging
from typing import Dict, Any
from scipy import stats
from sklearn.preprocessing import QuantileTransformer

from .base_factor_calculator import FactorCalculator


class DoubleLowFactorCalculator(FactorCalculator):
    """
    双低因子计算器
    
    实现简化的双低策略逻辑：
    1. 价格因子：转债当前价格
    2. 溢价因子：转股溢价率 * 100
    3. 双低因子：价格因子 + 溢价因子（从小到大排序）
    """
    
    def __init__(self, alpha=0.5):
        """
        初始化双低因子计算器
        
        Args:
            alpha: 价格因子权重，范围[0,1]，默认0.5
        """
        super().__init__("双低因子计算器")
        self.alpha = alpha
        
        print(f"双低因子计算器初始化完成: α={alpha:.2f} (价格权重:{alpha:.1%}, 溢价权重:{1-alpha:.1%})")
    
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的双低因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数，可包含alpha参数
            
        Returns:
            包含双低因子的DataFrame
        """
        # 从kwargs中获取alpha参数，如果有的话
        alpha = kwargs.get('alpha', self.alpha)
        
        date_str = self._standardize_date(date)
        print(f"\n🔄 计算双低因子 - 日期: {date_str}, α={alpha:.2f}")
        start_time = time.time()
        
        # 获取数据
        df = self._get_data_for_date(date_str)
        
        if df.empty:
            print(f"日期 {date_str} 没有可用数据")
            return pd.DataFrame()
        
        print(f"找到 {len(df)} 只可转债")
        
        # 计算双低得分
        df = self._calculate_double_low_score(df, alpha)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        # 添加排名信息（从小到大排序，得分越小排名越高）
        df['double_low_rank'] = df['double_low_score'].rank(ascending=True)
        df['double_low_percentile'] = df['double_low_score'].rank(pct=True, ascending=True)
        
        elapsed_time = time.time() - start_time
        print(f"✅ 双低因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的双低因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数，可包含alpha参数
            
        Returns:
            包含双低因子的DataFrame
        """
        alpha = kwargs.get('alpha', self.alpha)
        print(f"\n🔄 计算双低因子 - 时间段: {start_date} 到 {end_date}, α={alpha:.2f}")
        
        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')
        
        # 获取交易日
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)
        
        if not trading_dates:
            print("指定时间段内没有交易日数据")
            return pd.DataFrame()
        
        print(f"找到 {len(trading_dates)} 个交易日")
        
        all_factors = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")
            
            try:
                daily_factors = self.calculate_factors_for_date(trade_date, **kwargs)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                self.logger.error(f"处理日期 {trade_date} 时出错: {e}")
                continue
        
        if not all_factors:
            print("没有成功计算出任何因子数据")
            return pd.DataFrame()
        
        # 合并所有日期的数据
        result_df = pd.concat(all_factors, ignore_index=True)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        print(f"✅ 双低因子计算完成，共 {len(result_df)} 条记录")
        
        return result_df
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的双低因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数，可包含alpha参数
            
        Returns:
            包含单个债券双低因子的DataFrame
        """
        alpha = kwargs.get('alpha', self.alpha)
        date_str = self._standardize_date(date)
        print(f"\n🎯 计算单债券双低因子 - 日期: {date_str}, 债券: {bond_code}, α={alpha:.2f}")
        start_time = time.time()
        
        # 获取指定可转债的数据
        df = self._get_single_cb_data(date_str, bond_code)
        
        if df.empty:
            raise ValueError(f"债券 {bond_code} 在日期 {date_str} 没有找到数据")
        
        print(f"找到债券: {bond_code}")
        
        # 计算双低得分
        df = self._calculate_double_low_score(df, alpha)
        
        # 注意：因子计算器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        # 对于单债券，无法计算排名，设置为1
        df['double_low_rank'] = 1
        df['double_low_percentile'] = 1.0
        
        elapsed_time = time.time() - start_time
        print(f"✅ 单债券双低因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def _get_data_for_date(self, date_str: str) -> pd.DataFrame:
        """
        获取指定日期的可转债数据（双低策略专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD

        Returns:
            包含可转债基本信息、日行情和正股数据的DataFrame
        """
        # 双低策略的特殊过滤条件
        additional_filters = """
        cb.conv_price > 0
        AND cb.list_date IS NOT NULL
        AND cb.remain_size IS NOT NULL
        AND cb.remain_size >= 3000
        AND (
            CASE
                WHEN LENGTH(cb.list_date) = 8 THEN
                    JULIANDAY(SUBSTR(?, 1, 4) || '-' || SUBSTR(?, 5, 2) || '-' || SUBSTR(?, 7, 2)) -
                    JULIANDAY(SUBSTR(cb.list_date, 1, 4) || '-' || SUBSTR(cb.list_date, 5, 2) || '-' || SUBSTR(cb.list_date, 7, 2)) >= 10
                ELSE 0
            END
        )
        """

        # 使用基类的通用方法，但需要特殊处理参数
        conn = sqlite3.connect(self.db_path)
        try:
            df = super()._get_cb_data_for_date(
                date_str=date_str,
                fields="double_low",
                additional_filters="",  # 先不加过滤条件，因为需要特殊的参数处理
                strict_validation=False
            )

            # 如果基类方法返回空，尝试手动查询（保持原有的复杂过滤逻辑）
            if df.empty:
                query = f"""
                SELECT
                    cd.ts_code, cd.trade_date, cd.close,
                    cd.vol, cd.amount, cb.conv_price, cb.stk_code, cb.bond_short_name,
                    cb.delist_date, cb.list_date, cb.remain_size
                FROM cb_daily cd
                JOIN cb_basic cb ON cd.ts_code = cb.ts_code
                WHERE cd.trade_date = ?
                AND cd.close > 0
                AND cb.conv_price > 0
                AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > ?)
                AND cb.list_date IS NOT NULL
                AND cb.remain_size IS NOT NULL
                AND cb.remain_size >= 3000
                AND (
                    CASE
                        WHEN LENGTH(cb.list_date) = 8 THEN
                            JULIANDAY(SUBSTR(?, 1, 4) || '-' || SUBSTR(?, 5, 2) || '-' || SUBSTR(?, 7, 2)) -
                            JULIANDAY(SUBSTR(cb.list_date, 1, 4) || '-' || SUBSTR(cb.list_date, 5, 2) || '-' || SUBSTR(cb.list_date, 7, 2)) >= 10
                        ELSE 0
                    END
                )
                ORDER BY cd.ts_code
                """

                df = pd.read_sql(query, conn, params=[date_str, date_str, date_str, date_str, date_str])

            # 获取对应的正股数据（如果基础计算器没有提供的话）
            if not df.empty and 'stock_close' not in df.columns:
                stock_data = self._get_stock_data_for_date(df, date_str, conn)

                # 合并正股数据
                if not stock_data.empty:
                    df = pd.merge(df, stock_data, on='stk_ts_code', how='left', suffixes=('', '_stock'))
                    df.rename(columns={'close_stock': 'stock_close'}, inplace=True)

            return df

        except Exception as e:
            self.logger.error(f"获取数据时出错: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def _get_single_cb_data(self, date_str: str, bond_code: str) -> pd.DataFrame:
        """
        获取指定日期单个可转债的数据（双低策略专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            bond_code: 可转债代码

        Returns:
            包含单个可转债数据的DataFrame
        """
        # 使用基类的通用方法
        additional_filters = "cb.conv_price > 0"
        df = super()._get_single_cb_data(
            date_str=date_str,
            bond_code=bond_code,
            fields="double_low",
            additional_filters=additional_filters,
            strict_validation=False
        )

        if df.empty:
            return df

        # 获取对应的正股数据
        # 获取正股数据（如果基础计算器没有提供的话）
        if 'stock_close' not in df.columns:
            conn = sqlite3.connect(self.db_path)
            try:
                stock_data = self._get_stock_data_for_date(df, date_str, conn)

                # 合并正股数据
                if not stock_data.empty:
                    df = pd.merge(df, stock_data, on='stk_ts_code', how='left', suffixes=('', '_stock'))
                    df.rename(columns={'close_stock': 'stock_close'}, inplace=True)

            except Exception as e:
                self.logger.error(f"获取单债券正股数据时出错: {e}")
            finally:
                conn.close()

        return df

    def _get_stock_data_for_date(self, cb_df: pd.DataFrame, date_str: str, conn) -> pd.DataFrame:
        """
        获取正股数据

        Args:
            cb_df: 可转债数据DataFrame
            date_str: 日期字符串
            conn: 数据库连接

        Returns:
            正股数据DataFrame
        """
        # 构建正股代码列表
        # stk_code字段已经包含交易所后缀，直接使用
        cb_df['stk_ts_code'] = cb_df['stk_code']

        stock_codes = cb_df['stk_ts_code'].unique().tolist()

        if not stock_codes:
            return pd.DataFrame()

        # 构建查询语句
        placeholders = ','.join(['?' for _ in stock_codes])
        stock_query = f"""
        SELECT ts_code as stk_ts_code, close
        FROM stock_daily
        WHERE trade_date = ? AND ts_code IN ({placeholders})
        """

        params = [date_str] + stock_codes

        try:
            stock_df = pd.read_sql(stock_query, conn, params=params)
            print(f"获取到 {len(stock_df)} 只正股数据")
            return stock_df
        except Exception as e:
            self.logger.error(f"获取正股数据失败: {e}")
            return pd.DataFrame()

    def _calculate_double_low_score(self, df: pd.DataFrame, alpha: float) -> pd.DataFrame:
        """
        计算双低策略得分
        
        双低得分 = α * 价格因子 + (1-α) * 溢价因子
        其中：价格因子 = 转债价格，溢价因子 = 转股溢价率 * 100
        得分越小越好（低价格 + 低溢价）
        
        Args:
            df: 包含转债和正股数据的DataFrame
            alpha: 价格因子权重，范围[0,1]
            
        Returns:
            添加了双低得分的DataFrame
        """
        result = df.copy()
        
        # 价格因子：直接使用转债价格
        result['price_factor'] = result['full_price'] if 'full_price' in result.columns else result['close']

        # 计算转股溢价率（用于双低策略计算，但不输出到最终结果）
        if 'stock_close' in result.columns and 'conv_price' in result.columns:
            # 转股价值 = 正股原始价格 * 100 / 转股价 (使用原始价格，避免前复权影响)
            conversion_value = result['stock_close'] * 100 / result['conv_price']

            # 转股溢价率 = (转债价格 / 转股价值) - 1
            bond_price = result['full_price'] if 'full_price' in result.columns else result['close']
            premium_ratio_temp = (bond_price / conversion_value) - 1

            # 处理异常值
            premium_ratio_temp = premium_ratio_temp.clip(-0.5, 2.0)  # 限制在-50%到200%之间

        else:
            print("警告：缺少正股价格或转股价数据，使用默认溢价率")
            premium_ratio_temp = pd.Series([0.1] * len(result), index=result.index)  # 默认10%溢价率
        
        # 溢价因子：转股溢价率 * 100
        result['premium_factor'] = premium_ratio_temp * 100
        
        # 双低得分：加权组合
        result['double_low_score'] = alpha * result['price_factor'] + (1 - alpha) * result['premium_factor']

        # 只返回双低策略特有的因子，避免与基础价格因子计算器重复
        double_low_specific_columns = [
            'ts_code', 'trade_date',  # 必需的标识字段
            'price_factor', 'premium_factor',  # 双低策略核心因子
            'double_low_score', 'double_low_rank', 'double_low_percentile'  # 双低得分和排名
        ]

        # 只保留存在的列
        available_columns = [col for col in double_low_specific_columns if col in result.columns]
        result = result[available_columns]

        return result
    
    
    
    def _apply_single_bond_optimization(self, raw_factors: pd.DataFrame, 
                                      date: str, bond_code: str) -> pd.DataFrame:
        """应用单债券优化策略"""
        if raw_factors.empty:
            return raw_factors
        
        optimized_factors = raw_factors.copy()
        
        try:
            # 对于单债券，主要进行数据质量检查
            optimized_factors = self._fix_single_bond_data_quality(optimized_factors, bond_code)
            optimized_factors = self._check_single_bond_outliers(optimized_factors, bond_code)
        except Exception as e:
            self.logger.error(f"单债券优化过程中出错: {e}")
            optimized_factors = raw_factors
        
        return optimized_factors
    
    
    
    
    
    def _fix_single_bond_data_quality(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """修复单债券数据质量问题"""
        df_fixed = df.copy()
        
        # 对于单债券，主要检查数据完整性
        for col in df_fixed.select_dtypes(include=[np.number]).columns:
            if df_fixed[col].isnull().any():
                # 用前值填充
                df_fixed[col] = df_fixed[col].fillna(method='ffill')
                # 如果还有缺失值，用后值填充
                df_fixed[col] = df_fixed[col].fillna(method='bfill')
                # 如果还有缺失值，用0填充
                df_fixed[col] = df_fixed[col].fillna(0)
        
        return df_fixed
    
    def _check_single_bond_outliers(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """检查单债券异常值"""
        df_checked = df.copy()
        
        # 对于单债券，进行基本的异常值检查
        for col in df_checked.select_dtypes(include=[np.number]).columns:
            # 检查极端值（例如负的价格）
            if 'price' in col.lower() or 'value' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=0)
            
            # 检查百分比字段
            if 'ratio' in col.lower() or 'rate' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=-1, upper=10)  # 限制在合理范围内
        
        return df_checked
    
