"""
动量因子计算器
计算可转债的价格动量因子
"""

import pandas as pd
import numpy as np
import sqlite3
import time
import logging
from typing import Dict, Any
from scipy import stats
from sklearn.preprocessing import QuantileTransformer

from .base_factor_calculator import FactorCalculator


class MomentumFactorCalculator(FactorCalculator):
    """
    动量因子计算器
    
    计算可转债的价格动量因子：
    - 5日动量
    - 10日动量
    - 20日动量
    """
    
    def __init__(self):
        """初始化动量因子计算器"""
        super().__init__("动量因子计算器")
        print("动量因子计算器初始化完成")
    
    
    def calculate_factors_for_date(self, date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期的动量因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            **kwargs: 其他参数
            
        Returns:
            包含动量因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🔄 计算动量因子 - 日期: {date_str}")
        start_time = time.time()
        
        # 获取可转债数据
        df = self._get_cb_data_for_date(date_str)
        
        if df.empty:
            print(f"日期 {date_str} 没有可用的可转债数据")
            return pd.DataFrame()
        
        print(f"找到 {len(df)} 只可转债")
        
        # 计算动量因子
        df = self._calculate_momentum_factors(df, date_str)
        
        # 注意：因子计筗器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ 动量因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def calculate_factors_for_period(self, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        计算指定时间段的动量因子
        
        Args:
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD
            **kwargs: 其他参数
            
        Returns:
            包含动量因子的DataFrame
        """
        print(f"\n🔄 计算动量因子 - 时间段: {start_date} 到 {end_date}")
        
        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')
        
        # 获取交易日
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)
        
        if not trading_dates:
            print("指定时间段内没有交易日数据")
            return pd.DataFrame()
        
        print(f"找到 {len(trading_dates)} 个交易日")
        
        all_factors = []
        
        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")
            
            try:
                daily_factors = self.calculate_factors_for_date(trade_date, **kwargs)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                self.logger.error(f"处理日期 {trade_date} 时出错: {e}")
                continue
        
        if not all_factors:
            print("没有成功计算出任何因子数据")
            return pd.DataFrame()
        
        # 合并所有日期的数据
        result_df = pd.concat(all_factors, ignore_index=True)
        
        # 注意：因子计筗器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        print(f"✅ 动量因子计算完成，共 {len(result_df)} 条记录")
        
        return result_df
    
    def calculate_factors_for_single_bond(self, date: str, bond_code: str, **kwargs) -> pd.DataFrame:
        """
        计算指定日期单个可转债的动量因子
        
        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            bond_code: 可转债代码，如 '110059.SH'
            **kwargs: 其他参数
            
        Returns:
            包含单个债券动量因子的DataFrame
        """
        date_str = self._standardize_date(date)
        print(f"\n🎯 计算单债券动量因子 - 日期: {date_str}, 债券: {bond_code}")
        start_time = time.time()
        
        # 获取指定可转债的数据
        df = self._get_single_cb_data(date_str, bond_code)
        
        if df.empty:
            raise ValueError(f"债券 {bond_code} 在日期 {date_str} 没有找到数据")
        
        print(f"找到债券: {bond_code}")
        
        # 计算动量因子
        df = self._calculate_momentum_factors(df, date_str)
        
        # 注意：因子计筗器只负责计算原始因子数据，不进行预处理
        # 预处理在ML模型训练/预测时进行
        
        elapsed_time = time.time() - start_time
        print(f"✅ 单债券动量因子计算完成，用时 {elapsed_time:.2f} 秒")
        
        return df
    
    def _get_cb_data_for_date(self, date_str: str) -> pd.DataFrame:
        """
        获取指定日期的可转债数据（动量因子专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD

        Returns:
            包含可转债数据的DataFrame
        """
        # 使用基类的通用方法，动量因子只需要基本字段
        return super()._get_cb_data_for_date(
            date_str=date_str,
            fields="basic",
            additional_filters="",
            strict_validation=False
        )
    
    def _get_single_cb_data(self, date_str: str, bond_code: str) -> pd.DataFrame:
        """
        获取指定日期单个可转债的数据（动量因子专用）

        Args:
            date_str: 日期字符串，格式YYYYMMDD
            bond_code: 可转债代码

        Returns:
            包含单个可转债数据的DataFrame
        """
        # 使用基类的通用方法，动量因子只需要基本字段
        return super()._get_single_cb_data(
            date_str=date_str,
            bond_code=bond_code,
            fields="basic",
            additional_filters="",
            strict_validation=False
        )
    
    def _calculate_momentum_factors(self, df: pd.DataFrame, date_str: str) -> pd.DataFrame:
        """
        计算动量因子
        
        Args:
            df: 包含可转债数据的DataFrame
            date_str: 日期字符串
            
        Returns:
            添加了动量因子的DataFrame
        """
        if df.empty:
            return df
        
        result = df.copy()
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取历史价格数据（使用收盘价）
            ts_codes = "','".join(df['ts_code'].unique())
            query = f"""
            SELECT ts_code, trade_date, close, full_price
            FROM cb_daily
            WHERE ts_code IN ('{ts_codes}')
            AND trade_date <= '{date_str}'
            ORDER BY ts_code, trade_date DESC
            """
            
            hist_data = pd.read_sql(query, conn)
            
            # NaN值处理
            if hist_data['full_price'].isna().any():
                print(f"警告：历史数据中有full_price NaN值")
                hist_data['full_price'] = hist_data['full_price'].fillna(method='ffill')
            
            conn.close()
            
            if hist_data.empty:
                # 如果没有历史数据，设置默认值
                result['momentum_5d'] = 0.0
                result['momentum_10d'] = 0.0
                result['momentum_20d'] = 0.0
                return result
            
            # 计算动量因子
            momentum_results = []
            
            for ts_code in df['ts_code'].unique():
                ts_hist = hist_data[hist_data['ts_code'] == ts_code].head(21)  # 最多21天
                
                if len(ts_hist) >= 6:
                    # 使用收盘价计算动量
                    prices = ts_hist['full_price'].values
                    
                    # 5日动量
                    if len(prices) >= 6:
                        momentum_5d = (prices[0] / prices[5] - 1) if prices[5] > 0 else 0
                    else:
                        momentum_5d = 0
                    
                    # 10日动量
                    if len(prices) >= 11:
                        momentum_10d = (prices[0] / prices[10] - 1) if prices[10] > 0 else 0
                    else:
                        momentum_10d = 0
                    
                    # 20日动量
                    if len(prices) >= 21:
                        momentum_20d = (prices[0] / prices[20] - 1) if prices[20] > 0 else 0
                    else:
                        momentum_20d = 0
                    
                    momentum_results.append({
                        'ts_code': ts_code,
                        'momentum_5d': momentum_5d,
                        'momentum_10d': momentum_10d,
                        'momentum_20d': momentum_20d
                    })
            
            if momentum_results:
                momentum_df = pd.DataFrame(momentum_results)
                result = result.merge(momentum_df, on='ts_code', how='left')
            
            # 填充缺失值
            result['momentum_5d'] = result['momentum_5d'].fillna(0)
            result['momentum_10d'] = result['momentum_10d'].fillna(0)
            result['momentum_20d'] = result['momentum_20d'].fillna(0)

            # 只返回动量因子特有的字段，避免与基础价格因子计算器重复
            momentum_specific_columns = [
                'ts_code', 'trade_date',  # 必需的标识字段
                'momentum_5d', 'momentum_10d', 'momentum_20d'  # 动量因子
            ]

            # 只保留存在的列
            available_columns = [col for col in momentum_specific_columns if col in result.columns]
            result = result[available_columns]

            return result

        except Exception as e:
            self.logger.warning(f"计算动量因子失败: {e}")
            # 返回默认的动量因子
            momentum_result = result[['ts_code', 'trade_date']].copy()
            momentum_result['momentum_5d'] = 0.0
            momentum_result['momentum_10d'] = 0.0
            momentum_result['momentum_20d'] = 0.0
            return momentum_result
    
    
    def _apply_single_bond_optimization(self, raw_factors: pd.DataFrame, 
                                      date: str, bond_code: str) -> pd.DataFrame:
        """应用单债券优化策略"""
        if raw_factors.empty:
            return raw_factors
        
        optimized_factors = raw_factors.copy()
        
        try:
            # 对于单债券，主要进行数据质量检查
            optimized_factors = self._fix_single_bond_data_quality(optimized_factors, bond_code)
            optimized_factors = self._check_single_bond_outliers(optimized_factors, bond_code)
        except Exception as e:
            self.logger.error(f"单债券优化过程中出错: {e}")
            optimized_factors = raw_factors
        
        return optimized_factors
    
    
    
    
    
    def _fix_single_bond_data_quality(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """修复单债券数据质量问题"""
        df_fixed = df.copy()
        
        # 对于单债券，主要检查数据完整性
        for col in df_fixed.select_dtypes(include=[np.number]).columns:
            if df_fixed[col].isnull().any():
                # 用前值填充
                df_fixed[col] = df_fixed[col].fillna(method='ffill')
                # 如果还有缺失值，用后值填充
                df_fixed[col] = df_fixed[col].fillna(method='bfill')
                # 如果还有缺失值，用0填充
                df_fixed[col] = df_fixed[col].fillna(0)
        
        return df_fixed
    
    def _check_single_bond_outliers(self, df: pd.DataFrame, bond_code: str) -> pd.DataFrame:
        """检查单债券异常值"""
        df_checked = df.copy()
        
        # 对于单债券，进行基本的异常值检查
        for col in df_checked.select_dtypes(include=[np.number]).columns:
            # 检查极端值（例如负的价格）
            if 'price' in col.lower() or 'value' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=0)
            
            # 检查百分比字段
            if 'ratio' in col.lower() or 'rate' in col.lower():
                df_checked[col] = df_checked[col].clip(lower=-1, upper=10)  # 限制在合理范围内
        
        return df_checked
    
