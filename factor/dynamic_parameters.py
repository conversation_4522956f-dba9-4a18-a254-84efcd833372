#!/usr/bin/env python3
"""
动态参数获取模块
通过市场环境动态获取ZL模型参数，包括无风险利率、赎回概率等
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import logging
from typing import Dict, Optional, Tuple
from config.config import config_manager

class DynamicParameterManager:
    """动态参数管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = config_manager.get_db_path()
        
        # 参数缓存，避免重复计算
        self.parameter_cache = {}
        self.cache_date = None
        
    def get_risk_free_rate(self, date: str) -> float:
        """
        获取无风险利率（基于国债收益率）
        
        Args:
            date: 日期字符串，格式YYYYMMDD
            
        Returns:
            无风险利率（年化）
        """
        try:
            # 尝试使用akshare获取国债收益率
            import akshare as ak
            
            # 获取中美国债收益率数据
            try:
                # 为了提高效率，我们获取一个较长时期的数据，避免频繁请求
                start_of_year = date[:4] + "0101"
                bond_data = ak.bond_zh_us_rate(start_date=start_of_year)
                if not bond_data.empty:
                    # 使用10年期国债收益率作为无风险利率
                    # 找到小于等于当前日期的最新数据
                    bond_data['日期'] = pd.to_datetime(bond_data['日期'])
                    target_date = pd.to_datetime(date, format='%Y%m%d')
                    latest_data = bond_data[bond_data['日期'] <= target_date].iloc[-1]

                    if pd.notna(latest_data['中国国债收益率10年']):
                        rate = latest_data['中国国债收益率10年'] / 100  # 转换为小数
                        self.logger.info(f"从akshare获取无风险利率: {rate:.4f}")
                        return rate
            except Exception as e:
                self.logger.warning(f"akshare获取国债收益率失败: {e}")
            
            # 备用方案：根据历史经验和当前市场环境估算
            year = int(date[:4])
            month = int(date[4:6])
            
            # 2024年市场环境下的估算
            if year >= 2024:
                base_rate = 0.025  # 2.5%基准
                # 根据月份微调（考虑货币政策周期）
                if month in [1, 2, 12]:  # 年初年末通常利率较低
                    adjustment = -0.002
                elif month in [6, 7, 8]:  # 年中通常利率较高
                    adjustment = 0.003
                else:
                    adjustment = 0.0
                
                final_rate = base_rate + adjustment
                self.logger.info(f"使用估算无风险利率: {final_rate:.4f}")
                return final_rate
            else:
                # 历史数据使用固定值
                return 0.03
                
        except Exception as e:
            self.logger.error(f"获取无风险利率失败: {e}")
            return 0.025  # 默认值
    
    def calculate_redeem_probability(self, date: str) -> float:
        """
        基于历史数据和当前市场热度计算赎回概率
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询当天所有可转债的收盘价
            query = "SELECT close FROM cb_daily WHERE trade_date = ?"
            df = pd.read_sql(query, conn, params=[date])
            conn.close()
            
            if df.empty:
                self.logger.warning(f"日期 {date} 没有找到可转债数据，无法计算赎回概率")
                return 0.45  # 默认值
            
            # 计算赎回压力分数
            # 当价格 > 120 时开始有压力，在 140 时压力达到最大
            pressure_scores = (df['close'] - 120) / 20
            pressure_scores = pressure_scores.clip(0, 1) # 将分数限制在0到1之间
            
            # 平均赎回压力
            avg_pressure = pressure_scores.mean()
            
            # 将压力分数映射到概率
            # 基础概率0.2，最大额外增加0.6
            base_probability = 0.20
            redeem_prob = base_probability + avg_pressure * 0.6
            
            self.logger.info(f"计算赎回概率: {redeem_prob:.4f} (基于平均赎回压力: {avg_pressure:.4f})")
            return redeem_prob
                
        except Exception as e:
            self.logger.error(f"计算赎回概率失败: {e}")
            return 0.45  # 默认值

    def calculate_revision_probability(self, date: str) -> float:
        """
        基于市场低价券数量计算下修概率
        """
        try:
            conn = sqlite3.connect(self.db_path)
            query = "SELECT close FROM cb_daily WHERE trade_date = ?"
            df = pd.read_sql(query, conn, params=[date])
            conn.close()

            if df.empty:
                return 0.14 # 默认值

            # 计算价格低于95元的转债比例
            low_price_ratio = len(df[df['close'] < 95]) / len(df)

            # 将比例映射到概率
            # 基础概率0.05，最大额外增加0.3
            base_probability = 0.05
            revision_prob = base_probability + low_price_ratio * 0.3
            
            self.logger.info(f"计算下修概率: {revision_prob:.4f} (基于低价券比例: {low_price_ratio:.4f})")
            return revision_prob

        except Exception as e:
            self.logger.error(f"计算下修概率失败: {e}")
            return 0.14 # 默认值

    def calculate_putback_probability(self, date: str) -> float:
        """
        基于市场极低价券数量计算回售概率
        """
        try:
            conn = sqlite3.connect(self.db_path)
            query = "SELECT close FROM cb_daily WHERE trade_date = ?"
            df = pd.read_sql(query, conn, params=[date])
            conn.close()

            if df.empty:
                return 0.22 # 默认值

            # 计算价格低于85元的转债比例
            very_low_price_ratio = len(df[df['close'] < 85]) / len(df)

            # 将比例映射到概率
            # 基础概率0.1，最大额外增加0.3
            base_probability = 0.10
            putback_prob = base_probability + very_low_price_ratio * 0.3

            self.logger.info(f"计算回售概率: {putback_prob:.4f} (基于极低价券比例: {very_low_price_ratio:.4f})")
            return putback_prob

        except Exception as e:
            self.logger.error(f"计算回售概率失败: {e}")
            return 0.22 # 默认值

    def calculate_market_volatility(self, date: str, lookback_days: int = 30) -> float:
        """
        计算市场波动率
        
        Args:
            date: 当前日期
            lookback_days: 回看天数
            
        Returns:
            年化波动率
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 计算回看期间
            current_date = datetime.strptime(date, '%Y%m%d')
            start_date = current_date - timedelta(days=lookback_days)
            start_date_str = start_date.strftime('%Y%m%d')
            
            # 获取可转债市场整体数据
            query = """
            SELECT trade_date, AVG(close) as avg_price
            FROM cb_daily 
            WHERE trade_date >= ? AND trade_date <= ?
            GROUP BY trade_date
            ORDER BY trade_date
            """
            
            df = pd.read_sql(query, conn, params=[start_date_str, date])
            conn.close()
            
            if len(df) < 5:
                return 0.3  # 默认波动率
            
            # 计算日收益率
            df['returns'] = df['avg_price'].pct_change()
            
            # 计算年化波动率
            daily_vol = df['returns'].std()
            annual_vol = daily_vol * np.sqrt(252)  # 年化
            
            # 限制波动率范围
            annual_vol = max(0.1, min(0.8, annual_vol))
            
            self.logger.info(f"计算市场波动率: {annual_vol:.4f}")
            return annual_vol
            
        except Exception as e:
            self.logger.error(f"计算市场波动率失败: {e}")
            return 0.3
    
    def get_dynamic_parameters(self, date: str) -> Dict[str, float]:
        """
        获取指定日期的动态参数
        
        Args:
            date: 日期字符串
            
        Returns:
            参数字典
        """
        # 检查缓存
        if self.cache_date == date and self.parameter_cache:
            return self.parameter_cache.copy()
        
        self.logger.info(f"计算{date}的动态参数")
        
        parameters = {
            'risk_free_rate': self.get_risk_free_rate(date),
            'redeem_probability': self.calculate_redeem_probability(date),
            'market_volatility': self.calculate_market_volatility(date),
            'putback_probability': self.calculate_putback_probability(date),
            'revision_probability': self.calculate_revision_probability(date),
        }
        
        # 缓存结果
        self.parameter_cache = parameters.copy()
        self.cache_date = date
        
        self.logger.info(f"动态参数: {parameters}")
        return parameters
    
    def update_monthly_parameters(self, year_month: str) -> Dict[str, float]:
        """
        每月更新参数（可以设置定时任务调用）
        
        Args:
            year_month: 年月字符串，格式YYYYMM
            
        Returns:
            更新后的参数
        """
        # 使用月末日期
        date = year_month + "28"  # 简化处理，使用28号
        
        parameters = self.get_dynamic_parameters(date)
        
        # 可以在这里保存到配置文件或数据库
        self.logger.info(f"{year_month}月参数更新完成: {parameters}")
        
        return parameters

# 全局实例
dynamic_param_manager = DynamicParameterManager()