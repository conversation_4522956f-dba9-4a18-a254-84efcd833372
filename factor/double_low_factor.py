"""
双低策略因子计算模块
实现"低价格"和"低转股溢价"的双低策略因子计算
基于advice_2.md中的量化思路
"""

import pandas as pd
import sqlite3
import warnings

from config.config import config_manager

warnings.filterwarnings('ignore')


class DoubleLowFactorCalculator:
    """
    双低策略因子计算器

    实现简化的双低策略逻辑：
    1. 价格因子：转债当前价格
    2. 溢价因子：转股溢价率 * 100
    3. 双低因子：价格因子 + 溢价因子（从小到大排序）
    """

    def __init__(self):
        """初始化双低策略因子计算器"""
        self.db_path = config_manager.get_db_path()
        print("双低策略因子计算器初始化完成")

    def calculate_double_low_score(self, df, alpha=0.5):
        """
        计算双低策略得分

        双低得分 = α * 价格因子 + (1-α) * 溢价因子
        其中：价格因子 = 转债价格，溢价因子 = 转股溢价率 * 100
        得分越小越好（低价格 + 低溢价）

        Args:
            df: 包含转债和正股数据的DataFrame
            alpha: 价格因子权重，范围[0,1]，默认0.5

        Returns:
            DataFrame: 添加了双低得分的数据
        """
        # 价格因子：直接使用转债价格
        df['price_factor'] = df['close']

        # 检查是否已有转股溢价率，避免重复计算
        if 'premium_ratio' not in df.columns:
            print("基础计算器未提供转股溢价率，开始计算...")
            # 计算转股溢价率
            if 'stock_close' in df.columns and 'conv_price' in df.columns:
                # 转股价值 = 正股原始价格 * 100 / 转股价 (使用原始价格，避免前复权影响)
                # 注意：这里应该使用正股的org_close，但当前数据结构中可能还是stock_close
                df['conversion_value'] = df['stock_close'] * 100 / df['conv_price']

                # 转股溢价率 = (转债价格 / 转股价值) - 1
                df['premium_ratio'] = (df['close'] / df['conversion_value']) - 1

                # 处理异常值
                df['premium_ratio'] = df['premium_ratio'].clip(-0.5, 2.0)  # 限制在-50%到200%之间

            else:
                print("警告：缺少正股价格或转股价数据，使用默认溢价率")
                df['premium_ratio'] = 0.1  # 默认10%溢价率
        else:
            print("使用基础计算器提供的转股溢价率，跳过重复计算")

        # 溢价因子：转股溢价率 * 100
        df['premium_factor'] = df['premium_ratio'] * 100

        # 双低得分：加权组合
        df['double_low_score'] = alpha * df['price_factor'] + (1 - alpha) * df['premium_factor']

        return df
    
    def calculate_factors_for_date(self, date, alpha=0.5):
        """
        计算指定日期的双低策略因子

        Args:
            date: 日期，格式为YYYY-MM-DD或YYYYMMDD
            alpha: 价格因子权重，范围[0,1]，默认0.5

        Returns:
            DataFrame: 包含双低策略因子的数据
        """
        # 标准化日期格式
        if isinstance(date, str):
            if '-' in date:
                date_str = date.replace('-', '')
            else:
                date_str = date
        else:
            date_str = date.strftime('%Y%m%d')

        print(f"\n计算双低策略因子 - 日期: {date_str}")

        # 获取数据
        df = self._get_data_for_date(date_str)

        if df.empty:
            print(f"日期 {date_str} 没有可用数据")
            return pd.DataFrame()

        print(f"找到 {len(df)} 只可转债")

        # 计算双低得分
        df = self.calculate_double_low_score(df, alpha)

        # 添加排名信息（从小到大排序，得分越小排名越高）
        df['double_low_rank'] = df['double_low_score'].rank(ascending=True)
        df['double_low_percentile'] = df['double_low_score'].rank(pct=True, ascending=True)

        print(f"双低策略因子计算完成，α={alpha:.2f}")

        return df
    
    def calculate_factors_for_period(self, start_date, end_date, alpha=0.5):
        """
        计算指定时间段的双低策略因子

        Args:
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
            alpha: 价格因子权重，范围[0,1]，默认0.5

        Returns:
            DataFrame: 包含双低策略因子的数据
        """
        print(f"\n计算双低策略因子 - 时间段: {start_date} 到 {end_date}")

        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')

        # 获取时间段内的所有交易日
        trading_dates = self._get_trading_dates(start_date_str, end_date_str)

        if not trading_dates:
            print("指定时间段内没有交易日数据")
            return pd.DataFrame()

        print(f"找到 {len(trading_dates)} 个交易日")

        all_factors = []

        for i, trade_date in enumerate(trading_dates):
            print(f"处理日期 {trade_date} ({i+1}/{len(trading_dates)})")

            try:
                daily_factors = self.calculate_factors_for_date(trade_date, alpha)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                print(f"处理日期 {trade_date} 时出错: {e}")
                continue

        if not all_factors:
            print("没有成功计算出任何因子数据")
            return pd.DataFrame()

        # 合并所有日期的数据
        result_df = pd.concat(all_factors, ignore_index=True)

        print(f"双低策略因子计算完成，共 {len(result_df)} 条记录")

        return result_df
    
    def _get_data_for_date(self, date_str):
        """
        获取指定日期的可转债数据
        
        Args:
            date_str: 日期字符串，格式YYYYMMDD
            
        Returns:
            DataFrame: 包含可转债基本信息、日行情和正股数据
        """
        conn = sqlite3.connect(self.db_path)
        
        try:
            # 获取可转债日行情和基本信息，应用双低策略过滤条件
            query = """
            SELECT
                cd.ts_code,
                cd.trade_date,
                cd.close,
                cd.vol,
                cd.amount,
                cb.conv_price,
                cb.stk_code,
                cb.bond_short_name,
                cb.delist_date,
                cb.list_date,
                cb.remain_size
            FROM cb_daily cd
            JOIN cb_basic cb ON cd.ts_code = cb.ts_code
            WHERE cd.trade_date = ?
            AND cd.close > 0
            AND cb.conv_price > 0
            AND (cb.delist_date IS NULL OR cb.delist_date = '' OR cb.delist_date > ?)
            AND cb.list_date IS NOT NULL
            AND cb.remain_size IS NOT NULL
            AND cb.remain_size >= 3000
            AND (
                CASE
                    WHEN LENGTH(cb.list_date) = 8 THEN
                        JULIANDAY(SUBSTR(?, 1, 4) || '-' || SUBSTR(?, 5, 2) || '-' || SUBSTR(?, 7, 2)) -
                        JULIANDAY(SUBSTR(cb.list_date, 1, 4) || '-' || SUBSTR(cb.list_date, 5, 2) || '-' || SUBSTR(cb.list_date, 7, 2)) >= 10
                    ELSE 0
                END
            )
            ORDER BY cd.ts_code
            """
            
            df = pd.read_sql(query, conn, params=[date_str, date_str, date_str, date_str, date_str])

            if df.empty:
                return df

            # 不需要处理全价数据，直接使用收盘价
            
            # 获取对应的正股数据
            stock_data = self._get_stock_data_for_date(df, date_str, conn)
            
            # 合并正股数据
            if not stock_data.empty:
                df = pd.merge(df, stock_data, on='stk_ts_code', how='left', suffixes=('', '_stock'))
                df.rename(columns={'close_stock': 'stock_close'}, inplace=True)
            
            return df
            
        except Exception as e:
            print(f"获取数据时出错: {e}")
            return pd.DataFrame()
        finally:
            conn.close()
    
    def _get_stock_data_for_date(self, cb_df, date_str, conn):
        """
        获取正股数据
        
        Args:
            cb_df: 可转债数据DataFrame
            date_str: 日期字符串
            conn: 数据库连接
            
        Returns:
            DataFrame: 正股数据
        """
        # 构建正股代码列表
        # stk_code字段已经包含交易所后缀，直接使用
        cb_df['stk_ts_code'] = cb_df['stk_code']

        stock_codes = cb_df['stk_ts_code'].unique().tolist()

        if not stock_codes:
            return pd.DataFrame()

        # 构建查询语句
        placeholders = ','.join(['?' for _ in stock_codes])
        stock_query = f"""
        SELECT ts_code as stk_ts_code, close
        FROM stock_daily
        WHERE trade_date = ? AND ts_code IN ({placeholders})
        """

        params = [date_str] + stock_codes

        try:
            stock_df = pd.read_sql(stock_query, conn, params=params)
            print(f"获取到 {len(stock_df)} 只正股数据")
            return stock_df
        except Exception as e:
            print(f"获取正股数据失败: {e}")
            return pd.DataFrame()
    
    def _get_trading_dates(self, start_date_str, end_date_str):
        """
        获取指定时间段内的交易日
        
        Args:
            start_date_str: 开始日期，格式YYYYMMDD
            end_date_str: 结束日期，格式YYYYMMDD
            
        Returns:
            list: 交易日列表
        """
        conn = sqlite3.connect(self.db_path)
        
        try:
            query = """
            SELECT DISTINCT trade_date
            FROM cb_daily
            WHERE trade_date >= ? AND trade_date <= ?
            ORDER BY trade_date
            """
            
            df = pd.read_sql(query, conn, params=[start_date_str, end_date_str])
            return df['trade_date'].tolist()
            
        except Exception as e:
            print(f"获取交易日时出错: {e}")
            return []
        finally:
            conn.close()


class DoubleLowOptimizer:
    """
    双低策略因子分析器
    分析双低因子的质量和分布特征
    """

    def __init__(self, factor_calculator):
        """
        初始化分析器

        Args:
            factor_calculator: DoubleLowFactorCalculator实例
        """
        self.factor_calculator = factor_calculator

    def analyze_factor_quality(self, start_date, end_date):
        """
        分析双低因子质量

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            dict: 包含因子质量分析结果
        """
        print(f"开始分析双低因子质量")

        try:
            # 计算因子
            factors_df = self.factor_calculator.calculate_factors_for_period(
                start_date, end_date
            )

            if factors_df.empty:
                print("没有可用的因子数据")
                return None

            # 评估因子质量
            score_std = factors_df['double_low_score'].std()
            score_mean = factors_df['double_low_score'].mean()
            score_range = factors_df['double_low_score'].max() - factors_df['double_low_score'].min()

            # 计算因子的区分度（变异系数）
            discrimination = score_std / score_mean if score_mean != 0 else 0

            # 价格因子和溢价因子的统计
            price_std = factors_df['price_factor'].std()
            price_mean = factors_df['price_factor'].mean()
            premium_std = factors_df['premium_factor'].std()
            premium_mean = factors_df['premium_factor'].mean()

            result = {
                'total_data_points': len(factors_df),
                'score_mean': score_mean,
                'score_std': score_std,
                'score_range': score_range,
                'discrimination': discrimination,
                'price_factor_mean': price_mean,
                'price_factor_std': price_std,
                'premium_factor_mean': premium_mean,
                'premium_factor_std': premium_std
            }

            print(f"因子质量分析完成")
            print(f"数据点数量: {result['total_data_points']}")
            print(f"双低得分均值: {result['score_mean']:.2f}")
            print(f"双低得分标准差: {result['score_std']:.2f}")
            print(f"因子区分度: {result['discrimination']:.4f}")

            return result

        except Exception as e:
            print(f"因子质量分析失败: {e}")
            return None
