#!/usr/bin/env python3
"""
样本权重计算工具模块
提供通用的样本权重计算函数，用于机器学习模型训练
"""

import pandas as pd
import numpy as np


def calculate_sample_weights(df_filtered, target_factor, trade_date_col='trade_date'):
    """
    计算样本权重：对每一天按目标因子值进行百分比排名，然后映射到0.01到1之间
    
    Args:
        df_filtered: 经过过滤的DataFrame，包含trade_date, ts_code和target_factor
        target_factor: 目标因子列名
        trade_date_col: 交易日期列名，默认为'trade_date'
        
    Returns:
        sample_weights: numpy数组，样本权重
    """
    print("正在计算样本权重...")
    
    # 按日期分组，计算每日的百分比排名
    df_with_weights = df_filtered.copy()
    
    # 计算每日的百分比排名（0到1之间）
    df_with_weights['daily_rank'] = df_with_weights.groupby(trade_date_col)[target_factor].rank(pct=True)
    
    # 将排名映射到权重：排名越极端（接近0或1），权重越高
    # abs(rank * 2 - 1) 将 [0,1] 映射到 [1,0,1]，即两端高，中间低
    df_with_weights['sample_weight'] = np.abs(df_with_weights['daily_rank'] * 2 - 1)
    
    # 将权重范围调整到 [0.01, 1]，避免权重为0
    df_with_weights['sample_weight'] = df_with_weights['sample_weight'] * 0.99 + 0.01
    
    # 提取权重数组
    sample_weights = df_with_weights['sample_weight'].values
    
    # 检查和清理权重数组
    if np.any(np.isnan(sample_weights)) or np.any(np.isinf(sample_weights)):
        print("检测到权重中的NaN或无穷值，进行清理...")
        # 将NaN或无穷值替换为0.01（最小权重）
        sample_weights = np.nan_to_num(sample_weights, nan=0.01, posinf=1, neginf=0.01)
    
    # 确保权重在有效范围内
    sample_weights = np.clip(sample_weights, 0.01, 1)
    
    print(f"样本权重范围: [{sample_weights.min():.4f}, {sample_weights.max():.4f}]")
    print(f"权重分布: 低权重样本(<0.5)={np.sum(sample_weights < 0.5)}, 高权重样本(>0.5)={np.sum(sample_weights > 0.5)}")
    print(f"权重验证: NaN数量={np.sum(np.isnan(sample_weights))}, 无穷值数量={np.sum(np.isinf(sample_weights))}")
    
    return sample_weights


def add_sample_weights_to_dataframe(df, target_factor, trade_date_col='trade_date', weight_col='sample_weight'):
    """
    将样本权重添加到DataFrame中
    
    Args:
        df: 输入DataFrame
        target_factor: 目标因子列名
        trade_date_col: 交易日期列名，默认为'trade_date'
        weight_col: 权重列名，默认为'sample_weight'
        
    Returns:
        添加了权重列的DataFrame
    """
    df_with_weights = df.copy()
    
    # 计算样本权重
    sample_weights = calculate_sample_weights(df, target_factor, trade_date_col)
    
    # 添加权重列
    df_with_weights[weight_col] = sample_weights
    
    return df_with_weights