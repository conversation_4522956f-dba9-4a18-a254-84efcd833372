#!/usr/bin/env python3
"""
字体工具模块
提供跨平台的中文字体设置功能
"""

import platform
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm


def setup_chinese_font():
    """
    设置中文字体，兼容不同操作系统
    
    自动检测操作系统并选择合适的中文字体：
    - macOS: Arial Unicode MS, PingFang SC, Hiragino Sans GB, STHeiti
    - Windows: SimHei, Microsoft YaHei, SimSun
    - Linux: DejaVu Sans, WenQuanYi Micro Hei, SimHei
    
    如果没有找到中文字体，会使用默认字体并给出警告
    """
    system = platform.system()
    
    if system == "Darwin":  # macOS
        # macOS 系统的中文字体
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'SimHei']
    elif system == "Windows":  # Windows
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux
        chinese_fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 找到第一个可用的中文字体
    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            break
    else:
        # 如果没有找到中文字体，使用默认字体并禁用中文显示警告
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("警告：未找到合适的中文字体，图表标题可能显示为方框")
    
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号


def get_available_chinese_fonts():
    """
    获取系统中可用的中文字体列表
    
    Returns:
        list: 可用的中文字体名称列表
    """
    system = platform.system()
    
    if system == "Darwin":  # macOS
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'SimHei']
    elif system == "Windows":  # Windows
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux
        chinese_fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 返回可用的中文字体
    return [font for font in chinese_fonts if font in available_fonts]


def set_custom_font(font_name):
    """
    设置自定义字体
    
    Args:
        font_name (str): 字体名称
        
    Returns:
        bool: 设置是否成功
    """
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    if font_name in available_fonts:
        plt.rcParams['font.sans-serif'] = [font_name]
        plt.rcParams['axes.unicode_minus'] = False
        return True
    else:
        print(f"警告：字体 '{font_name}' 不可用")
        return False


def print_font_info():
    """
    打印当前字体设置信息
    """
    print(f"操作系统: {platform.system()}")
    print(f"当前设置的字体: {plt.rcParams['font.sans-serif']}")
    print(f"可用的中文字体: {get_available_chinese_fonts()}")


if __name__ == "__main__":
    # 测试字体设置
    print("=== 字体设置测试 ===")
    print_font_info()
    
    print("\n设置中文字体...")
    setup_chinese_font()
    
    print("\n设置后的字体信息:")
    print_font_info()
