#!/usr/bin/env python3
"""
特征分析工具
使用alphalens对每个特征进行详细分析，包括IC值、收益率分析等
"""

import pandas as pd
import numpy as np
import sqlite3
import warnings
warnings.filterwarnings('ignore')
from sklearn.metrics import r2_score

try:
    import alphalens as al
except ImportError:
    print("正在安装alphalens...")
    import subprocess
    subprocess.check_call(["pip", "install", "alphalens-reloaded"])
    import alphalens as al

import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import os

class FeatureAnalyzer:
    """
    特征分析器
    使用alphalens对因子进行全面分析
    """
    
    def __init__(self, db_path: str = './data_storage/convertible_bond.db'):
        self.db_path = db_path
        self.results = {}
        
    def load_factor_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """
        从数据库加载因子数据
        
        Args:
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            
        Returns:
            因子数据DataFrame
        """
        start_date = start_date.replace('-', '')
        end_date = end_date.replace('-', '')
        
        conn = sqlite3.connect(self.db_path)
        
        query = """
        SELECT * FROM cb_factor 
        WHERE trade_date >= ? AND trade_date <= ?
        AND ts_code IS NOT NULL
        ORDER BY trade_date, ts_code
        """
        
        df = pd.read_sql(query, conn, params=(start_date, end_date))
        conn.close()
        
        if df.empty:
            raise ValueError(f"没有找到 {start_date} 到 {end_date} 期间的数据")
            
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
        
        return df
    
    def load_return_data(self, start_date: str, end_date: str, forward_days: int = 10) -> pd.DataFrame:
        """
        计算前瞻收益率数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期  
            forward_days: 前瞻天数
            
        Returns:
            包含收益率的DataFrame
        """
        start_date = start_date.replace('-', '')
        end_date = end_date.replace('-', '')
        
        conn = sqlite3.connect(self.db_path)
        
        # 获取价格数据
        query = """
        SELECT ts_code, trade_date, full_price
        FROM cb_daily 
        WHERE trade_date >= ? AND trade_date <= ?
        AND full_price > 0
        ORDER BY ts_code, trade_date
        """
        
        df = pd.read_sql(query, conn, params=(start_date, end_date))
        conn.close()
        
        if df.empty:
            raise ValueError(f"没有找到价格数据")
            
        df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
        
        # 计算前瞻收益率
        returns_list = []
        
        for ts_code, group in df.groupby('ts_code'):
            group = group.sort_values('trade_date').reset_index(drop=True)
            
            # 计算前瞻收益率
            group[f'return_{forward_days}d'] = group['full_price'].pct_change(periods=forward_days).shift(-forward_days)
            
            returns_list.append(group)
        
        result = pd.concat(returns_list, ignore_index=True)
        
        return result[['ts_code', 'trade_date', f'return_{forward_days}d']]
    
    def get_factor_list(self) -> List[str]:
        """
        获取所有可分析的因子列表
        
        Returns:
            因子名称列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(cb_factor)")
        columns = cursor.fetchall()
        conn.close()
        
        # 排除基础列
        exclude_columns = {'ts_code', 'trade_date', 'id'}
        factor_columns = [col[1] for col in columns if col[1] not in exclude_columns]
        
        return factor_columns
    
    def analyze_single_factor(self, factor_name: str, factor_data: pd.DataFrame, 
                            return_data: pd.DataFrame, forward_days: int = 10) -> Dict:
        """
        分析单个因子
        
        Args:
            factor_name: 因子名称
            factor_data: 因子数据
            return_data: 收益率数据
            forward_days: 前瞻天数
            
        Returns:
            分析结果字典
        """
        print(f"\n分析因子: {factor_name}")
        
        # 检查因子是否存在
        if factor_name not in factor_data.columns:
            print(f"  因子 {factor_name} 不存在")
            return {'error': f'因子 {factor_name} 不存在'}
        
        # 合并因子和收益率数据
        merged_data = pd.merge(
            factor_data[['ts_code', 'trade_date', factor_name]], 
            return_data, 
            on=['ts_code', 'trade_date'], 
            how='inner'
        )
        
        if merged_data.empty:
            print(f"  合并后无数据")
            return {'error': '合并后无数据'}
        
        # 检查数据质量
        factor_values = merged_data[factor_name]
        return_values = merged_data[f'return_{forward_days}d']
        
        # 统计信息
        total_count = len(factor_values)
        factor_null_count = factor_values.isnull().sum()
        return_null_count = return_values.isnull().sum()
        factor_null_pct = factor_null_count / total_count * 100
        return_null_pct = return_null_count / total_count * 100
        
        print(f"  数据统计:")
        print(f"    总样本数: {total_count:,}")
        print(f"    因子缺失: {factor_null_count:,} ({factor_null_pct:.1f}%)")
        print(f"    收益缺失: {return_null_count:,} ({return_null_pct:.1f}%)")
        
        # 如果缺失率过高，跳过分析
        if factor_null_pct > 90:
            print(f"  因子缺失率过高 ({factor_null_pct:.1f}%)，跳过分析")
            return {
                'factor_name': factor_name,
                'error': f'因子缺失率过高 ({factor_null_pct:.1f}%)',
                'total_count': total_count,
                'factor_null_pct': factor_null_pct,
                'return_null_pct': return_null_pct
            }
        
        # 删除缺失值
        clean_data = merged_data.dropna(subset=[factor_name, f'return_{forward_days}d'])
        
        if len(clean_data) < 100:
            print(f"  有效样本过少 ({len(clean_data)})，跳过分析")
            return {
                'factor_name': factor_name,
                'error': f'有效样本过少 ({len(clean_data)})',
                'total_count': total_count,
                'valid_count': len(clean_data)
            }
        
        print(f"    有效样本: {len(clean_data):,}")
        
        try:
            # 准备alphalens格式的数据
            # 确保trade_date是工作日频率
            clean_data_copy = clean_data.copy()
            clean_data_copy['trade_date'] = pd.to_datetime(clean_data_copy['trade_date'])
            
            # 创建因子序列，确保日期索引频率一致
            factor_series = clean_data_copy.set_index(['trade_date', 'ts_code'])[factor_name]
            
            # 确保因子数据的日期索引与价格数据一致
            # 重新索引到工作日频率
            factor_df = factor_series.reset_index()
            factor_df = factor_df.pivot(index='trade_date', columns='ts_code', values=factor_name)
            
            # 创建工作日频率的日期范围
            start_date_dt = factor_df.index.min()
            end_date_dt = factor_df.index.max()
            business_dates = pd.bdate_range(start=start_date_dt, end=end_date_dt)
            
            # 重新索引到工作日频率
            factor_df = factor_df.reindex(business_dates)
            
            # 转换回MultiIndex格式
            factor_series = factor_df.stack().dropna()
            factor_series.index.names = ['trade_date', 'ts_code']
            
            # 从数据库获取真实价格数据
            start_date_str = clean_data['trade_date'].min().strftime('%Y%m%d')
            end_date_str = clean_data['trade_date'].max().strftime('%Y%m%d')
            
            conn = sqlite3.connect(self.db_path)
            price_query = """
            SELECT ts_code, trade_date, full_price
            FROM cb_daily 
            WHERE trade_date >= ? AND trade_date <= ?
            AND full_price > 0
            ORDER BY trade_date, ts_code
            """
            
            price_df = pd.read_sql(price_query, conn, params=(start_date_str, end_date_str))
            conn.close()
            
            if price_df.empty:
                print(f"  无法获取价格数据")
                return {
                    'factor_name': factor_name,
                    'error': '无法获取价格数据'
                }
            
            price_df['trade_date'] = pd.to_datetime(price_df['trade_date'], format='%Y%m%d')
            
            # 构建价格矩阵
            prices = price_df.pivot(index='trade_date', columns='ts_code', values='full_price')
            prices = prices.ffill().bfill()
            
            # 确保价格数据不为空
            if prices.empty:
                print(f"  价格数据为空")
                return {
                    'factor_name': factor_name,
                    'error': '价格数据为空'
                }
            
            # 不强制设置频率，让alphalens自己处理
            # 只确保价格数据是排序的
            prices = prices.sort_index()
            
            # 使用简化的因子分析（绕过alphalens的bug）
            print(f"    使用简化因子分析方法...")
            
            # 直接计算IC（信息系数）
            factor_values = clean_data[factor_name]
            return_values = clean_data[f'return_{forward_days}d']
            
            # 计算各日期的IC
            daily_ic_list = []
            
            for date, group in clean_data.groupby('trade_date'):
                if len(group) >= 3:  # 至少需要3个样本
                    group_factor = group[factor_name]
                    group_return = group[f'return_{forward_days}d']
                    
                    # 计算相关系数作为IC
                    ic = group_factor.corr(group_return)
                    if not pd.isna(ic):
                        daily_ic_list.append(ic)
            
            if daily_ic_list:
                mean_ic = np.mean(daily_ic_list)
                ic_std = np.std(daily_ic_list)
                ic_ir = mean_ic / ic_std if ic_std != 0 else np.nan
            else:
                mean_ic = ic_std = ic_ir = np.nan
            
            print(f"    简化IC计算完成: {len(daily_ic_list)}个交易日")
            
            # 计算分层收益 - 根据IC值正负修正分层定义
            try:
                # 按因子值分成5个分位数
                clean_data['factor_quantile'] = pd.qcut(clean_data[factor_name], 5, labels=False, duplicates='drop')
                
                # 计算各分位数的平均收益
                quantile_returns = clean_data.groupby('factor_quantile')[f'return_{forward_days}d'].mean()
                
                if len(quantile_returns) >= 2:
                    # 获取最低和最高分位数的原始收益
                    lowest_quantile_return_raw = quantile_returns.iloc[0]   # 因子值最低分位数收益
                    highest_quantile_return_raw = quantile_returns.iloc[-1] # 因子值最高分位数收益
                    
                    # 转换为年化收益率
                    trading_days_per_year = 252
                    lowest_quantile_return = (1 + lowest_quantile_return_raw) ** (trading_days_per_year / forward_days) - 1
                    highest_quantile_return = (1 + highest_quantile_return_raw) ** (trading_days_per_year / forward_days) - 1
                    
                    # 根据IC值正负决定第1层和第5层的定义以及多空方向
                    if mean_ic >= 0:
                        # 正IC：因子值越大收益越高，第5层为高因子值（高预期收益），第1层为低因子值（低预期收益）
                        q1_return = lowest_quantile_return    # 第1层 = 因子值最低层（预期收益低）
                        q5_return = highest_quantile_return   # 第5层 = 因子值最高层（预期收益高）
                        long_short_return = q5_return - q1_return  # 做多第5层，做空第1层
                    else:
                        # 负IC：因子值越小收益越高，为保持第5层始终是最高预期收益，互换层级标签
                        q1_return = highest_quantile_return   # 第1层 = 因子值最高层（预期收益低）
                        q5_return = lowest_quantile_return    # 第5层 = 因子值最低层（预期收益高）
                        long_short_return = q5_return - q1_return  # 做多第5层，做空第1层
                else:
                    q1_return = q5_return = long_short_return = np.nan
                    
            except Exception as e:
                print(f"    分层收益计算失败: {e}")
                q1_return = q5_return = long_short_return = np.nan
            
            print(f"  分析结果:")
            print(f"    平均IC: {mean_ic:.4f}")
            print(f"    IC标准差: {ic_std:.4f}")
            print(f"    IC_IR: {ic_ir:.4f}")
            
            # 根据IC正负给出更清晰的层级解释
            if mean_ic >= 0:
                print(f"    第1层年化收益: {q1_return:.4f} (因子值低，预期收益低)")
                print(f"    第5层年化收益: {q5_return:.4f} (因子值高，预期收益高)")
                print(f"    多空年化收益: {long_short_return:.4f} (做多第5层，做空第1层)")
            else:
                print(f"    第1层年化收益: {q1_return:.4f} (因子值高，预期收益低)")
                print(f"    第5层年化收益: {q5_return:.4f} (因子值低，预期收益高)")
                print(f"    多空年化收益: {long_short_return:.4f} (做多第5层，做空第1层)")
            
            # 异常值检测
            factor_values_clean = clean_data[factor_name]
            q1 = factor_values_clean.quantile(0.25)
            q3 = factor_values_clean.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            outliers = ((factor_values_clean < lower_bound) | (factor_values_clean > upper_bound)).sum()
            outlier_pct = outliers / len(factor_values_clean) * 100
            
            print(f"    异常值: {outliers:,} ({outlier_pct:.1f}%)")
            
            # 计算横截面R²：按天的特征排序与收益排序的相关性
            print(f"  计算横截面R2...")
            cross_sectional_r2 = self._calculate_cross_sectional_r2(clean_data, factor_name, f'return_{forward_days}d')
            
            return {
                'factor_name': factor_name,
                'total_count': total_count,
                'valid_count': len(clean_data),
                'factor_null_pct': factor_null_pct,
                'return_null_pct': return_null_pct,
                'mean_ic': mean_ic,
                'ic_std': ic_std,
                'ic_ir': ic_ir,
                'q1_return': q1_return,
                'q5_return': q5_return,
                'long_short_return': long_short_return,
                'outliers': outliers,
                'outlier_pct': outlier_pct,
                'factor_mean': factor_values_clean.mean(),
                'factor_std': factor_values_clean.std(),
                'factor_min': factor_values_clean.min(),
                'factor_max': factor_values_clean.max(),
                'cross_sectional_r2': cross_sectional_r2
            }
            
        except Exception as e:
            print(f"  分析失败: {str(e)}")
            return {
                'factor_name': factor_name,
                'error': f'分析失败: {str(e)}',
                'total_count': total_count,
                'valid_count': len(clean_data) if 'clean_data' in locals() else 0
            }
    
    def _calculate_cross_sectional_r2(self, data, factor_name, target_name):
        """
        计算横截面R²：按天计算特征排序与收益排序的相关性
        
        Args:
            data: 包含因子和收益率的DataFrame
            factor_name: 因子名称
            target_name: 目标收益率名称
            
        Returns:
            float: 横截面R²的均值
        """
        try:
            if data.empty:
                return 0.0
            
            # 首先计算整体IC值来决定因子排序方向
            overall_factor = data[factor_name].dropna()
            overall_return = data[target_name].dropna()
            
            # 找到两者的交集
            common_index = overall_factor.index.intersection(overall_return.index)
            if len(common_index) < 10:  # 至少需要10个样本来计算可靠的IC
                print(f"    横截面R2: 0.0000 (样本数不足)")
                return 0.0
            
            factor_for_ic = overall_factor.loc[common_index]
            return_for_ic = overall_return.loc[common_index]
            
            # 计算整体IC值
            overall_ic = factor_for_ic.corr(return_for_ic)
            
            # 根据IC值的正负决定因子排序方向
            # 如果IC为负，因子值小的应该有更高的预期收益，所以用降序排序(ascending=False)
            # 如果IC为正，因子值大的应该有更高的预期收益，所以用升序排序(ascending=True)
            ascending_order = overall_ic > 0
            
            print(f"    整体IC: {overall_ic:.4f}, 因子排序方向: {'升序' if ascending_order else '降序'}")
            
            daily_r2_list = []
            
            # 按日期分组计算每日R²
            for date, group in data.groupby('trade_date'):
                if len(group) < 2:  # 至少需要2个样本才能计算R²
                    continue
                
                # 获取因子值和收益率
                factor_values = group[factor_name]
                return_values = group[target_name]
                
                # 检查是否有有效数据
                if factor_values.isna().all() or return_values.isna().all():
                    continue
                
                # 删除缺失值
                valid_mask = ~(factor_values.isna() | return_values.isna())
                if valid_mask.sum() < 2:
                    continue
                
                factor_clean = factor_values[valid_mask]
                return_clean = return_values[valid_mask]
                
                # 根据IC值正负决定因子排序方向
                factor_ranks = factor_clean.rank(pct=True, ascending=ascending_order)
                # 计算收益排序（按百分比）
                return_ranks = return_clean.rank(pct=True)
                
                # 计算当日横截面R²
                try:
                    daily_r2 = r2_score(return_ranks, factor_ranks)
                    if not np.isnan(daily_r2) and not np.isinf(daily_r2):
                        daily_r2_list.append(daily_r2)
                except Exception:
                    # 如果计算失败（比如方差为0），跳过这一天
                    continue
            
            # 返回所有日期R²的均值
            if daily_r2_list:
                cross_sectional_r2 = np.mean(daily_r2_list)
                print(f"    横截面R2: {cross_sectional_r2:.4f} (计算了{len(daily_r2_list)}个交易日)")
                return cross_sectional_r2
            else:
                print(f"    横截面R2: 0.0000 (无法计算)")
                return 0.0
                
        except Exception as e:
            print(f"    横截面R2计算失败: {e}")
            return 0.0
    
    def analyze_all_factors(self, start_date: str = '2020-01-01', 
                          end_date: str = '2024-12-31', forward_days: int = 10) -> Dict:
        """
        分析所有因子
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            forward_days: 前瞻天数
            
        Returns:
            所有因子的分析结果
        """
        print(f"\n开始全因子分析")
        print(f"分析期间: {start_date} 到 {end_date}")
        print(f"前瞻天数: {forward_days}天")
        
        # 加载数据
        print("\n加载因子数据...")
        try:
            factor_data = self.load_factor_data(start_date, end_date)
            print(f"  因子数据: {len(factor_data):,} 条记录")
        except Exception as e:
            print(f"  加载因子数据失败: {e}")
            return {'error': f'加载因子数据失败: {e}'}
        
        print("\n加载收益率数据...")
        try:
            return_data = self.load_return_data(start_date, end_date, forward_days)
            print(f"  收益率数据: {len(return_data):,} 条记录")
        except Exception as e:
            print(f"  加载收益率数据失败: {e}")
            return {'error': f'加载收益率数据失败: {e}'}
        
        # 获取因子列表
        factor_list = self.get_factor_list()
        print(f"\n待分析因子: {len(factor_list)} 个")
        
        # 分析每个因子
        results = {}
        for i, factor_name in enumerate(factor_list, 1):
            print(f"\n[{i}/{len(factor_list)}] 分析因子: {factor_name}")
            result = self.analyze_single_factor(factor_name, factor_data, return_data, forward_days)
            results[factor_name] = result
        
        self.results = results
        return results
    
    def generate_report(self, output_file: str = 'factor_analysis_report.md') -> str:
        """
        生成分析报告
        
        Args:
            output_file: 输出文件名
            
        Returns:
            报告文件路径
        """
        if not self.results:
            raise ValueError("请先运行analyze_all_factors()")
        
        # 创建报告内容
        report_lines = []
        report_lines.append("# 可转债因子分析报告")
        report_lines.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"\n分析因子数量: {len(self.results)}")
        
        # 统计概览
        successful_analyses = [r for r in self.results.values() if 'error' not in r]
        failed_analyses = [r for r in self.results.values() if 'error' in r]
        
        report_lines.append(f"\n## 分析概览")
        report_lines.append(f"\n- 成功分析: {len(successful_analyses)} 个因子")
        report_lines.append(f"- 分析失败: {len(failed_analyses)} 个因子")
        
        if successful_analyses:
            # IC值排序
            ic_ranking = sorted(successful_analyses, key=lambda x: abs(x.get('mean_ic', 0)), reverse=True)
            
            report_lines.append(f"\n## IC值排名 (Top 10)")
            report_lines.append(f"\n| 排名 | 因子名称 | 平均IC | IC标准差 | IC_IR | 多空年化收益 | 横截面R² |")
            report_lines.append(f"| --- | --- | --- | --- | --- | --- | --- |")
            
            for i, result in enumerate(ic_ranking[:10], 1):
                factor_name = result['factor_name']
                mean_ic = result.get('mean_ic', np.nan)
                ic_std = result.get('ic_std', np.nan)
                ic_ir = result.get('ic_ir', np.nan)
                long_short = result.get('long_short_return', np.nan)
                cross_r2 = result.get('cross_sectional_r2', np.nan)
                
                report_lines.append(
                    f"| {i} | {factor_name} | {mean_ic:.4f} | {ic_std:.4f} | {ic_ir:.4f} | {long_short:.4f} | {cross_r2:.4f} |"
                )
            
            # 多空年化收益排序
            long_short_ranking = sorted(successful_analyses, key=lambda x: abs(x.get('long_short_return', 0)), reverse=True)
            
            report_lines.append(f"\n## 多空年化收益排名 (Top 10)")
            report_lines.append(f"\n| 排名 | 因子名称 | 多空年化收益 | 平均IC | IC_IR | 第1层收益 | 第5层收益 |")
            report_lines.append(f"| --- | --- | --- | --- | --- | --- | --- |")
            
            for i, result in enumerate(long_short_ranking[:10], 1):
                factor_name = result['factor_name']
                long_short = result.get('long_short_return', np.nan)
                mean_ic = result.get('mean_ic', np.nan)
                ic_ir = result.get('ic_ir', np.nan)
                q1_return = result.get('q1_return', np.nan)
                q5_return = result.get('q5_return', np.nan)
                
                report_lines.append(
                    f"| {i} | {factor_name} | {long_short:.4f} | {mean_ic:.4f} | {ic_ir:.4f} | {q1_return:.4f} | {q5_return:.4f} |"
                )
        
        # 详细分析结果
        report_lines.append(f"\n## 详细分析结果")
        
        for factor_name, result in self.results.items():
            report_lines.append(f"\n### {factor_name}")
            
            if 'error' in result:
                report_lines.append(f"\n**分析失败**: {result['error']}")
                if 'total_count' in result:
                    report_lines.append(f"\n- 总样本数: {result['total_count']:,}")
                if 'factor_null_pct' in result:
                    report_lines.append(f"- 因子缺失率: {result['factor_null_pct']:.1f}%")
            else:
                report_lines.append(f"\n**分析成功**")
                report_lines.append(f"\n**数据质量:**")
                report_lines.append(f"- 总样本数: {result['total_count']:,}")
                report_lines.append(f"- 有效样本数: {result['valid_count']:,}")
                report_lines.append(f"- 因子缺失率: {result['factor_null_pct']:.1f}%")
                report_lines.append(f"- 收益缺失率: {result['return_null_pct']:.1f}%")
                
                report_lines.append(f"\n**预测能力:**")
                report_lines.append(f"- 平均IC: {result['mean_ic']:.4f}")
                report_lines.append(f"- IC标准差: {result['ic_std']:.4f}")
                report_lines.append(f"- IC信息比率: {result['ic_ir']:.4f}")
                report_lines.append(f"- 横截面R²: {result.get('cross_sectional_r2', 0.0):.4f}")
                
                report_lines.append(f"\n**分层年化收益:**")
                report_lines.append(f"- 第1层年化收益: {result['q1_return']:.4f}")
                report_lines.append(f"- 第5层年化收益: {result['q5_return']:.4f}")
                report_lines.append(f"- 多空年化收益: {result['long_short_return']:.4f}")
                
                report_lines.append(f"\n**因子分布:**")
                report_lines.append(f"- 均值: {result['factor_mean']:.4f}")
                report_lines.append(f"- 标准差: {result['factor_std']:.4f}")
                report_lines.append(f"- 最小值: {result['factor_min']:.4f}")
                report_lines.append(f"- 最大值: {result['factor_max']:.4f}")
                report_lines.append(f"- 异常值: {result['outliers']:,} ({result['outlier_pct']:.1f}%)")
                
                # 评估和建议
                report_lines.append(f"\n**评估和建议:**")
                
                # IC评估
                mean_ic = abs(result['mean_ic'])
                if mean_ic > 0.05:
                    ic_assessment = "优秀 (|IC| > 0.05)"
                elif mean_ic > 0.03:
                    ic_assessment = "良好 (|IC| > 0.03)"
                elif mean_ic > 0.01:
                    ic_assessment = "一般 (|IC| > 0.01)"
                else:
                    ic_assessment = "较差 (|IC| ≤ 0.01)"
                
                report_lines.append(f"- IC评估: {ic_assessment}")
                
                # 异常值评估
                if result['outlier_pct'] > 10:
                    outlier_assessment = "异常值较多，建议进行异常值处理"
                elif result['outlier_pct'] > 5:
                    outlier_assessment = "异常值适中，可考虑异常值处理"
                else:
                    outlier_assessment = "异常值较少，数据质量良好"
                
                report_lines.append(f"- 异常值评估: {outlier_assessment}")
                
                # 缺失值评估
                if result['factor_null_pct'] > 50:
                    missing_assessment = "缺失率过高，建议检查数据源或填充策略"
                elif result['factor_null_pct'] > 20:
                    missing_assessment = "缺失率较高，建议改进填充策略"
                elif result['factor_null_pct'] > 5:
                    missing_assessment = "缺失率适中，当前填充策略可接受"
                else:
                    missing_assessment = "缺失率很低，数据质量优秀"
                
                report_lines.append(f"- 缺失值评估: {missing_assessment}")
        
        # 失败因子分析
        if failed_analyses:
            report_lines.append(f"\n## 失败因子分析")
            
            error_types = {}
            for result in failed_analyses:
                error = result.get('error', '未知错误')
                if error not in error_types:
                    error_types[error] = []
                error_types[error].append(result['factor_name'])
            
            for error, factors in error_types.items():
                report_lines.append(f"\n### {error}")
                report_lines.append(f"\n影响因子: {', '.join(factors)}")
        
        # 总结和建议
        report_lines.append(f"\n## 总结和建议")
        
        if successful_analyses:
            # 找出最佳因子
            best_ic_factor = max(successful_analyses, key=lambda x: abs(x.get('mean_ic', 0)))
            best_return_factor = max(successful_analyses, key=lambda x: abs(x.get('long_short_return', 0)))
            
            report_lines.append(f"\n### 优秀因子")
            report_lines.append(f"\n- **最佳IC因子**: {best_ic_factor['factor_name']} (IC={best_ic_factor['mean_ic']:.4f})")
            report_lines.append(f"- **最佳收益因子**: {best_return_factor['factor_name']} (多空年化收益={best_return_factor['long_short_return']:.4f})")
            
            # 数据质量问题
            high_missing_factors = [r for r in successful_analyses if r['factor_null_pct'] > 20]
            high_outlier_factors = [r for r in successful_analyses if r['outlier_pct'] > 10]
            
            if high_missing_factors or high_outlier_factors:
                report_lines.append(f"\n### 数据质量问题")
                
                if high_missing_factors:
                    factor_names = [r['factor_name'] for r in high_missing_factors]
                    report_lines.append(f"\n- **高缺失率因子** ({len(high_missing_factors)}个): {', '.join(factor_names)}")
                    report_lines.append(f"  建议: 检查数据源，改进数据收集或填充策略")
                
                if high_outlier_factors:
                    factor_names = [r['factor_name'] for r in high_outlier_factors]
                    report_lines.append(f"\n- **高异常值因子** ({len(high_outlier_factors)}个): {', '.join(factor_names)}")
                    report_lines.append(f"  建议: 实施异常值检测和处理，如winsorize或robust scaling")
        
        report_lines.append(f"\n### 整体建议")
        report_lines.append(f"\n1. **因子选择**: 优先使用IC绝对值大于0.03且多空年化收益显著的因子")
        report_lines.append(f"2. **数据预处理**: 对高缺失率和高异常值的因子进行专门处理")
        report_lines.append(f"3. **因子组合**: 考虑将多个互补因子组合使用，提高预测稳定性")
        report_lines.append(f"4. **定期监控**: 建立因子表现监控机制，及时发现因子失效")
        
        # 写入文件
        report_content = '\n'.join(report_lines)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n分析报告已生成: {output_file}")
        return output_file
    
    def export_to_csv(self, output_file: str = 'factor_analysis_results.csv') -> str:
        """
        导出分析结果到CSV文件
        
        Args:
            output_file: 输出CSV文件名
            
        Returns:
            CSV文件路径
        """
        if not self.results:
            raise ValueError("请先运行analyze_all_factors()")
        
        # 准备CSV数据
        csv_data = []
        
        for factor_name, result in self.results.items():
            if 'error' in result:
                # 失败的分析
                row = {
                    '因子名称': factor_name,
                    '分析状态': '失败',
                    '错误信息': result['error'],
                    '总样本数': result.get('total_count', ''),
                    '有效样本数': '',
                    '因子缺失率(%)': result.get('factor_null_pct', ''),
                    '收益缺失率(%)': '',
                    '平均IC': '',
                    'IC标准差': '',
                    'IC信息比率': '',
                    '横截面R²': '',
                    '第1层年化收益': '',
                    '第5层年化收益': '',
                    '多空年化收益': '',
                    '因子均值': '',
                    '因子标准差': '',
                    '因子最小值': '',
                    '因子最大值': '',
                    '异常值数量': '',
                    '异常值比例(%)': ''
                }
            else:
                # 成功的分析
                row = {
                    '因子名称': factor_name,
                    '分析状态': '成功',
                    '错误信息': '',
                    '总样本数': result['total_count'],
                    '有效样本数': result['valid_count'],
                    '因子缺失率(%)': round(result['factor_null_pct'], 2),
                    '收益缺失率(%)': round(result['return_null_pct'], 2),
                    '平均IC': round(result['mean_ic'], 6),
                    'IC标准差': round(result['ic_std'], 6),
                    'IC信息比率': round(result['ic_ir'], 6),
                    '横截面R²': round(result.get('cross_sectional_r2', 0.0), 6),
                    '第1层年化收益': round(result['q1_return'], 6),
                    '第5层年化收益': round(result['q5_return'], 6),
                    '多空年化收益': round(result['long_short_return'], 6),
                    '因子均值': round(result['factor_mean'], 6),
                    '因子标准差': round(result['factor_std'], 6),
                    '因子最小值': round(result['factor_min'], 6),
                    '因子最大值': round(result['factor_max'], 6),
                    '异常值数量': result['outliers'],
                    '异常值比例(%)': round(result['outlier_pct'], 2)
                }
            
            csv_data.append(row)
        
        # 转换为DataFrame并保存
        df = pd.DataFrame(csv_data)
        
        # 按多空年化收益绝对值排序（成功的分析排在前面）
        def sort_key(row):
            if row['分析状态'] == '失败':
                return (1, 0)  # 失败的排在后面
            else:
                try:
                    long_short_val = float(row['多空年化收益']) if row['多空年化收益'] != '' else 0.0
                    return (0, -abs(long_short_val))  # 成功的排在前面，按多空收益绝对值降序
                except (ValueError, TypeError):
                    return (0, 0)
        
        df['排序键'] = df.apply(sort_key, axis=1)
        df = df.sort_values('排序键').drop('排序键', axis=1)
        
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n分析结果已导出到CSV: {output_file}")
        print(f"包含 {len(df)} 个因子的详细分析结果")
        
        return output_file

def main():
    """
    主函数
    """
    analyzer = FeatureAnalyzer()
    
    # 分析所有因子
    results = analyzer.analyze_all_factors(
        start_date='2020-01-01',
        end_date='2024-12-31',
        forward_days=10
    )
    
    # 生成报告
    report_file = analyzer.generate_report('factor_analysis_report.md')
    
    # 导出CSV文件
    csv_file = analyzer.export_to_csv('factor_analysis_results.csv')
    
    print(f"\n因子分析完成！")
    print(f"分析了 {len(results)} 个因子")
    print(f"报告文件: {report_file}")
    print(f"CSV文件: {csv_file}")

if __name__ == '__main__':
    main()